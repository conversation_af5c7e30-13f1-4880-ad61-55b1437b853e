{"ast": null, "code": "/**\n * @remix-run/router v1.7.2\n *\n * Copyright (c) Remix Software Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE.md file in the root directory of this source tree.\n *\n * @license MIT\n */\nfunction _extends() {\n  _extends = Object.assign ? Object.assign.bind() : function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n    return target;\n  };\n  return _extends.apply(this, arguments);\n}\n\n////////////////////////////////////////////////////////////////////////////////\n//#region Types and Constants\n////////////////////////////////////////////////////////////////////////////////\n/**\n * Actions represent the type of change to a location value.\n */\nvar Action;\n(function (Action) {\n  /**\n   * A POP indicates a change to an arbitrary index in the history stack, such\n   * as a back or forward navigation. It does not describe the direction of the\n   * navigation, only that the current index changed.\n   *\n   * Note: This is the default action for newly created history objects.\n   */\n  Action[\"Pop\"] = \"POP\";\n  /**\n   * A PUSH indicates a new entry being added to the history stack, such as when\n   * a link is clicked and a new page loads. When this happens, all subsequent\n   * entries in the stack are lost.\n   */\n  Action[\"Push\"] = \"PUSH\";\n  /**\n   * A REPLACE indicates the entry at the current index in the history stack\n   * being replaced by a new one.\n   */\n  Action[\"Replace\"] = \"REPLACE\";\n})(Action || (Action = {}));\nconst PopStateEventType = \"popstate\";\n/**\n * Memory history stores the current location in memory. It is designed for use\n * in stateful non-browser environments like tests and React Native.\n */\nfunction createMemoryHistory(options) {\n  if (options === void 0) {\n    options = {};\n  }\n  let {\n    initialEntries = [\"/\"],\n    initialIndex,\n    v5Compat = false\n  } = options;\n  let entries; // Declare so we can access from createMemoryLocation\n  entries = initialEntries.map((entry, index) => createMemoryLocation(entry, typeof entry === \"string\" ? null : entry.state, index === 0 ? \"default\" : undefined));\n  let index = clampIndex(initialIndex == null ? entries.length - 1 : initialIndex);\n  let action = Action.Pop;\n  let listener = null;\n  function clampIndex(n) {\n    return Math.min(Math.max(n, 0), entries.length - 1);\n  }\n  function getCurrentLocation() {\n    return entries[index];\n  }\n  function createMemoryLocation(to, state, key) {\n    if (state === void 0) {\n      state = null;\n    }\n    let location = createLocation(entries ? getCurrentLocation().pathname : \"/\", to, state, key);\n    warning(location.pathname.charAt(0) === \"/\", \"relative pathnames are not supported in memory history: \" + JSON.stringify(to));\n    return location;\n  }\n  function createHref(to) {\n    return typeof to === \"string\" ? to : createPath(to);\n  }\n  let history = {\n    get index() {\n      return index;\n    },\n    get action() {\n      return action;\n    },\n    get location() {\n      return getCurrentLocation();\n    },\n    createHref,\n    createURL(to) {\n      return new URL(createHref(to), \"http://localhost\");\n    },\n    encodeLocation(to) {\n      let path = typeof to === \"string\" ? parsePath(to) : to;\n      return {\n        pathname: path.pathname || \"\",\n        search: path.search || \"\",\n        hash: path.hash || \"\"\n      };\n    },\n    push(to, state) {\n      action = Action.Push;\n      let nextLocation = createMemoryLocation(to, state);\n      index += 1;\n      entries.splice(index, entries.length, nextLocation);\n      if (v5Compat && listener) {\n        listener({\n          action,\n          location: nextLocation,\n          delta: 1\n        });\n      }\n    },\n    replace(to, state) {\n      action = Action.Replace;\n      let nextLocation = createMemoryLocation(to, state);\n      entries[index] = nextLocation;\n      if (v5Compat && listener) {\n        listener({\n          action,\n          location: nextLocation,\n          delta: 0\n        });\n      }\n    },\n    go(delta) {\n      action = Action.Pop;\n      let nextIndex = clampIndex(index + delta);\n      let nextLocation = entries[nextIndex];\n      index = nextIndex;\n      if (listener) {\n        listener({\n          action,\n          location: nextLocation,\n          delta\n        });\n      }\n    },\n    listen(fn) {\n      listener = fn;\n      return () => {\n        listener = null;\n      };\n    }\n  };\n  return history;\n}\n/**\n * Browser history stores the location in regular URLs. This is the standard for\n * most web apps, but it requires some configuration on the server to ensure you\n * serve the same app at multiple URLs.\n *\n * @see https://github.com/remix-run/history/tree/main/docs/api-reference.md#createbrowserhistory\n */\nfunction createBrowserHistory(options) {\n  if (options === void 0) {\n    options = {};\n  }\n  function createBrowserLocation(window, globalHistory) {\n    let {\n      pathname,\n      search,\n      hash\n    } = window.location;\n    return createLocation(\"\", {\n      pathname,\n      search,\n      hash\n    },\n    // state defaults to `null` because `window.history.state` does\n    globalHistory.state && globalHistory.state.usr || null, globalHistory.state && globalHistory.state.key || \"default\");\n  }\n  function createBrowserHref(window, to) {\n    return typeof to === \"string\" ? to : createPath(to);\n  }\n  return getUrlBasedHistory(createBrowserLocation, createBrowserHref, null, options);\n}\n/**\n * Hash history stores the location in window.location.hash. This makes it ideal\n * for situations where you don't want to send the location to the server for\n * some reason, either because you do cannot configure it or the URL space is\n * reserved for something else.\n *\n * @see https://github.com/remix-run/history/tree/main/docs/api-reference.md#createhashhistory\n */\nfunction createHashHistory(options) {\n  if (options === void 0) {\n    options = {};\n  }\n  function createHashLocation(window, globalHistory) {\n    let {\n      pathname = \"/\",\n      search = \"\",\n      hash = \"\"\n    } = parsePath(window.location.hash.substr(1));\n    return createLocation(\"\", {\n      pathname,\n      search,\n      hash\n    },\n    // state defaults to `null` because `window.history.state` does\n    globalHistory.state && globalHistory.state.usr || null, globalHistory.state && globalHistory.state.key || \"default\");\n  }\n  function createHashHref(window, to) {\n    let base = window.document.querySelector(\"base\");\n    let href = \"\";\n    if (base && base.getAttribute(\"href\")) {\n      let url = window.location.href;\n      let hashIndex = url.indexOf(\"#\");\n      href = hashIndex === -1 ? url : url.slice(0, hashIndex);\n    }\n    return href + \"#\" + (typeof to === \"string\" ? to : createPath(to));\n  }\n  function validateHashLocation(location, to) {\n    warning(location.pathname.charAt(0) === \"/\", \"relative pathnames are not supported in hash history.push(\" + JSON.stringify(to) + \")\");\n  }\n  return getUrlBasedHistory(createHashLocation, createHashHref, validateHashLocation, options);\n}\nfunction invariant(value, message) {\n  if (value === false || value === null || typeof value === \"undefined\") {\n    throw new Error(message);\n  }\n}\nfunction warning(cond, message) {\n  if (!cond) {\n    // eslint-disable-next-line no-console\n    if (typeof console !== \"undefined\") console.warn(message);\n    try {\n      // Welcome to debugging history!\n      //\n      // This error is thrown as a convenience so you can more easily\n      // find the source for a warning that appears in the console by\n      // enabling \"pause on exceptions\" in your JavaScript debugger.\n      throw new Error(message);\n      // eslint-disable-next-line no-empty\n    } catch (e) {}\n  }\n}\nfunction createKey() {\n  return Math.random().toString(36).substr(2, 8);\n}\n/**\n * For browser-based histories, we combine the state and key into an object\n */\nfunction getHistoryState(location, index) {\n  return {\n    usr: location.state,\n    key: location.key,\n    idx: index\n  };\n}\n/**\n * Creates a Location object with a unique key from the given Path\n */\nfunction createLocation(current, to, state, key) {\n  if (state === void 0) {\n    state = null;\n  }\n  let location = _extends({\n    pathname: typeof current === \"string\" ? current : current.pathname,\n    search: \"\",\n    hash: \"\"\n  }, typeof to === \"string\" ? parsePath(to) : to, {\n    state,\n    // TODO: This could be cleaned up.  push/replace should probably just take\n    // full Locations now and avoid the need to run through this flow at all\n    // But that's a pretty big refactor to the current test suite so going to\n    // keep as is for the time being and just let any incoming keys take precedence\n    key: to && to.key || key || createKey()\n  });\n  return location;\n}\n/**\n * Creates a string URL path from the given pathname, search, and hash components.\n */\nfunction createPath(_ref) {\n  let {\n    pathname = \"/\",\n    search = \"\",\n    hash = \"\"\n  } = _ref;\n  if (search && search !== \"?\") pathname += search.charAt(0) === \"?\" ? search : \"?\" + search;\n  if (hash && hash !== \"#\") pathname += hash.charAt(0) === \"#\" ? hash : \"#\" + hash;\n  return pathname;\n}\n/**\n * Parses a string URL path into its separate pathname, search, and hash components.\n */\nfunction parsePath(path) {\n  let parsedPath = {};\n  if (path) {\n    let hashIndex = path.indexOf(\"#\");\n    if (hashIndex >= 0) {\n      parsedPath.hash = path.substr(hashIndex);\n      path = path.substr(0, hashIndex);\n    }\n    let searchIndex = path.indexOf(\"?\");\n    if (searchIndex >= 0) {\n      parsedPath.search = path.substr(searchIndex);\n      path = path.substr(0, searchIndex);\n    }\n    if (path) {\n      parsedPath.pathname = path;\n    }\n  }\n  return parsedPath;\n}\nfunction getUrlBasedHistory(getLocation, createHref, validateLocation, options) {\n  if (options === void 0) {\n    options = {};\n  }\n  let {\n    window = document.defaultView,\n    v5Compat = false\n  } = options;\n  let globalHistory = window.history;\n  let action = Action.Pop;\n  let listener = null;\n  let index = getIndex();\n  // Index should only be null when we initialize. If not, it's because the\n  // user called history.pushState or history.replaceState directly, in which\n  // case we should log a warning as it will result in bugs.\n  if (index == null) {\n    index = 0;\n    globalHistory.replaceState(_extends({}, globalHistory.state, {\n      idx: index\n    }), \"\");\n  }\n  function getIndex() {\n    let state = globalHistory.state || {\n      idx: null\n    };\n    return state.idx;\n  }\n  function handlePop() {\n    action = Action.Pop;\n    let nextIndex = getIndex();\n    let delta = nextIndex == null ? null : nextIndex - index;\n    index = nextIndex;\n    if (listener) {\n      listener({\n        action,\n        location: history.location,\n        delta\n      });\n    }\n  }\n  function push(to, state) {\n    action = Action.Push;\n    let location = createLocation(history.location, to, state);\n    if (validateLocation) validateLocation(location, to);\n    index = getIndex() + 1;\n    let historyState = getHistoryState(location, index);\n    let url = history.createHref(location);\n    // try...catch because iOS limits us to 100 pushState calls :/\n    try {\n      globalHistory.pushState(historyState, \"\", url);\n    } catch (error) {\n      // If the exception is because `state` can't be serialized, let that throw\n      // outwards just like a replace call would so the dev knows the cause\n      // https://html.spec.whatwg.org/multipage/nav-history-apis.html#shared-history-push/replace-state-steps\n      // https://html.spec.whatwg.org/multipage/structured-data.html#structuredserializeinternal\n      if (error instanceof DOMException && error.name === \"DataCloneError\") {\n        throw error;\n      }\n      // They are going to lose state here, but there is no real\n      // way to warn them about it since the page will refresh...\n      window.location.assign(url);\n    }\n    if (v5Compat && listener) {\n      listener({\n        action,\n        location: history.location,\n        delta: 1\n      });\n    }\n  }\n  function replace(to, state) {\n    action = Action.Replace;\n    let location = createLocation(history.location, to, state);\n    if (validateLocation) validateLocation(location, to);\n    index = getIndex();\n    let historyState = getHistoryState(location, index);\n    let url = history.createHref(location);\n    globalHistory.replaceState(historyState, \"\", url);\n    if (v5Compat && listener) {\n      listener({\n        action,\n        location: history.location,\n        delta: 0\n      });\n    }\n  }\n  function createURL(to) {\n    // window.location.origin is \"null\" (the literal string value) in Firefox\n    // under certain conditions, notably when serving from a local HTML file\n    // See https://bugzilla.mozilla.org/show_bug.cgi?id=878297\n    let base = window.location.origin !== \"null\" ? window.location.origin : window.location.href;\n    let href = typeof to === \"string\" ? to : createPath(to);\n    invariant(base, \"No window.location.(origin|href) available to create URL for href: \" + href);\n    return new URL(href, base);\n  }\n  let history = {\n    get action() {\n      return action;\n    },\n    get location() {\n      return getLocation(window, globalHistory);\n    },\n    listen(fn) {\n      if (listener) {\n        throw new Error(\"A history only accepts one active listener\");\n      }\n      window.addEventListener(PopStateEventType, handlePop);\n      listener = fn;\n      return () => {\n        window.removeEventListener(PopStateEventType, handlePop);\n        listener = null;\n      };\n    },\n    createHref(to) {\n      return createHref(window, to);\n    },\n    createURL,\n    encodeLocation(to) {\n      // Encode a Location the same way window.location would\n      let url = createURL(to);\n      return {\n        pathname: url.pathname,\n        search: url.search,\n        hash: url.hash\n      };\n    },\n    push,\n    replace,\n    go(n) {\n      return globalHistory.go(n);\n    }\n  };\n  return history;\n}\n//#endregion\n\nvar ResultType;\n(function (ResultType) {\n  ResultType[\"data\"] = \"data\";\n  ResultType[\"deferred\"] = \"deferred\";\n  ResultType[\"redirect\"] = \"redirect\";\n  ResultType[\"error\"] = \"error\";\n})(ResultType || (ResultType = {}));\nconst immutableRouteKeys = new Set([\"lazy\", \"caseSensitive\", \"path\", \"id\", \"index\", \"children\"]);\nfunction isIndexRoute(route) {\n  return route.index === true;\n}\n// Walk the route tree generating unique IDs where necessary so we are working\n// solely with AgnosticDataRouteObject's within the Router\nfunction convertRoutesToDataRoutes(routes, mapRouteProperties, parentPath, manifest) {\n  if (parentPath === void 0) {\n    parentPath = [];\n  }\n  if (manifest === void 0) {\n    manifest = {};\n  }\n  return routes.map((route, index) => {\n    let treePath = [...parentPath, index];\n    let id = typeof route.id === \"string\" ? route.id : treePath.join(\"-\");\n    invariant(route.index !== true || !route.children, \"Cannot specify children on an index route\");\n    invariant(!manifest[id], \"Found a route id collision on id \\\"\" + id + \"\\\".  Route \" + \"id's must be globally unique within Data Router usages\");\n    if (isIndexRoute(route)) {\n      let indexRoute = _extends({}, route, mapRouteProperties(route), {\n        id\n      });\n      manifest[id] = indexRoute;\n      return indexRoute;\n    } else {\n      let pathOrLayoutRoute = _extends({}, route, mapRouteProperties(route), {\n        id,\n        children: undefined\n      });\n      manifest[id] = pathOrLayoutRoute;\n      if (route.children) {\n        pathOrLayoutRoute.children = convertRoutesToDataRoutes(route.children, mapRouteProperties, treePath, manifest);\n      }\n      return pathOrLayoutRoute;\n    }\n  });\n}\n/**\n * Matches the given routes to a location and returns the match data.\n *\n * @see https://reactrouter.com/utils/match-routes\n */\nfunction matchRoutes(routes, locationArg, basename) {\n  if (basename === void 0) {\n    basename = \"/\";\n  }\n  let location = typeof locationArg === \"string\" ? parsePath(locationArg) : locationArg;\n  let pathname = stripBasename(location.pathname || \"/\", basename);\n  if (pathname == null) {\n    return null;\n  }\n  let branches = flattenRoutes(routes);\n  rankRouteBranches(branches);\n  let matches = null;\n  for (let i = 0; matches == null && i < branches.length; ++i) {\n    matches = matchRouteBranch(branches[i],\n    // Incoming pathnames are generally encoded from either window.location\n    // or from router.navigate, but we want to match against the unencoded\n    // paths in the route definitions.  Memory router locations won't be\n    // encoded here but there also shouldn't be anything to decode so this\n    // should be a safe operation.  This avoids needing matchRoutes to be\n    // history-aware.\n    safelyDecodeURI(pathname));\n  }\n  return matches;\n}\nfunction flattenRoutes(routes, branches, parentsMeta, parentPath) {\n  if (branches === void 0) {\n    branches = [];\n  }\n  if (parentsMeta === void 0) {\n    parentsMeta = [];\n  }\n  if (parentPath === void 0) {\n    parentPath = \"\";\n  }\n  let flattenRoute = (route, index, relativePath) => {\n    let meta = {\n      relativePath: relativePath === undefined ? route.path || \"\" : relativePath,\n      caseSensitive: route.caseSensitive === true,\n      childrenIndex: index,\n      route\n    };\n    if (meta.relativePath.startsWith(\"/\")) {\n      invariant(meta.relativePath.startsWith(parentPath), \"Absolute route path \\\"\" + meta.relativePath + \"\\\" nested under path \" + (\"\\\"\" + parentPath + \"\\\" is not valid. An absolute child route path \") + \"must start with the combined path of all its parent routes.\");\n      meta.relativePath = meta.relativePath.slice(parentPath.length);\n    }\n    let path = joinPaths([parentPath, meta.relativePath]);\n    let routesMeta = parentsMeta.concat(meta);\n    // Add the children before adding this route to the array so we traverse the\n    // route tree depth-first and child routes appear before their parents in\n    // the \"flattened\" version.\n    if (route.children && route.children.length > 0) {\n      invariant(\n      // Our types know better, but runtime JS may not!\n      // @ts-expect-error\n      route.index !== true, \"Index routes must not have child routes. Please remove \" + (\"all child routes from route path \\\"\" + path + \"\\\".\"));\n      flattenRoutes(route.children, branches, routesMeta, path);\n    }\n    // Routes without a path shouldn't ever match by themselves unless they are\n    // index routes, so don't add them to the list of possible branches.\n    if (route.path == null && !route.index) {\n      return;\n    }\n    branches.push({\n      path,\n      score: computeScore(path, route.index),\n      routesMeta\n    });\n  };\n  routes.forEach((route, index) => {\n    var _route$path;\n    // coarse-grain check for optional params\n    if (route.path === \"\" || !((_route$path = route.path) != null && _route$path.includes(\"?\"))) {\n      flattenRoute(route, index);\n    } else {\n      for (let exploded of explodeOptionalSegments(route.path)) {\n        flattenRoute(route, index, exploded);\n      }\n    }\n  });\n  return branches;\n}\n/**\n * Computes all combinations of optional path segments for a given path,\n * excluding combinations that are ambiguous and of lower priority.\n *\n * For example, `/one/:two?/three/:four?/:five?` explodes to:\n * - `/one/three`\n * - `/one/:two/three`\n * - `/one/three/:four`\n * - `/one/three/:five`\n * - `/one/:two/three/:four`\n * - `/one/:two/three/:five`\n * - `/one/three/:four/:five`\n * - `/one/:two/three/:four/:five`\n */\nfunction explodeOptionalSegments(path) {\n  let segments = path.split(\"/\");\n  if (segments.length === 0) return [];\n  let [first, ...rest] = segments;\n  // Optional path segments are denoted by a trailing `?`\n  let isOptional = first.endsWith(\"?\");\n  // Compute the corresponding required segment: `foo?` -> `foo`\n  let required = first.replace(/\\?$/, \"\");\n  if (rest.length === 0) {\n    // Intepret empty string as omitting an optional segment\n    // `[\"one\", \"\", \"three\"]` corresponds to omitting `:two` from `/one/:two?/three` -> `/one/three`\n    return isOptional ? [required, \"\"] : [required];\n  }\n  let restExploded = explodeOptionalSegments(rest.join(\"/\"));\n  let result = [];\n  // All child paths with the prefix.  Do this for all children before the\n  // optional version for all children so we get consistent ordering where the\n  // parent optional aspect is preferred as required.  Otherwise, we can get\n  // child sections interspersed where deeper optional segments are higher than\n  // parent optional segments, where for example, /:two would explodes _earlier_\n  // then /:one.  By always including the parent as required _for all children_\n  // first, we avoid this issue\n  result.push(...restExploded.map(subpath => subpath === \"\" ? required : [required, subpath].join(\"/\")));\n  // Then if this is an optional value, add all child versions without\n  if (isOptional) {\n    result.push(...restExploded);\n  }\n  // for absolute paths, ensure `/` instead of empty segment\n  return result.map(exploded => path.startsWith(\"/\") && exploded === \"\" ? \"/\" : exploded);\n}\nfunction rankRouteBranches(branches) {\n  branches.sort((a, b) => a.score !== b.score ? b.score - a.score // Higher score first\n  : compareIndexes(a.routesMeta.map(meta => meta.childrenIndex), b.routesMeta.map(meta => meta.childrenIndex)));\n}\nconst paramRe = /^:\\w+$/;\nconst dynamicSegmentValue = 3;\nconst indexRouteValue = 2;\nconst emptySegmentValue = 1;\nconst staticSegmentValue = 10;\nconst splatPenalty = -2;\nconst isSplat = s => s === \"*\";\nfunction computeScore(path, index) {\n  let segments = path.split(\"/\");\n  let initialScore = segments.length;\n  if (segments.some(isSplat)) {\n    initialScore += splatPenalty;\n  }\n  if (index) {\n    initialScore += indexRouteValue;\n  }\n  return segments.filter(s => !isSplat(s)).reduce((score, segment) => score + (paramRe.test(segment) ? dynamicSegmentValue : segment === \"\" ? emptySegmentValue : staticSegmentValue), initialScore);\n}\nfunction compareIndexes(a, b) {\n  let siblings = a.length === b.length && a.slice(0, -1).every((n, i) => n === b[i]);\n  return siblings ?\n  // If two routes are siblings, we should try to match the earlier sibling\n  // first. This allows people to have fine-grained control over the matching\n  // behavior by simply putting routes with identical paths in the order they\n  // want them tried.\n  a[a.length - 1] - b[b.length - 1] :\n  // Otherwise, it doesn't really make sense to rank non-siblings by index,\n  // so they sort equally.\n  0;\n}\nfunction matchRouteBranch(branch, pathname) {\n  let {\n    routesMeta\n  } = branch;\n  let matchedParams = {};\n  let matchedPathname = \"/\";\n  let matches = [];\n  for (let i = 0; i < routesMeta.length; ++i) {\n    let meta = routesMeta[i];\n    let end = i === routesMeta.length - 1;\n    let remainingPathname = matchedPathname === \"/\" ? pathname : pathname.slice(matchedPathname.length) || \"/\";\n    let match = matchPath({\n      path: meta.relativePath,\n      caseSensitive: meta.caseSensitive,\n      end\n    }, remainingPathname);\n    if (!match) return null;\n    Object.assign(matchedParams, match.params);\n    let route = meta.route;\n    matches.push({\n      // TODO: Can this as be avoided?\n      params: matchedParams,\n      pathname: joinPaths([matchedPathname, match.pathname]),\n      pathnameBase: normalizePathname(joinPaths([matchedPathname, match.pathnameBase])),\n      route\n    });\n    if (match.pathnameBase !== \"/\") {\n      matchedPathname = joinPaths([matchedPathname, match.pathnameBase]);\n    }\n  }\n  return matches;\n}\n/**\n * Returns a path with params interpolated.\n *\n * @see https://reactrouter.com/utils/generate-path\n */\nfunction generatePath(originalPath, params) {\n  if (params === void 0) {\n    params = {};\n  }\n  let path = originalPath;\n  if (path.endsWith(\"*\") && path !== \"*\" && !path.endsWith(\"/*\")) {\n    warning(false, \"Route path \\\"\" + path + \"\\\" will be treated as if it were \" + (\"\\\"\" + path.replace(/\\*$/, \"/*\") + \"\\\" because the `*` character must \") + \"always follow a `/` in the pattern. To get rid of this warning, \" + (\"please change the route path to \\\"\" + path.replace(/\\*$/, \"/*\") + \"\\\".\"));\n    path = path.replace(/\\*$/, \"/*\");\n  }\n  // ensure `/` is added at the beginning if the path is absolute\n  const prefix = path.startsWith(\"/\") ? \"/\" : \"\";\n  const stringify = p => p == null ? \"\" : typeof p === \"string\" ? p : String(p);\n  const segments = path.split(/\\/+/).map((segment, index, array) => {\n    const isLastSegment = index === array.length - 1;\n    // only apply the splat if it's the last segment\n    if (isLastSegment && segment === \"*\") {\n      const star = \"*\";\n      // Apply the splat\n      return stringify(params[star]);\n    }\n    const keyMatch = segment.match(/^:(\\w+)(\\??)$/);\n    if (keyMatch) {\n      const [, key, optional] = keyMatch;\n      let param = params[key];\n      invariant(optional === \"?\" || param != null, \"Missing \\\":\" + key + \"\\\" param\");\n      return stringify(param);\n    }\n    // Remove any optional markers from optional static segments\n    return segment.replace(/\\?$/g, \"\");\n  })\n  // Remove empty segments\n  .filter(segment => !!segment);\n  return prefix + segments.join(\"/\");\n}\n/**\n * Performs pattern matching on a URL pathname and returns information about\n * the match.\n *\n * @see https://reactrouter.com/utils/match-path\n */\nfunction matchPath(pattern, pathname) {\n  if (typeof pattern === \"string\") {\n    pattern = {\n      path: pattern,\n      caseSensitive: false,\n      end: true\n    };\n  }\n  let [matcher, paramNames] = compilePath(pattern.path, pattern.caseSensitive, pattern.end);\n  let match = pathname.match(matcher);\n  if (!match) return null;\n  let matchedPathname = match[0];\n  let pathnameBase = matchedPathname.replace(/(.)\\/+$/, \"$1\");\n  let captureGroups = match.slice(1);\n  let params = paramNames.reduce((memo, paramName, index) => {\n    // We need to compute the pathnameBase here using the raw splat value\n    // instead of using params[\"*\"] later because it will be decoded then\n    if (paramName === \"*\") {\n      let splatValue = captureGroups[index] || \"\";\n      pathnameBase = matchedPathname.slice(0, matchedPathname.length - splatValue.length).replace(/(.)\\/+$/, \"$1\");\n    }\n    memo[paramName] = safelyDecodeURIComponent(captureGroups[index] || \"\", paramName);\n    return memo;\n  }, {});\n  return {\n    params,\n    pathname: matchedPathname,\n    pathnameBase,\n    pattern\n  };\n}\nfunction compilePath(path, caseSensitive, end) {\n  if (caseSensitive === void 0) {\n    caseSensitive = false;\n  }\n  if (end === void 0) {\n    end = true;\n  }\n  warning(path === \"*\" || !path.endsWith(\"*\") || path.endsWith(\"/*\"), \"Route path \\\"\" + path + \"\\\" will be treated as if it were \" + (\"\\\"\" + path.replace(/\\*$/, \"/*\") + \"\\\" because the `*` character must \") + \"always follow a `/` in the pattern. To get rid of this warning, \" + (\"please change the route path to \\\"\" + path.replace(/\\*$/, \"/*\") + \"\\\".\"));\n  let paramNames = [];\n  let regexpSource = \"^\" + path.replace(/\\/*\\*?$/, \"\") // Ignore trailing / and /*, we'll handle it below\n  .replace(/^\\/*/, \"/\") // Make sure it has a leading /\n  .replace(/[\\\\.*+^$?{}|()[\\]]/g, \"\\\\$&\") // Escape special regex chars\n  .replace(/\\/:(\\w+)/g, (_, paramName) => {\n    paramNames.push(paramName);\n    return \"/([^\\\\/]+)\";\n  });\n  if (path.endsWith(\"*\")) {\n    paramNames.push(\"*\");\n    regexpSource += path === \"*\" || path === \"/*\" ? \"(.*)$\" // Already matched the initial /, just match the rest\n    : \"(?:\\\\/(.+)|\\\\/*)$\"; // Don't include the / in params[\"*\"]\n  } else if (end) {\n    // When matching to the end, ignore trailing slashes\n    regexpSource += \"\\\\/*$\";\n  } else if (path !== \"\" && path !== \"/\") {\n    // If our path is non-empty and contains anything beyond an initial slash,\n    // then we have _some_ form of path in our regex so we should expect to\n    // match only if we find the end of this path segment.  Look for an optional\n    // non-captured trailing slash (to match a portion of the URL) or the end\n    // of the path (if we've matched to the end).  We used to do this with a\n    // word boundary but that gives false positives on routes like\n    // /user-preferences since `-` counts as a word boundary.\n    regexpSource += \"(?:(?=\\\\/|$))\";\n  } else ;\n  let matcher = new RegExp(regexpSource, caseSensitive ? undefined : \"i\");\n  return [matcher, paramNames];\n}\nfunction safelyDecodeURI(value) {\n  try {\n    return decodeURI(value);\n  } catch (error) {\n    warning(false, \"The URL path \\\"\" + value + \"\\\" could not be decoded because it is is a \" + \"malformed URL segment. This is probably due to a bad percent \" + (\"encoding (\" + error + \").\"));\n    return value;\n  }\n}\nfunction safelyDecodeURIComponent(value, paramName) {\n  try {\n    return decodeURIComponent(value);\n  } catch (error) {\n    warning(false, \"The value for the URL param \\\"\" + paramName + \"\\\" will not be decoded because\" + (\" the string \\\"\" + value + \"\\\" is a malformed URL segment. This is probably\") + (\" due to a bad percent encoding (\" + error + \").\"));\n    return value;\n  }\n}\n/**\n * @private\n */\nfunction stripBasename(pathname, basename) {\n  if (basename === \"/\") return pathname;\n  if (!pathname.toLowerCase().startsWith(basename.toLowerCase())) {\n    return null;\n  }\n  // We want to leave trailing slash behavior in the user's control, so if they\n  // specify a basename with a trailing slash, we should support it\n  let startIndex = basename.endsWith(\"/\") ? basename.length - 1 : basename.length;\n  let nextChar = pathname.charAt(startIndex);\n  if (nextChar && nextChar !== \"/\") {\n    // pathname does not start with basename/\n    return null;\n  }\n  return pathname.slice(startIndex) || \"/\";\n}\n/**\n * Returns a resolved path object relative to the given pathname.\n *\n * @see https://reactrouter.com/utils/resolve-path\n */\nfunction resolvePath(to, fromPathname) {\n  if (fromPathname === void 0) {\n    fromPathname = \"/\";\n  }\n  let {\n    pathname: toPathname,\n    search = \"\",\n    hash = \"\"\n  } = typeof to === \"string\" ? parsePath(to) : to;\n  let pathname = toPathname ? toPathname.startsWith(\"/\") ? toPathname : resolvePathname(toPathname, fromPathname) : fromPathname;\n  return {\n    pathname,\n    search: normalizeSearch(search),\n    hash: normalizeHash(hash)\n  };\n}\nfunction resolvePathname(relativePath, fromPathname) {\n  let segments = fromPathname.replace(/\\/+$/, \"\").split(\"/\");\n  let relativeSegments = relativePath.split(\"/\");\n  relativeSegments.forEach(segment => {\n    if (segment === \"..\") {\n      // Keep the root \"\" segment so the pathname starts at /\n      if (segments.length > 1) segments.pop();\n    } else if (segment !== \".\") {\n      segments.push(segment);\n    }\n  });\n  return segments.length > 1 ? segments.join(\"/\") : \"/\";\n}\nfunction getInvalidPathError(char, field, dest, path) {\n  return \"Cannot include a '\" + char + \"' character in a manually specified \" + (\"`to.\" + field + \"` field [\" + JSON.stringify(path) + \"].  Please separate it out to the \") + (\"`to.\" + dest + \"` field. Alternatively you may provide the full path as \") + \"a string in <Link to=\\\"...\\\"> and the router will parse it for you.\";\n}\n/**\n * @private\n *\n * When processing relative navigation we want to ignore ancestor routes that\n * do not contribute to the path, such that index/pathless layout routes don't\n * interfere.\n *\n * For example, when moving a route element into an index route and/or a\n * pathless layout route, relative link behavior contained within should stay\n * the same.  Both of the following examples should link back to the root:\n *\n *   <Route path=\"/\">\n *     <Route path=\"accounts\" element={<Link to=\"..\"}>\n *   </Route>\n *\n *   <Route path=\"/\">\n *     <Route path=\"accounts\">\n *       <Route element={<AccountsLayout />}>       // <-- Does not contribute\n *         <Route index element={<Link to=\"..\"} />  // <-- Does not contribute\n *       </Route\n *     </Route>\n *   </Route>\n */\nfunction getPathContributingMatches(matches) {\n  return matches.filter((match, index) => index === 0 || match.route.path && match.route.path.length > 0);\n}\n/**\n * @private\n */\nfunction resolveTo(toArg, routePathnames, locationPathname, isPathRelative) {\n  if (isPathRelative === void 0) {\n    isPathRelative = false;\n  }\n  let to;\n  if (typeof toArg === \"string\") {\n    to = parsePath(toArg);\n  } else {\n    to = _extends({}, toArg);\n    invariant(!to.pathname || !to.pathname.includes(\"?\"), getInvalidPathError(\"?\", \"pathname\", \"search\", to));\n    invariant(!to.pathname || !to.pathname.includes(\"#\"), getInvalidPathError(\"#\", \"pathname\", \"hash\", to));\n    invariant(!to.search || !to.search.includes(\"#\"), getInvalidPathError(\"#\", \"search\", \"hash\", to));\n  }\n  let isEmptyPath = toArg === \"\" || to.pathname === \"\";\n  let toPathname = isEmptyPath ? \"/\" : to.pathname;\n  let from;\n  // Routing is relative to the current pathname if explicitly requested.\n  //\n  // If a pathname is explicitly provided in `to`, it should be relative to the\n  // route context. This is explained in `Note on `<Link to>` values` in our\n  // migration guide from v5 as a means of disambiguation between `to` values\n  // that begin with `/` and those that do not. However, this is problematic for\n  // `to` values that do not provide a pathname. `to` can simply be a search or\n  // hash string, in which case we should assume that the navigation is relative\n  // to the current location's pathname and *not* the route pathname.\n  if (isPathRelative || toPathname == null) {\n    from = locationPathname;\n  } else {\n    let routePathnameIndex = routePathnames.length - 1;\n    if (toPathname.startsWith(\"..\")) {\n      let toSegments = toPathname.split(\"/\");\n      // Each leading .. segment means \"go up one route\" instead of \"go up one\n      // URL segment\".  This is a key difference from how <a href> works and a\n      // major reason we call this a \"to\" value instead of a \"href\".\n      while (toSegments[0] === \"..\") {\n        toSegments.shift();\n        routePathnameIndex -= 1;\n      }\n      to.pathname = toSegments.join(\"/\");\n    }\n    // If there are more \"..\" segments than parent routes, resolve relative to\n    // the root / URL.\n    from = routePathnameIndex >= 0 ? routePathnames[routePathnameIndex] : \"/\";\n  }\n  let path = resolvePath(to, from);\n  // Ensure the pathname has a trailing slash if the original \"to\" had one\n  let hasExplicitTrailingSlash = toPathname && toPathname !== \"/\" && toPathname.endsWith(\"/\");\n  // Or if this was a link to the current path which has a trailing slash\n  let hasCurrentTrailingSlash = (isEmptyPath || toPathname === \".\") && locationPathname.endsWith(\"/\");\n  if (!path.pathname.endsWith(\"/\") && (hasExplicitTrailingSlash || hasCurrentTrailingSlash)) {\n    path.pathname += \"/\";\n  }\n  return path;\n}\n/**\n * @private\n */\nfunction getToPathname(to) {\n  // Empty strings should be treated the same as / paths\n  return to === \"\" || to.pathname === \"\" ? \"/\" : typeof to === \"string\" ? parsePath(to).pathname : to.pathname;\n}\n/**\n * @private\n */\nconst joinPaths = paths => paths.join(\"/\").replace(/\\/\\/+/g, \"/\");\n/**\n * @private\n */\nconst normalizePathname = pathname => pathname.replace(/\\/+$/, \"\").replace(/^\\/*/, \"/\");\n/**\n * @private\n */\nconst normalizeSearch = search => !search || search === \"?\" ? \"\" : search.startsWith(\"?\") ? search : \"?\" + search;\n/**\n * @private\n */\nconst normalizeHash = hash => !hash || hash === \"#\" ? \"\" : hash.startsWith(\"#\") ? hash : \"#\" + hash;\n/**\n * This is a shortcut for creating `application/json` responses. Converts `data`\n * to JSON and sets the `Content-Type` header.\n */\nconst json = function json(data, init) {\n  if (init === void 0) {\n    init = {};\n  }\n  let responseInit = typeof init === \"number\" ? {\n    status: init\n  } : init;\n  let headers = new Headers(responseInit.headers);\n  if (!headers.has(\"Content-Type\")) {\n    headers.set(\"Content-Type\", \"application/json; charset=utf-8\");\n  }\n  return new Response(JSON.stringify(data), _extends({}, responseInit, {\n    headers\n  }));\n};\nclass AbortedDeferredError extends Error {}\nclass DeferredData {\n  constructor(data, responseInit) {\n    this.pendingKeysSet = new Set();\n    this.subscribers = new Set();\n    this.deferredKeys = [];\n    invariant(data && typeof data === \"object\" && !Array.isArray(data), \"defer() only accepts plain objects\");\n    // Set up an AbortController + Promise we can race against to exit early\n    // cancellation\n    let reject;\n    this.abortPromise = new Promise((_, r) => reject = r);\n    this.controller = new AbortController();\n    let onAbort = () => reject(new AbortedDeferredError(\"Deferred data aborted\"));\n    this.unlistenAbortSignal = () => this.controller.signal.removeEventListener(\"abort\", onAbort);\n    this.controller.signal.addEventListener(\"abort\", onAbort);\n    this.data = Object.entries(data).reduce((acc, _ref) => {\n      let [key, value] = _ref;\n      return Object.assign(acc, {\n        [key]: this.trackPromise(key, value)\n      });\n    }, {});\n    if (this.done) {\n      // All incoming values were resolved\n      this.unlistenAbortSignal();\n    }\n    this.init = responseInit;\n  }\n  trackPromise(key, value) {\n    if (!(value instanceof Promise)) {\n      return value;\n    }\n    this.deferredKeys.push(key);\n    this.pendingKeysSet.add(key);\n    // We store a little wrapper promise that will be extended with\n    // _data/_error props upon resolve/reject\n    let promise = Promise.race([value, this.abortPromise]).then(data => this.onSettle(promise, key, undefined, data), error => this.onSettle(promise, key, error));\n    // Register rejection listeners to avoid uncaught promise rejections on\n    // errors or aborted deferred values\n    promise.catch(() => {});\n    Object.defineProperty(promise, \"_tracked\", {\n      get: () => true\n    });\n    return promise;\n  }\n  onSettle(promise, key, error, data) {\n    if (this.controller.signal.aborted && error instanceof AbortedDeferredError) {\n      this.unlistenAbortSignal();\n      Object.defineProperty(promise, \"_error\", {\n        get: () => error\n      });\n      return Promise.reject(error);\n    }\n    this.pendingKeysSet.delete(key);\n    if (this.done) {\n      // Nothing left to abort!\n      this.unlistenAbortSignal();\n    }\n    // If the promise was resolved/rejected with undefined, we'll throw an error as you\n    // should always resolve with a value or null\n    if (error === undefined && data === undefined) {\n      let undefinedError = new Error(\"Deferred data for key \\\"\" + key + \"\\\" resolved/rejected with `undefined`, \" + \"you must resolve/reject with a value or `null`.\");\n      Object.defineProperty(promise, \"_error\", {\n        get: () => undefinedError\n      });\n      this.emit(false, key);\n      return Promise.reject(undefinedError);\n    }\n    if (data === undefined) {\n      Object.defineProperty(promise, \"_error\", {\n        get: () => error\n      });\n      this.emit(false, key);\n      return Promise.reject(error);\n    }\n    Object.defineProperty(promise, \"_data\", {\n      get: () => data\n    });\n    this.emit(false, key);\n    return data;\n  }\n  emit(aborted, settledKey) {\n    this.subscribers.forEach(subscriber => subscriber(aborted, settledKey));\n  }\n  subscribe(fn) {\n    this.subscribers.add(fn);\n    return () => this.subscribers.delete(fn);\n  }\n  cancel() {\n    this.controller.abort();\n    this.pendingKeysSet.forEach((v, k) => this.pendingKeysSet.delete(k));\n    this.emit(true);\n  }\n  async resolveData(signal) {\n    let aborted = false;\n    if (!this.done) {\n      let onAbort = () => this.cancel();\n      signal.addEventListener(\"abort\", onAbort);\n      aborted = await new Promise(resolve => {\n        this.subscribe(aborted => {\n          signal.removeEventListener(\"abort\", onAbort);\n          if (aborted || this.done) {\n            resolve(aborted);\n          }\n        });\n      });\n    }\n    return aborted;\n  }\n  get done() {\n    return this.pendingKeysSet.size === 0;\n  }\n  get unwrappedData() {\n    invariant(this.data !== null && this.done, \"Can only unwrap data on initialized and settled deferreds\");\n    return Object.entries(this.data).reduce((acc, _ref2) => {\n      let [key, value] = _ref2;\n      return Object.assign(acc, {\n        [key]: unwrapTrackedPromise(value)\n      });\n    }, {});\n  }\n  get pendingKeys() {\n    return Array.from(this.pendingKeysSet);\n  }\n}\nfunction isTrackedPromise(value) {\n  return value instanceof Promise && value._tracked === true;\n}\nfunction unwrapTrackedPromise(value) {\n  if (!isTrackedPromise(value)) {\n    return value;\n  }\n  if (value._error) {\n    throw value._error;\n  }\n  return value._data;\n}\nconst defer = function defer(data, init) {\n  if (init === void 0) {\n    init = {};\n  }\n  let responseInit = typeof init === \"number\" ? {\n    status: init\n  } : init;\n  return new DeferredData(data, responseInit);\n};\n/**\n * A redirect response. Sets the status code and the `Location` header.\n * Defaults to \"302 Found\".\n */\nconst redirect = function redirect(url, init) {\n  if (init === void 0) {\n    init = 302;\n  }\n  let responseInit = init;\n  if (typeof responseInit === \"number\") {\n    responseInit = {\n      status: responseInit\n    };\n  } else if (typeof responseInit.status === \"undefined\") {\n    responseInit.status = 302;\n  }\n  let headers = new Headers(responseInit.headers);\n  headers.set(\"Location\", url);\n  return new Response(null, _extends({}, responseInit, {\n    headers\n  }));\n};\n/**\n * @private\n * Utility class we use to hold auto-unwrapped 4xx/5xx Response bodies\n */\nclass ErrorResponse {\n  constructor(status, statusText, data, internal) {\n    if (internal === void 0) {\n      internal = false;\n    }\n    this.status = status;\n    this.statusText = statusText || \"\";\n    this.internal = internal;\n    if (data instanceof Error) {\n      this.data = data.toString();\n      this.error = data;\n    } else {\n      this.data = data;\n    }\n  }\n}\n/**\n * Check if the given error is an ErrorResponse generated from a 4xx/5xx\n * Response thrown from an action/loader\n */\nfunction isRouteErrorResponse(error) {\n  return error != null && typeof error.status === \"number\" && typeof error.statusText === \"string\" && typeof error.internal === \"boolean\" && \"data\" in error;\n}\nconst validMutationMethodsArr = [\"post\", \"put\", \"patch\", \"delete\"];\nconst validMutationMethods = new Set(validMutationMethodsArr);\nconst validRequestMethodsArr = [\"get\", ...validMutationMethodsArr];\nconst validRequestMethods = new Set(validRequestMethodsArr);\nconst redirectStatusCodes = new Set([301, 302, 303, 307, 308]);\nconst redirectPreserveMethodStatusCodes = new Set([307, 308]);\nconst IDLE_NAVIGATION = {\n  state: \"idle\",\n  location: undefined,\n  formMethod: undefined,\n  formAction: undefined,\n  formEncType: undefined,\n  formData: undefined,\n  json: undefined,\n  text: undefined\n};\nconst IDLE_FETCHER = {\n  state: \"idle\",\n  data: undefined,\n  formMethod: undefined,\n  formAction: undefined,\n  formEncType: undefined,\n  formData: undefined,\n  json: undefined,\n  text: undefined\n};\nconst IDLE_BLOCKER = {\n  state: \"unblocked\",\n  proceed: undefined,\n  reset: undefined,\n  location: undefined\n};\nconst ABSOLUTE_URL_REGEX = /^(?:[a-z][a-z0-9+.-]*:|\\/\\/)/i;\nconst defaultMapRouteProperties = route => ({\n  hasErrorBoundary: Boolean(route.hasErrorBoundary)\n});\n//#endregion\n////////////////////////////////////////////////////////////////////////////////\n//#region createRouter\n////////////////////////////////////////////////////////////////////////////////\n/**\n * Create a router and listen to history POP navigations\n */\nfunction createRouter(init) {\n  const routerWindow = init.window ? init.window : typeof window !== \"undefined\" ? window : undefined;\n  const isBrowser = typeof routerWindow !== \"undefined\" && typeof routerWindow.document !== \"undefined\" && typeof routerWindow.document.createElement !== \"undefined\";\n  const isServer = !isBrowser;\n  invariant(init.routes.length > 0, \"You must provide a non-empty routes array to createRouter\");\n  let mapRouteProperties;\n  if (init.mapRouteProperties) {\n    mapRouteProperties = init.mapRouteProperties;\n  } else if (init.detectErrorBoundary) {\n    // If they are still using the deprecated version, wrap it with the new API\n    let detectErrorBoundary = init.detectErrorBoundary;\n    mapRouteProperties = route => ({\n      hasErrorBoundary: detectErrorBoundary(route)\n    });\n  } else {\n    mapRouteProperties = defaultMapRouteProperties;\n  }\n  // Routes keyed by ID\n  let manifest = {};\n  // Routes in tree format for matching\n  let dataRoutes = convertRoutesToDataRoutes(init.routes, mapRouteProperties, undefined, manifest);\n  let inFlightDataRoutes;\n  let basename = init.basename || \"/\";\n  // Config driven behavior flags\n  let future = _extends({\n    v7_normalizeFormMethod: false,\n    v7_prependBasename: false\n  }, init.future);\n  // Cleanup function for history\n  let unlistenHistory = null;\n  // Externally-provided functions to call on all state changes\n  let subscribers = new Set();\n  // Externally-provided object to hold scroll restoration locations during routing\n  let savedScrollPositions = null;\n  // Externally-provided function to get scroll restoration keys\n  let getScrollRestorationKey = null;\n  // Externally-provided function to get current scroll position\n  let getScrollPosition = null;\n  // One-time flag to control the initial hydration scroll restoration.  Because\n  // we don't get the saved positions from <ScrollRestoration /> until _after_\n  // the initial render, we need to manually trigger a separate updateState to\n  // send along the restoreScrollPosition\n  // Set to true if we have `hydrationData` since we assume we were SSR'd and that\n  // SSR did the initial scroll restoration.\n  let initialScrollRestored = init.hydrationData != null;\n  let initialMatches = matchRoutes(dataRoutes, init.history.location, basename);\n  let initialErrors = null;\n  if (initialMatches == null) {\n    // If we do not match a user-provided-route, fall back to the root\n    // to allow the error boundary to take over\n    let error = getInternalRouterError(404, {\n      pathname: init.history.location.pathname\n    });\n    let {\n      matches,\n      route\n    } = getShortCircuitMatches(dataRoutes);\n    initialMatches = matches;\n    initialErrors = {\n      [route.id]: error\n    };\n  }\n  let initialized =\n  // All initialMatches need to be loaded before we're ready.  If we have lazy\n  // functions around still then we'll need to run them in initialize()\n  !initialMatches.some(m => m.route.lazy) && (\n  // And we have to either have no loaders or have been provided hydrationData\n  !initialMatches.some(m => m.route.loader) || init.hydrationData != null);\n  let router;\n  let state = {\n    historyAction: init.history.action,\n    location: init.history.location,\n    matches: initialMatches,\n    initialized,\n    navigation: IDLE_NAVIGATION,\n    // Don't restore on initial updateState() if we were SSR'd\n    restoreScrollPosition: init.hydrationData != null ? false : null,\n    preventScrollReset: false,\n    revalidation: \"idle\",\n    loaderData: init.hydrationData && init.hydrationData.loaderData || {},\n    actionData: init.hydrationData && init.hydrationData.actionData || null,\n    errors: init.hydrationData && init.hydrationData.errors || initialErrors,\n    fetchers: new Map(),\n    blockers: new Map()\n  };\n  // -- Stateful internal variables to manage navigations --\n  // Current navigation in progress (to be committed in completeNavigation)\n  let pendingAction = Action.Pop;\n  // Should the current navigation prevent the scroll reset if scroll cannot\n  // be restored?\n  let pendingPreventScrollReset = false;\n  // AbortController for the active navigation\n  let pendingNavigationController;\n  // We use this to avoid touching history in completeNavigation if a\n  // revalidation is entirely uninterrupted\n  let isUninterruptedRevalidation = false;\n  // Use this internal flag to force revalidation of all loaders:\n  //  - submissions (completed or interrupted)\n  //  - useRevalidator()\n  //  - X-Remix-Revalidate (from redirect)\n  let isRevalidationRequired = false;\n  // Use this internal array to capture routes that require revalidation due\n  // to a cancelled deferred on action submission\n  let cancelledDeferredRoutes = [];\n  // Use this internal array to capture fetcher loads that were cancelled by an\n  // action navigation and require revalidation\n  let cancelledFetcherLoads = [];\n  // AbortControllers for any in-flight fetchers\n  let fetchControllers = new Map();\n  // Track loads based on the order in which they started\n  let incrementingLoadId = 0;\n  // Track the outstanding pending navigation data load to be compared against\n  // the globally incrementing load when a fetcher load lands after a completed\n  // navigation\n  let pendingNavigationLoadId = -1;\n  // Fetchers that triggered data reloads as a result of their actions\n  let fetchReloadIds = new Map();\n  // Fetchers that triggered redirect navigations\n  let fetchRedirectIds = new Set();\n  // Most recent href/match for fetcher.load calls for fetchers\n  let fetchLoadMatches = new Map();\n  // Store DeferredData instances for active route matches.  When a\n  // route loader returns defer() we stick one in here.  Then, when a nested\n  // promise resolves we update loaderData.  If a new navigation starts we\n  // cancel active deferreds for eliminated routes.\n  let activeDeferreds = new Map();\n  // Store blocker functions in a separate Map outside of router state since\n  // we don't need to update UI state if they change\n  let blockerFunctions = new Map();\n  // Flag to ignore the next history update, so we can revert the URL change on\n  // a POP navigation that was blocked by the user without touching router state\n  let ignoreNextHistoryUpdate = false;\n  // Initialize the router, all side effects should be kicked off from here.\n  // Implemented as a Fluent API for ease of:\n  //   let router = createRouter(init).initialize();\n  function initialize() {\n    // If history informs us of a POP navigation, start the navigation but do not update\n    // state.  We'll update our own state once the navigation completes\n    unlistenHistory = init.history.listen(_ref => {\n      let {\n        action: historyAction,\n        location,\n        delta\n      } = _ref;\n      // Ignore this event if it was just us resetting the URL from a\n      // blocked POP navigation\n      if (ignoreNextHistoryUpdate) {\n        ignoreNextHistoryUpdate = false;\n        return;\n      }\n      warning(blockerFunctions.size === 0 || delta != null, \"You are trying to use a blocker on a POP navigation to a location \" + \"that was not created by @remix-run/router. This will fail silently in \" + \"production. This can happen if you are navigating outside the router \" + \"via `window.history.pushState`/`window.location.hash` instead of using \" + \"router navigation APIs.  This can also happen if you are using \" + \"createHashRouter and the user manually changes the URL.\");\n      let blockerKey = shouldBlockNavigation({\n        currentLocation: state.location,\n        nextLocation: location,\n        historyAction\n      });\n      if (blockerKey && delta != null) {\n        // Restore the URL to match the current UI, but don't update router state\n        ignoreNextHistoryUpdate = true;\n        init.history.go(delta * -1);\n        // Put the blocker into a blocked state\n        updateBlocker(blockerKey, {\n          state: \"blocked\",\n          location,\n          proceed() {\n            updateBlocker(blockerKey, {\n              state: \"proceeding\",\n              proceed: undefined,\n              reset: undefined,\n              location\n            });\n            // Re-do the same POP navigation we just blocked\n            init.history.go(delta);\n          },\n          reset() {\n            let blockers = new Map(state.blockers);\n            blockers.set(blockerKey, IDLE_BLOCKER);\n            updateState({\n              blockers\n            });\n          }\n        });\n        return;\n      }\n      return startNavigation(historyAction, location);\n    });\n    // Kick off initial data load if needed.  Use Pop to avoid modifying history\n    // Note we don't do any handling of lazy here.  For SPA's it'll get handled\n    // in the normal navigation flow.  For SSR it's expected that lazy modules are\n    // resolved prior to router creation since we can't go into a fallbackElement\n    // UI for SSR'd apps\n    if (!state.initialized) {\n      startNavigation(Action.Pop, state.location);\n    }\n    return router;\n  }\n  // Clean up a router and it's side effects\n  function dispose() {\n    if (unlistenHistory) {\n      unlistenHistory();\n    }\n    subscribers.clear();\n    pendingNavigationController && pendingNavigationController.abort();\n    state.fetchers.forEach((_, key) => deleteFetcher(key));\n    state.blockers.forEach((_, key) => deleteBlocker(key));\n  }\n  // Subscribe to state updates for the router\n  function subscribe(fn) {\n    subscribers.add(fn);\n    return () => subscribers.delete(fn);\n  }\n  // Update our state and notify the calling context of the change\n  function updateState(newState) {\n    state = _extends({}, state, newState);\n    subscribers.forEach(subscriber => subscriber(state));\n  }\n  // Complete a navigation returning the state.navigation back to the IDLE_NAVIGATION\n  // and setting state.[historyAction/location/matches] to the new route.\n  // - Location is a required param\n  // - Navigation will always be set to IDLE_NAVIGATION\n  // - Can pass any other state in newState\n  function completeNavigation(location, newState) {\n    var _location$state, _location$state2;\n    // Deduce if we're in a loading/actionReload state:\n    // - We have committed actionData in the store\n    // - The current navigation was a mutation submission\n    // - We're past the submitting state and into the loading state\n    // - The location being loaded is not the result of a redirect\n    let isActionReload = state.actionData != null && state.navigation.formMethod != null && isMutationMethod(state.navigation.formMethod) && state.navigation.state === \"loading\" && ((_location$state = location.state) == null ? void 0 : _location$state._isRedirect) !== true;\n    let actionData;\n    if (newState.actionData) {\n      if (Object.keys(newState.actionData).length > 0) {\n        actionData = newState.actionData;\n      } else {\n        // Empty actionData -> clear prior actionData due to an action error\n        actionData = null;\n      }\n    } else if (isActionReload) {\n      // Keep the current data if we're wrapping up the action reload\n      actionData = state.actionData;\n    } else {\n      // Clear actionData on any other completed navigations\n      actionData = null;\n    }\n    // Always preserve any existing loaderData from re-used routes\n    let loaderData = newState.loaderData ? mergeLoaderData(state.loaderData, newState.loaderData, newState.matches || [], newState.errors) : state.loaderData;\n    // On a successful navigation we can assume we got through all blockers\n    // so we can start fresh\n    let blockers = state.blockers;\n    if (blockers.size > 0) {\n      blockers = new Map(blockers);\n      blockers.forEach((_, k) => blockers.set(k, IDLE_BLOCKER));\n    }\n    // Always respect the user flag.  Otherwise don't reset on mutation\n    // submission navigations unless they redirect\n    let preventScrollReset = pendingPreventScrollReset === true || state.navigation.formMethod != null && isMutationMethod(state.navigation.formMethod) && ((_location$state2 = location.state) == null ? void 0 : _location$state2._isRedirect) !== true;\n    if (inFlightDataRoutes) {\n      dataRoutes = inFlightDataRoutes;\n      inFlightDataRoutes = undefined;\n    }\n    if (isUninterruptedRevalidation) ;else if (pendingAction === Action.Pop) ;else if (pendingAction === Action.Push) {\n      init.history.push(location, location.state);\n    } else if (pendingAction === Action.Replace) {\n      init.history.replace(location, location.state);\n    }\n    updateState(_extends({}, newState, {\n      actionData,\n      loaderData,\n      historyAction: pendingAction,\n      location,\n      initialized: true,\n      navigation: IDLE_NAVIGATION,\n      revalidation: \"idle\",\n      restoreScrollPosition: getSavedScrollPosition(location, newState.matches || state.matches),\n      preventScrollReset,\n      blockers\n    }));\n    // Reset stateful navigation vars\n    pendingAction = Action.Pop;\n    pendingPreventScrollReset = false;\n    isUninterruptedRevalidation = false;\n    isRevalidationRequired = false;\n    cancelledDeferredRoutes = [];\n    cancelledFetcherLoads = [];\n  }\n  // Trigger a navigation event, which can either be a numerical POP or a PUSH\n  // replace with an optional submission\n  async function navigate(to, opts) {\n    if (typeof to === \"number\") {\n      init.history.go(to);\n      return;\n    }\n    let normalizedPath = normalizeTo(state.location, state.matches, basename, future.v7_prependBasename, to, opts == null ? void 0 : opts.fromRouteId, opts == null ? void 0 : opts.relative);\n    let {\n      path,\n      submission,\n      error\n    } = normalizeNavigateOptions(future.v7_normalizeFormMethod, false, normalizedPath, opts);\n    let currentLocation = state.location;\n    let nextLocation = createLocation(state.location, path, opts && opts.state);\n    // When using navigate as a PUSH/REPLACE we aren't reading an already-encoded\n    // URL from window.location, so we need to encode it here so the behavior\n    // remains the same as POP and non-data-router usages.  new URL() does all\n    // the same encoding we'd get from a history.pushState/window.location read\n    // without having to touch history\n    nextLocation = _extends({}, nextLocation, init.history.encodeLocation(nextLocation));\n    let userReplace = opts && opts.replace != null ? opts.replace : undefined;\n    let historyAction = Action.Push;\n    if (userReplace === true) {\n      historyAction = Action.Replace;\n    } else if (userReplace === false) ;else if (submission != null && isMutationMethod(submission.formMethod) && submission.formAction === state.location.pathname + state.location.search) {\n      // By default on submissions to the current location we REPLACE so that\n      // users don't have to double-click the back button to get to the prior\n      // location.  If the user redirects to a different location from the\n      // action/loader this will be ignored and the redirect will be a PUSH\n      historyAction = Action.Replace;\n    }\n    let preventScrollReset = opts && \"preventScrollReset\" in opts ? opts.preventScrollReset === true : undefined;\n    let blockerKey = shouldBlockNavigation({\n      currentLocation,\n      nextLocation,\n      historyAction\n    });\n    if (blockerKey) {\n      // Put the blocker into a blocked state\n      updateBlocker(blockerKey, {\n        state: \"blocked\",\n        location: nextLocation,\n        proceed() {\n          updateBlocker(blockerKey, {\n            state: \"proceeding\",\n            proceed: undefined,\n            reset: undefined,\n            location: nextLocation\n          });\n          // Send the same navigation through\n          navigate(to, opts);\n        },\n        reset() {\n          let blockers = new Map(state.blockers);\n          blockers.set(blockerKey, IDLE_BLOCKER);\n          updateState({\n            blockers\n          });\n        }\n      });\n      return;\n    }\n    return await startNavigation(historyAction, nextLocation, {\n      submission,\n      // Send through the formData serialization error if we have one so we can\n      // render at the right error boundary after we match routes\n      pendingError: error,\n      preventScrollReset,\n      replace: opts && opts.replace\n    });\n  }\n  // Revalidate all current loaders.  If a navigation is in progress or if this\n  // is interrupted by a navigation, allow this to \"succeed\" by calling all\n  // loaders during the next loader round\n  function revalidate() {\n    interruptActiveLoads();\n    updateState({\n      revalidation: \"loading\"\n    });\n    // If we're currently submitting an action, we don't need to start a new\n    // navigation, we'll just let the follow up loader execution call all loaders\n    if (state.navigation.state === \"submitting\") {\n      return;\n    }\n    // If we're currently in an idle state, start a new navigation for the current\n    // action/location and mark it as uninterrupted, which will skip the history\n    // update in completeNavigation\n    if (state.navigation.state === \"idle\") {\n      startNavigation(state.historyAction, state.location, {\n        startUninterruptedRevalidation: true\n      });\n      return;\n    }\n    // Otherwise, if we're currently in a loading state, just start a new\n    // navigation to the navigation.location but do not trigger an uninterrupted\n    // revalidation so that history correctly updates once the navigation completes\n    startNavigation(pendingAction || state.historyAction, state.navigation.location, {\n      overrideNavigation: state.navigation\n    });\n  }\n  // Start a navigation to the given action/location.  Can optionally provide a\n  // overrideNavigation which will override the normalLoad in the case of a redirect\n  // navigation\n  async function startNavigation(historyAction, location, opts) {\n    // Abort any in-progress navigations and start a new one. Unset any ongoing\n    // uninterrupted revalidations unless told otherwise, since we want this\n    // new navigation to update history normally\n    pendingNavigationController && pendingNavigationController.abort();\n    pendingNavigationController = null;\n    pendingAction = historyAction;\n    isUninterruptedRevalidation = (opts && opts.startUninterruptedRevalidation) === true;\n    // Save the current scroll position every time we start a new navigation,\n    // and track whether we should reset scroll on completion\n    saveScrollPosition(state.location, state.matches);\n    pendingPreventScrollReset = (opts && opts.preventScrollReset) === true;\n    let routesToUse = inFlightDataRoutes || dataRoutes;\n    let loadingNavigation = opts && opts.overrideNavigation;\n    let matches = matchRoutes(routesToUse, location, basename);\n    // Short circuit with a 404 on the root error boundary if we match nothing\n    if (!matches) {\n      let error = getInternalRouterError(404, {\n        pathname: location.pathname\n      });\n      let {\n        matches: notFoundMatches,\n        route\n      } = getShortCircuitMatches(routesToUse);\n      // Cancel all pending deferred on 404s since we don't keep any routes\n      cancelActiveDeferreds();\n      completeNavigation(location, {\n        matches: notFoundMatches,\n        loaderData: {},\n        errors: {\n          [route.id]: error\n        }\n      });\n      return;\n    }\n    // Short circuit if it's only a hash change and not a revalidation or\n    // mutation submission.\n    //\n    // Ignore on initial page loads because since the initial load will always\n    // be \"same hash\".  For example, on /page#hash and submit a <Form method=\"post\">\n    // which will default to a navigation to /page\n    if (state.initialized && !isRevalidationRequired && isHashChangeOnly(state.location, location) && !(opts && opts.submission && isMutationMethod(opts.submission.formMethod))) {\n      completeNavigation(location, {\n        matches\n      });\n      return;\n    }\n    // Create a controller/Request for this navigation\n    pendingNavigationController = new AbortController();\n    let request = createClientSideRequest(init.history, location, pendingNavigationController.signal, opts && opts.submission);\n    let pendingActionData;\n    let pendingError;\n    if (opts && opts.pendingError) {\n      // If we have a pendingError, it means the user attempted a GET submission\n      // with binary FormData so assign here and skip to handleLoaders.  That\n      // way we handle calling loaders above the boundary etc.  It's not really\n      // different from an actionError in that sense.\n      pendingError = {\n        [findNearestBoundary(matches).route.id]: opts.pendingError\n      };\n    } else if (opts && opts.submission && isMutationMethod(opts.submission.formMethod)) {\n      // Call action if we received an action submission\n      let actionOutput = await handleAction(request, location, opts.submission, matches, {\n        replace: opts.replace\n      });\n      if (actionOutput.shortCircuited) {\n        return;\n      }\n      pendingActionData = actionOutput.pendingActionData;\n      pendingError = actionOutput.pendingActionError;\n      loadingNavigation = getLoadingNavigation(location, opts.submission);\n      // Create a GET request for the loaders\n      request = new Request(request.url, {\n        signal: request.signal\n      });\n    }\n    // Call loaders\n    let {\n      shortCircuited,\n      loaderData,\n      errors\n    } = await handleLoaders(request, location, matches, loadingNavigation, opts && opts.submission, opts && opts.fetcherSubmission, opts && opts.replace, pendingActionData, pendingError);\n    if (shortCircuited) {\n      return;\n    }\n    // Clean up now that the action/loaders have completed.  Don't clean up if\n    // we short circuited because pendingNavigationController will have already\n    // been assigned to a new controller for the next navigation\n    pendingNavigationController = null;\n    completeNavigation(location, _extends({\n      matches\n    }, pendingActionData ? {\n      actionData: pendingActionData\n    } : {}, {\n      loaderData,\n      errors\n    }));\n  }\n  // Call the action matched by the leaf route for this navigation and handle\n  // redirects/errors\n  async function handleAction(request, location, submission, matches, opts) {\n    if (opts === void 0) {\n      opts = {};\n    }\n    interruptActiveLoads();\n    // Put us in a submitting state\n    let navigation = getSubmittingNavigation(location, submission);\n    updateState({\n      navigation\n    });\n    // Call our action and get the result\n    let result;\n    let actionMatch = getTargetMatch(matches, location);\n    if (!actionMatch.route.action && !actionMatch.route.lazy) {\n      result = {\n        type: ResultType.error,\n        error: getInternalRouterError(405, {\n          method: request.method,\n          pathname: location.pathname,\n          routeId: actionMatch.route.id\n        })\n      };\n    } else {\n      result = await callLoaderOrAction(\"action\", request, actionMatch, matches, manifest, mapRouteProperties, basename);\n      if (request.signal.aborted) {\n        return {\n          shortCircuited: true\n        };\n      }\n    }\n    if (isRedirectResult(result)) {\n      let replace;\n      if (opts && opts.replace != null) {\n        replace = opts.replace;\n      } else {\n        // If the user didn't explicity indicate replace behavior, replace if\n        // we redirected to the exact same location we're currently at to avoid\n        // double back-buttons\n        replace = result.location === state.location.pathname + state.location.search;\n      }\n      await startRedirectNavigation(state, result, {\n        submission,\n        replace\n      });\n      return {\n        shortCircuited: true\n      };\n    }\n    if (isErrorResult(result)) {\n      // Store off the pending error - we use it to determine which loaders\n      // to call and will commit it when we complete the navigation\n      let boundaryMatch = findNearestBoundary(matches, actionMatch.route.id);\n      // By default, all submissions are REPLACE navigations, but if the\n      // action threw an error that'll be rendered in an errorElement, we fall\n      // back to PUSH so that the user can use the back button to get back to\n      // the pre-submission form location to try again\n      if ((opts && opts.replace) !== true) {\n        pendingAction = Action.Push;\n      }\n      return {\n        // Send back an empty object we can use to clear out any prior actionData\n        pendingActionData: {},\n        pendingActionError: {\n          [boundaryMatch.route.id]: result.error\n        }\n      };\n    }\n    if (isDeferredResult(result)) {\n      throw getInternalRouterError(400, {\n        type: \"defer-action\"\n      });\n    }\n    return {\n      pendingActionData: {\n        [actionMatch.route.id]: result.data\n      }\n    };\n  }\n  // Call all applicable loaders for the given matches, handling redirects,\n  // errors, etc.\n  async function handleLoaders(request, location, matches, overrideNavigation, submission, fetcherSubmission, replace, pendingActionData, pendingError) {\n    // Figure out the right navigation we want to use for data loading\n    let loadingNavigation = overrideNavigation || getLoadingNavigation(location, submission);\n    // If this was a redirect from an action we don't have a \"submission\" but\n    // we have it on the loading navigation so use that if available\n    let activeSubmission = submission || fetcherSubmission || getSubmissionFromNavigation(loadingNavigation);\n    let routesToUse = inFlightDataRoutes || dataRoutes;\n    let [matchesToLoad, revalidatingFetchers] = getMatchesToLoad(init.history, state, matches, activeSubmission, location, isRevalidationRequired, cancelledDeferredRoutes, cancelledFetcherLoads, fetchLoadMatches, fetchRedirectIds, routesToUse, basename, pendingActionData, pendingError);\n    // Cancel pending deferreds for no-longer-matched routes or routes we're\n    // about to reload.  Note that if this is an action reload we would have\n    // already cancelled all pending deferreds so this would be a no-op\n    cancelActiveDeferreds(routeId => !(matches && matches.some(m => m.route.id === routeId)) || matchesToLoad && matchesToLoad.some(m => m.route.id === routeId));\n    pendingNavigationLoadId = ++incrementingLoadId;\n    // Short circuit if we have no loaders to run\n    if (matchesToLoad.length === 0 && revalidatingFetchers.length === 0) {\n      let updatedFetchers = markFetchRedirectsDone();\n      completeNavigation(location, _extends({\n        matches,\n        loaderData: {},\n        // Commit pending error if we're short circuiting\n        errors: pendingError || null\n      }, pendingActionData ? {\n        actionData: pendingActionData\n      } : {}, updatedFetchers ? {\n        fetchers: new Map(state.fetchers)\n      } : {}));\n      return {\n        shortCircuited: true\n      };\n    }\n    // If this is an uninterrupted revalidation, we remain in our current idle\n    // state.  If not, we need to switch to our loading state and load data,\n    // preserving any new action data or existing action data (in the case of\n    // a revalidation interrupting an actionReload)\n    if (!isUninterruptedRevalidation) {\n      revalidatingFetchers.forEach(rf => {\n        let fetcher = state.fetchers.get(rf.key);\n        let revalidatingFetcher = getLoadingFetcher(undefined, fetcher ? fetcher.data : undefined);\n        state.fetchers.set(rf.key, revalidatingFetcher);\n      });\n      let actionData = pendingActionData || state.actionData;\n      updateState(_extends({\n        navigation: loadingNavigation\n      }, actionData ? Object.keys(actionData).length === 0 ? {\n        actionData: null\n      } : {\n        actionData\n      } : {}, revalidatingFetchers.length > 0 ? {\n        fetchers: new Map(state.fetchers)\n      } : {}));\n    }\n    revalidatingFetchers.forEach(rf => {\n      if (fetchControllers.has(rf.key)) {\n        abortFetcher(rf.key);\n      }\n      if (rf.controller) {\n        // Fetchers use an independent AbortController so that aborting a fetcher\n        // (via deleteFetcher) does not abort the triggering navigation that\n        // triggered the revalidation\n        fetchControllers.set(rf.key, rf.controller);\n      }\n    });\n    // Proxy navigation abort through to revalidation fetchers\n    let abortPendingFetchRevalidations = () => revalidatingFetchers.forEach(f => abortFetcher(f.key));\n    if (pendingNavigationController) {\n      pendingNavigationController.signal.addEventListener(\"abort\", abortPendingFetchRevalidations);\n    }\n    let {\n      results,\n      loaderResults,\n      fetcherResults\n    } = await callLoadersAndMaybeResolveData(state.matches, matches, matchesToLoad, revalidatingFetchers, request);\n    if (request.signal.aborted) {\n      return {\n        shortCircuited: true\n      };\n    }\n    // Clean up _after_ loaders have completed.  Don't clean up if we short\n    // circuited because fetchControllers would have been aborted and\n    // reassigned to new controllers for the next navigation\n    if (pendingNavigationController) {\n      pendingNavigationController.signal.removeEventListener(\"abort\", abortPendingFetchRevalidations);\n    }\n    revalidatingFetchers.forEach(rf => fetchControllers.delete(rf.key));\n    // If any loaders returned a redirect Response, start a new REPLACE navigation\n    let redirect = findRedirect(results);\n    if (redirect) {\n      if (redirect.idx >= matchesToLoad.length) {\n        // If this redirect came from a fetcher make sure we mark it in\n        // fetchRedirectIds so it doesn't get revalidated on the next set of\n        // loader executions\n        let fetcherKey = revalidatingFetchers[redirect.idx - matchesToLoad.length].key;\n        fetchRedirectIds.add(fetcherKey);\n      }\n      await startRedirectNavigation(state, redirect.result, {\n        replace\n      });\n      return {\n        shortCircuited: true\n      };\n    }\n    // Process and commit output from loaders\n    let {\n      loaderData,\n      errors\n    } = processLoaderData(state, matches, matchesToLoad, loaderResults, pendingError, revalidatingFetchers, fetcherResults, activeDeferreds);\n    // Wire up subscribers to update loaderData as promises settle\n    activeDeferreds.forEach((deferredData, routeId) => {\n      deferredData.subscribe(aborted => {\n        // Note: No need to updateState here since the TrackedPromise on\n        // loaderData is stable across resolve/reject\n        // Remove this instance if we were aborted or if promises have settled\n        if (aborted || deferredData.done) {\n          activeDeferreds.delete(routeId);\n        }\n      });\n    });\n    let updatedFetchers = markFetchRedirectsDone();\n    let didAbortFetchLoads = abortStaleFetchLoads(pendingNavigationLoadId);\n    let shouldUpdateFetchers = updatedFetchers || didAbortFetchLoads || revalidatingFetchers.length > 0;\n    return _extends({\n      loaderData,\n      errors\n    }, shouldUpdateFetchers ? {\n      fetchers: new Map(state.fetchers)\n    } : {});\n  }\n  function getFetcher(key) {\n    return state.fetchers.get(key) || IDLE_FETCHER;\n  }\n  // Trigger a fetcher load/submit for the given fetcher key\n  function fetch(key, routeId, href, opts) {\n    if (isServer) {\n      throw new Error(\"router.fetch() was called during the server render, but it shouldn't be. \" + \"You are likely calling a useFetcher() method in the body of your component. \" + \"Try moving it to a useEffect or a callback.\");\n    }\n    if (fetchControllers.has(key)) abortFetcher(key);\n    let routesToUse = inFlightDataRoutes || dataRoutes;\n    let normalizedPath = normalizeTo(state.location, state.matches, basename, future.v7_prependBasename, href, routeId, opts == null ? void 0 : opts.relative);\n    let matches = matchRoutes(routesToUse, normalizedPath, basename);\n    if (!matches) {\n      setFetcherError(key, routeId, getInternalRouterError(404, {\n        pathname: normalizedPath\n      }));\n      return;\n    }\n    let {\n      path,\n      submission,\n      error\n    } = normalizeNavigateOptions(future.v7_normalizeFormMethod, true, normalizedPath, opts);\n    if (error) {\n      setFetcherError(key, routeId, error);\n      return;\n    }\n    let match = getTargetMatch(matches, path);\n    pendingPreventScrollReset = (opts && opts.preventScrollReset) === true;\n    if (submission && isMutationMethod(submission.formMethod)) {\n      handleFetcherAction(key, routeId, path, match, matches, submission);\n      return;\n    }\n    // Store off the match so we can call it's shouldRevalidate on subsequent\n    // revalidations\n    fetchLoadMatches.set(key, {\n      routeId,\n      path\n    });\n    handleFetcherLoader(key, routeId, path, match, matches, submission);\n  }\n  // Call the action for the matched fetcher.submit(), and then handle redirects,\n  // errors, and revalidation\n  async function handleFetcherAction(key, routeId, path, match, requestMatches, submission) {\n    interruptActiveLoads();\n    fetchLoadMatches.delete(key);\n    if (!match.route.action && !match.route.lazy) {\n      let error = getInternalRouterError(405, {\n        method: submission.formMethod,\n        pathname: path,\n        routeId: routeId\n      });\n      setFetcherError(key, routeId, error);\n      return;\n    }\n    // Put this fetcher into it's submitting state\n    let existingFetcher = state.fetchers.get(key);\n    let fetcher = getSubmittingFetcher(submission, existingFetcher);\n    state.fetchers.set(key, fetcher);\n    updateState({\n      fetchers: new Map(state.fetchers)\n    });\n    // Call the action for the fetcher\n    let abortController = new AbortController();\n    let fetchRequest = createClientSideRequest(init.history, path, abortController.signal, submission);\n    fetchControllers.set(key, abortController);\n    let originatingLoadId = incrementingLoadId;\n    let actionResult = await callLoaderOrAction(\"action\", fetchRequest, match, requestMatches, manifest, mapRouteProperties, basename);\n    if (fetchRequest.signal.aborted) {\n      // We can delete this so long as we weren't aborted by ou our own fetcher\n      // re-submit which would have put _new_ controller is in fetchControllers\n      if (fetchControllers.get(key) === abortController) {\n        fetchControllers.delete(key);\n      }\n      return;\n    }\n    if (isRedirectResult(actionResult)) {\n      fetchControllers.delete(key);\n      if (pendingNavigationLoadId > originatingLoadId) {\n        // A new navigation was kicked off after our action started, so that\n        // should take precedence over this redirect navigation.  We already\n        // set isRevalidationRequired so all loaders for the new route should\n        // fire unless opted out via shouldRevalidate\n        let doneFetcher = getDoneFetcher(undefined);\n        state.fetchers.set(key, doneFetcher);\n        updateState({\n          fetchers: new Map(state.fetchers)\n        });\n        return;\n      } else {\n        fetchRedirectIds.add(key);\n        let loadingFetcher = getLoadingFetcher(submission);\n        state.fetchers.set(key, loadingFetcher);\n        updateState({\n          fetchers: new Map(state.fetchers)\n        });\n        return startRedirectNavigation(state, actionResult, {\n          submission,\n          isFetchActionRedirect: true\n        });\n      }\n    }\n    // Process any non-redirect errors thrown\n    if (isErrorResult(actionResult)) {\n      setFetcherError(key, routeId, actionResult.error);\n      return;\n    }\n    if (isDeferredResult(actionResult)) {\n      throw getInternalRouterError(400, {\n        type: \"defer-action\"\n      });\n    }\n    // Start the data load for current matches, or the next location if we're\n    // in the middle of a navigation\n    let nextLocation = state.navigation.location || state.location;\n    let revalidationRequest = createClientSideRequest(init.history, nextLocation, abortController.signal);\n    let routesToUse = inFlightDataRoutes || dataRoutes;\n    let matches = state.navigation.state !== \"idle\" ? matchRoutes(routesToUse, state.navigation.location, basename) : state.matches;\n    invariant(matches, \"Didn't find any matches after fetcher action\");\n    let loadId = ++incrementingLoadId;\n    fetchReloadIds.set(key, loadId);\n    let loadFetcher = getLoadingFetcher(submission, actionResult.data);\n    state.fetchers.set(key, loadFetcher);\n    let [matchesToLoad, revalidatingFetchers] = getMatchesToLoad(init.history, state, matches, submission, nextLocation, isRevalidationRequired, cancelledDeferredRoutes, cancelledFetcherLoads, fetchLoadMatches, fetchRedirectIds, routesToUse, basename, {\n      [match.route.id]: actionResult.data\n    }, undefined // No need to send through errors since we short circuit above\n    );\n    // Put all revalidating fetchers into the loading state, except for the\n    // current fetcher which we want to keep in it's current loading state which\n    // contains it's action submission info + action data\n    revalidatingFetchers.filter(rf => rf.key !== key).forEach(rf => {\n      let staleKey = rf.key;\n      let existingFetcher = state.fetchers.get(staleKey);\n      let revalidatingFetcher = getLoadingFetcher(undefined, existingFetcher ? existingFetcher.data : undefined);\n      state.fetchers.set(staleKey, revalidatingFetcher);\n      if (fetchControllers.has(staleKey)) {\n        abortFetcher(staleKey);\n      }\n      if (rf.controller) {\n        fetchControllers.set(staleKey, rf.controller);\n      }\n    });\n    updateState({\n      fetchers: new Map(state.fetchers)\n    });\n    let abortPendingFetchRevalidations = () => revalidatingFetchers.forEach(rf => abortFetcher(rf.key));\n    abortController.signal.addEventListener(\"abort\", abortPendingFetchRevalidations);\n    let {\n      results,\n      loaderResults,\n      fetcherResults\n    } = await callLoadersAndMaybeResolveData(state.matches, matches, matchesToLoad, revalidatingFetchers, revalidationRequest);\n    if (abortController.signal.aborted) {\n      return;\n    }\n    abortController.signal.removeEventListener(\"abort\", abortPendingFetchRevalidations);\n    fetchReloadIds.delete(key);\n    fetchControllers.delete(key);\n    revalidatingFetchers.forEach(r => fetchControllers.delete(r.key));\n    let redirect = findRedirect(results);\n    if (redirect) {\n      if (redirect.idx >= matchesToLoad.length) {\n        // If this redirect came from a fetcher make sure we mark it in\n        // fetchRedirectIds so it doesn't get revalidated on the next set of\n        // loader executions\n        let fetcherKey = revalidatingFetchers[redirect.idx - matchesToLoad.length].key;\n        fetchRedirectIds.add(fetcherKey);\n      }\n      return startRedirectNavigation(state, redirect.result);\n    }\n    // Process and commit output from loaders\n    let {\n      loaderData,\n      errors\n    } = processLoaderData(state, state.matches, matchesToLoad, loaderResults, undefined, revalidatingFetchers, fetcherResults, activeDeferreds);\n    // Since we let revalidations complete even if the submitting fetcher was\n    // deleted, only put it back to idle if it hasn't been deleted\n    if (state.fetchers.has(key)) {\n      let doneFetcher = getDoneFetcher(actionResult.data);\n      state.fetchers.set(key, doneFetcher);\n    }\n    let didAbortFetchLoads = abortStaleFetchLoads(loadId);\n    // If we are currently in a navigation loading state and this fetcher is\n    // more recent than the navigation, we want the newer data so abort the\n    // navigation and complete it with the fetcher data\n    if (state.navigation.state === \"loading\" && loadId > pendingNavigationLoadId) {\n      invariant(pendingAction, \"Expected pending action\");\n      pendingNavigationController && pendingNavigationController.abort();\n      completeNavigation(state.navigation.location, {\n        matches,\n        loaderData,\n        errors,\n        fetchers: new Map(state.fetchers)\n      });\n    } else {\n      // otherwise just update with the fetcher data, preserving any existing\n      // loaderData for loaders that did not need to reload.  We have to\n      // manually merge here since we aren't going through completeNavigation\n      updateState(_extends({\n        errors,\n        loaderData: mergeLoaderData(state.loaderData, loaderData, matches, errors)\n      }, didAbortFetchLoads || revalidatingFetchers.length > 0 ? {\n        fetchers: new Map(state.fetchers)\n      } : {}));\n      isRevalidationRequired = false;\n    }\n  }\n  // Call the matched loader for fetcher.load(), handling redirects, errors, etc.\n  async function handleFetcherLoader(key, routeId, path, match, matches, submission) {\n    let existingFetcher = state.fetchers.get(key);\n    // Put this fetcher into it's loading state\n    let loadingFetcher = getLoadingFetcher(submission, existingFetcher ? existingFetcher.data : undefined);\n    state.fetchers.set(key, loadingFetcher);\n    updateState({\n      fetchers: new Map(state.fetchers)\n    });\n    // Call the loader for this fetcher route match\n    let abortController = new AbortController();\n    let fetchRequest = createClientSideRequest(init.history, path, abortController.signal);\n    fetchControllers.set(key, abortController);\n    let originatingLoadId = incrementingLoadId;\n    let result = await callLoaderOrAction(\"loader\", fetchRequest, match, matches, manifest, mapRouteProperties, basename);\n    // Deferred isn't supported for fetcher loads, await everything and treat it\n    // as a normal load.  resolveDeferredData will return undefined if this\n    // fetcher gets aborted, so we just leave result untouched and short circuit\n    // below if that happens\n    if (isDeferredResult(result)) {\n      result = (await resolveDeferredData(result, fetchRequest.signal, true)) || result;\n    }\n    // We can delete this so long as we weren't aborted by our our own fetcher\n    // re-load which would have put _new_ controller is in fetchControllers\n    if (fetchControllers.get(key) === abortController) {\n      fetchControllers.delete(key);\n    }\n    if (fetchRequest.signal.aborted) {\n      return;\n    }\n    // If the loader threw a redirect Response, start a new REPLACE navigation\n    if (isRedirectResult(result)) {\n      if (pendingNavigationLoadId > originatingLoadId) {\n        // A new navigation was kicked off after our loader started, so that\n        // should take precedence over this redirect navigation\n        let doneFetcher = getDoneFetcher(undefined);\n        state.fetchers.set(key, doneFetcher);\n        updateState({\n          fetchers: new Map(state.fetchers)\n        });\n        return;\n      } else {\n        fetchRedirectIds.add(key);\n        await startRedirectNavigation(state, result);\n        return;\n      }\n    }\n    // Process any non-redirect errors thrown\n    if (isErrorResult(result)) {\n      let boundaryMatch = findNearestBoundary(state.matches, routeId);\n      state.fetchers.delete(key);\n      // TODO: In remix, this would reset to IDLE_NAVIGATION if it was a catch -\n      // do we need to behave any differently with our non-redirect errors?\n      // What if it was a non-redirect Response?\n      updateState({\n        fetchers: new Map(state.fetchers),\n        errors: {\n          [boundaryMatch.route.id]: result.error\n        }\n      });\n      return;\n    }\n    invariant(!isDeferredResult(result), \"Unhandled fetcher deferred data\");\n    // Put the fetcher back into an idle state\n    let doneFetcher = getDoneFetcher(result.data);\n    state.fetchers.set(key, doneFetcher);\n    updateState({\n      fetchers: new Map(state.fetchers)\n    });\n  }\n  /**\n   * Utility function to handle redirects returned from an action or loader.\n   * Normally, a redirect \"replaces\" the navigation that triggered it.  So, for\n   * example:\n   *\n   *  - user is on /a\n   *  - user clicks a link to /b\n   *  - loader for /b redirects to /c\n   *\n   * In a non-JS app the browser would track the in-flight navigation to /b and\n   * then replace it with /c when it encountered the redirect response.  In\n   * the end it would only ever update the URL bar with /c.\n   *\n   * In client-side routing using pushState/replaceState, we aim to emulate\n   * this behavior and we also do not update history until the end of the\n   * navigation (including processed redirects).  This means that we never\n   * actually touch history until we've processed redirects, so we just use\n   * the history action from the original navigation (PUSH or REPLACE).\n   */\n  async function startRedirectNavigation(state, redirect, _temp) {\n    let {\n      submission,\n      replace,\n      isFetchActionRedirect\n    } = _temp === void 0 ? {} : _temp;\n    if (redirect.revalidate) {\n      isRevalidationRequired = true;\n    }\n    let redirectLocation = createLocation(state.location, redirect.location,\n    // TODO: This can be removed once we get rid of useTransition in Remix v2\n    _extends({\n      _isRedirect: true\n    }, isFetchActionRedirect ? {\n      _isFetchActionRedirect: true\n    } : {}));\n    invariant(redirectLocation, \"Expected a location on the redirect navigation\");\n    // Check if this an absolute external redirect that goes to a new origin\n    if (ABSOLUTE_URL_REGEX.test(redirect.location) && isBrowser) {\n      let url = init.history.createURL(redirect.location);\n      let isDifferentBasename = stripBasename(url.pathname, basename) == null;\n      if (routerWindow.location.origin !== url.origin || isDifferentBasename) {\n        if (replace) {\n          routerWindow.location.replace(redirect.location);\n        } else {\n          routerWindow.location.assign(redirect.location);\n        }\n        return;\n      }\n    }\n    // There's no need to abort on redirects, since we don't detect the\n    // redirect until the action/loaders have settled\n    pendingNavigationController = null;\n    let redirectHistoryAction = replace === true ? Action.Replace : Action.Push;\n    // Use the incoming submission if provided, fallback on the active one in\n    // state.navigation\n    let activeSubmission = submission || getSubmissionFromNavigation(state.navigation);\n    // If this was a 307/308 submission we want to preserve the HTTP method and\n    // re-submit the GET/POST/PUT/PATCH/DELETE as a submission navigation to the\n    // redirected location\n    if (redirectPreserveMethodStatusCodes.has(redirect.status) && activeSubmission && isMutationMethod(activeSubmission.formMethod)) {\n      await startNavigation(redirectHistoryAction, redirectLocation, {\n        submission: _extends({}, activeSubmission, {\n          formAction: redirect.location\n        }),\n        // Preserve this flag across redirects\n        preventScrollReset: pendingPreventScrollReset\n      });\n    } else if (isFetchActionRedirect) {\n      // For a fetch action redirect, we kick off a new loading navigation\n      // without the fetcher submission, but we send it along for shouldRevalidate\n      await startNavigation(redirectHistoryAction, redirectLocation, {\n        overrideNavigation: getLoadingNavigation(redirectLocation),\n        fetcherSubmission: activeSubmission,\n        // Preserve this flag across redirects\n        preventScrollReset: pendingPreventScrollReset\n      });\n    } else {\n      // If we have a submission, we will preserve it through the redirect navigation\n      let overrideNavigation = getLoadingNavigation(redirectLocation, activeSubmission);\n      await startNavigation(redirectHistoryAction, redirectLocation, {\n        overrideNavigation,\n        // Preserve this flag across redirects\n        preventScrollReset: pendingPreventScrollReset\n      });\n    }\n  }\n  async function callLoadersAndMaybeResolveData(currentMatches, matches, matchesToLoad, fetchersToLoad, request) {\n    // Call all navigation loaders and revalidating fetcher loaders in parallel,\n    // then slice off the results into separate arrays so we can handle them\n    // accordingly\n    let results = await Promise.all([...matchesToLoad.map(match => callLoaderOrAction(\"loader\", request, match, matches, manifest, mapRouteProperties, basename)), ...fetchersToLoad.map(f => {\n      if (f.matches && f.match && f.controller) {\n        return callLoaderOrAction(\"loader\", createClientSideRequest(init.history, f.path, f.controller.signal), f.match, f.matches, manifest, mapRouteProperties, basename);\n      } else {\n        let error = {\n          type: ResultType.error,\n          error: getInternalRouterError(404, {\n            pathname: f.path\n          })\n        };\n        return error;\n      }\n    })]);\n    let loaderResults = results.slice(0, matchesToLoad.length);\n    let fetcherResults = results.slice(matchesToLoad.length);\n    await Promise.all([resolveDeferredResults(currentMatches, matchesToLoad, loaderResults, loaderResults.map(() => request.signal), false, state.loaderData), resolveDeferredResults(currentMatches, fetchersToLoad.map(f => f.match), fetcherResults, fetchersToLoad.map(f => f.controller ? f.controller.signal : null), true)]);\n    return {\n      results,\n      loaderResults,\n      fetcherResults\n    };\n  }\n  function interruptActiveLoads() {\n    // Every interruption triggers a revalidation\n    isRevalidationRequired = true;\n    // Cancel pending route-level deferreds and mark cancelled routes for\n    // revalidation\n    cancelledDeferredRoutes.push(...cancelActiveDeferreds());\n    // Abort in-flight fetcher loads\n    fetchLoadMatches.forEach((_, key) => {\n      if (fetchControllers.has(key)) {\n        cancelledFetcherLoads.push(key);\n        abortFetcher(key);\n      }\n    });\n  }\n  function setFetcherError(key, routeId, error) {\n    let boundaryMatch = findNearestBoundary(state.matches, routeId);\n    deleteFetcher(key);\n    updateState({\n      errors: {\n        [boundaryMatch.route.id]: error\n      },\n      fetchers: new Map(state.fetchers)\n    });\n  }\n  function deleteFetcher(key) {\n    let fetcher = state.fetchers.get(key);\n    // Don't abort the controller if this is a deletion of a fetcher.submit()\n    // in it's loading phase since - we don't want to abort the corresponding\n    // revalidation and want them to complete and land\n    if (fetchControllers.has(key) && !(fetcher && fetcher.state === \"loading\" && fetchReloadIds.has(key))) {\n      abortFetcher(key);\n    }\n    fetchLoadMatches.delete(key);\n    fetchReloadIds.delete(key);\n    fetchRedirectIds.delete(key);\n    state.fetchers.delete(key);\n  }\n  function abortFetcher(key) {\n    let controller = fetchControllers.get(key);\n    invariant(controller, \"Expected fetch controller: \" + key);\n    controller.abort();\n    fetchControllers.delete(key);\n  }\n  function markFetchersDone(keys) {\n    for (let key of keys) {\n      let fetcher = getFetcher(key);\n      let doneFetcher = getDoneFetcher(fetcher.data);\n      state.fetchers.set(key, doneFetcher);\n    }\n  }\n  function markFetchRedirectsDone() {\n    let doneKeys = [];\n    let updatedFetchers = false;\n    for (let key of fetchRedirectIds) {\n      let fetcher = state.fetchers.get(key);\n      invariant(fetcher, \"Expected fetcher: \" + key);\n      if (fetcher.state === \"loading\") {\n        fetchRedirectIds.delete(key);\n        doneKeys.push(key);\n        updatedFetchers = true;\n      }\n    }\n    markFetchersDone(doneKeys);\n    return updatedFetchers;\n  }\n  function abortStaleFetchLoads(landedId) {\n    let yeetedKeys = [];\n    for (let [key, id] of fetchReloadIds) {\n      if (id < landedId) {\n        let fetcher = state.fetchers.get(key);\n        invariant(fetcher, \"Expected fetcher: \" + key);\n        if (fetcher.state === \"loading\") {\n          abortFetcher(key);\n          fetchReloadIds.delete(key);\n          yeetedKeys.push(key);\n        }\n      }\n    }\n    markFetchersDone(yeetedKeys);\n    return yeetedKeys.length > 0;\n  }\n  function getBlocker(key, fn) {\n    let blocker = state.blockers.get(key) || IDLE_BLOCKER;\n    if (blockerFunctions.get(key) !== fn) {\n      blockerFunctions.set(key, fn);\n    }\n    return blocker;\n  }\n  function deleteBlocker(key) {\n    state.blockers.delete(key);\n    blockerFunctions.delete(key);\n  }\n  // Utility function to update blockers, ensuring valid state transitions\n  function updateBlocker(key, newBlocker) {\n    let blocker = state.blockers.get(key) || IDLE_BLOCKER;\n    // Poor mans state machine :)\n    // https://mermaid.live/edit#pako:eNqVkc9OwzAMxl8l8nnjAYrEtDIOHEBIgwvKJTReGy3_lDpIqO27k6awMG0XcrLlnz87nwdonESogKXXBuE79rq75XZO3-yHds0RJVuv70YrPlUrCEe2HfrORS3rubqZfuhtpg5C9wk5tZ4VKcRUq88q9Z8RS0-48cE1iHJkL0ugbHuFLus9L6spZy8nX9MP2CNdomVaposqu3fGayT8T8-jJQwhepo_UtpgBQaDEUom04dZhAN1aJBDlUKJBxE1ceB2Smj0Mln-IBW5AFU2dwUiktt_2Qaq2dBfaKdEup85UV7Yd-dKjlnkabl2Pvr0DTkTreM\n    invariant(blocker.state === \"unblocked\" && newBlocker.state === \"blocked\" || blocker.state === \"blocked\" && newBlocker.state === \"blocked\" || blocker.state === \"blocked\" && newBlocker.state === \"proceeding\" || blocker.state === \"blocked\" && newBlocker.state === \"unblocked\" || blocker.state === \"proceeding\" && newBlocker.state === \"unblocked\", \"Invalid blocker state transition: \" + blocker.state + \" -> \" + newBlocker.state);\n    let blockers = new Map(state.blockers);\n    blockers.set(key, newBlocker);\n    updateState({\n      blockers\n    });\n  }\n  function shouldBlockNavigation(_ref2) {\n    let {\n      currentLocation,\n      nextLocation,\n      historyAction\n    } = _ref2;\n    if (blockerFunctions.size === 0) {\n      return;\n    }\n    // We ony support a single active blocker at the moment since we don't have\n    // any compelling use cases for multi-blocker yet\n    if (blockerFunctions.size > 1) {\n      warning(false, \"A router only supports one blocker at a time\");\n    }\n    let entries = Array.from(blockerFunctions.entries());\n    let [blockerKey, blockerFunction] = entries[entries.length - 1];\n    let blocker = state.blockers.get(blockerKey);\n    if (blocker && blocker.state === \"proceeding\") {\n      // If the blocker is currently proceeding, we don't need to re-check\n      // it and can let this navigation continue\n      return;\n    }\n    // At this point, we know we're unblocked/blocked so we need to check the\n    // user-provided blocker function\n    if (blockerFunction({\n      currentLocation,\n      nextLocation,\n      historyAction\n    })) {\n      return blockerKey;\n    }\n  }\n  function cancelActiveDeferreds(predicate) {\n    let cancelledRouteIds = [];\n    activeDeferreds.forEach((dfd, routeId) => {\n      if (!predicate || predicate(routeId)) {\n        // Cancel the deferred - but do not remove from activeDeferreds here -\n        // we rely on the subscribers to do that so our tests can assert proper\n        // cleanup via _internalActiveDeferreds\n        dfd.cancel();\n        cancelledRouteIds.push(routeId);\n        activeDeferreds.delete(routeId);\n      }\n    });\n    return cancelledRouteIds;\n  }\n  // Opt in to capturing and reporting scroll positions during navigations,\n  // used by the <ScrollRestoration> component\n  function enableScrollRestoration(positions, getPosition, getKey) {\n    savedScrollPositions = positions;\n    getScrollPosition = getPosition;\n    getScrollRestorationKey = getKey || null;\n    // Perform initial hydration scroll restoration, since we miss the boat on\n    // the initial updateState() because we've not yet rendered <ScrollRestoration/>\n    // and therefore have no savedScrollPositions available\n    if (!initialScrollRestored && state.navigation === IDLE_NAVIGATION) {\n      initialScrollRestored = true;\n      let y = getSavedScrollPosition(state.location, state.matches);\n      if (y != null) {\n        updateState({\n          restoreScrollPosition: y\n        });\n      }\n    }\n    return () => {\n      savedScrollPositions = null;\n      getScrollPosition = null;\n      getScrollRestorationKey = null;\n    };\n  }\n  function getScrollKey(location, matches) {\n    if (getScrollRestorationKey) {\n      let key = getScrollRestorationKey(location, matches.map(m => createUseMatchesMatch(m, state.loaderData)));\n      return key || location.key;\n    }\n    return location.key;\n  }\n  function saveScrollPosition(location, matches) {\n    if (savedScrollPositions && getScrollPosition) {\n      let key = getScrollKey(location, matches);\n      savedScrollPositions[key] = getScrollPosition();\n    }\n  }\n  function getSavedScrollPosition(location, matches) {\n    if (savedScrollPositions) {\n      let key = getScrollKey(location, matches);\n      let y = savedScrollPositions[key];\n      if (typeof y === \"number\") {\n        return y;\n      }\n    }\n    return null;\n  }\n  function _internalSetRoutes(newRoutes) {\n    manifest = {};\n    inFlightDataRoutes = convertRoutesToDataRoutes(newRoutes, mapRouteProperties, undefined, manifest);\n  }\n  router = {\n    get basename() {\n      return basename;\n    },\n    get state() {\n      return state;\n    },\n    get routes() {\n      return dataRoutes;\n    },\n    initialize,\n    subscribe,\n    enableScrollRestoration,\n    navigate,\n    fetch,\n    revalidate,\n    // Passthrough to history-aware createHref used by useHref so we get proper\n    // hash-aware URLs in DOM paths\n    createHref: to => init.history.createHref(to),\n    encodeLocation: to => init.history.encodeLocation(to),\n    getFetcher,\n    deleteFetcher,\n    dispose,\n    getBlocker,\n    deleteBlocker,\n    _internalFetchControllers: fetchControllers,\n    _internalActiveDeferreds: activeDeferreds,\n    // TODO: Remove setRoutes, it's temporary to avoid dealing with\n    // updating the tree while validating the update algorithm.\n    _internalSetRoutes\n  };\n  return router;\n}\n//#endregion\n////////////////////////////////////////////////////////////////////////////////\n//#region createStaticHandler\n////////////////////////////////////////////////////////////////////////////////\nconst UNSAFE_DEFERRED_SYMBOL = Symbol(\"deferred\");\nfunction createStaticHandler(routes, opts) {\n  invariant(routes.length > 0, \"You must provide a non-empty routes array to createStaticHandler\");\n  let manifest = {};\n  let basename = (opts ? opts.basename : null) || \"/\";\n  let mapRouteProperties;\n  if (opts != null && opts.mapRouteProperties) {\n    mapRouteProperties = opts.mapRouteProperties;\n  } else if (opts != null && opts.detectErrorBoundary) {\n    // If they are still using the deprecated version, wrap it with the new API\n    let detectErrorBoundary = opts.detectErrorBoundary;\n    mapRouteProperties = route => ({\n      hasErrorBoundary: detectErrorBoundary(route)\n    });\n  } else {\n    mapRouteProperties = defaultMapRouteProperties;\n  }\n  let dataRoutes = convertRoutesToDataRoutes(routes, mapRouteProperties, undefined, manifest);\n  /**\n   * The query() method is intended for document requests, in which we want to\n   * call an optional action and potentially multiple loaders for all nested\n   * routes.  It returns a StaticHandlerContext object, which is very similar\n   * to the router state (location, loaderData, actionData, errors, etc.) and\n   * also adds SSR-specific information such as the statusCode and headers\n   * from action/loaders Responses.\n   *\n   * It _should_ never throw and should report all errors through the\n   * returned context.errors object, properly associating errors to their error\n   * boundary.  Additionally, it tracks _deepestRenderedBoundaryId which can be\n   * used to emulate React error boundaries during SSr by performing a second\n   * pass only down to the boundaryId.\n   *\n   * The one exception where we do not return a StaticHandlerContext is when a\n   * redirect response is returned or thrown from any action/loader.  We\n   * propagate that out and return the raw Response so the HTTP server can\n   * return it directly.\n   */\n  async function query(request, _temp2) {\n    let {\n      requestContext\n    } = _temp2 === void 0 ? {} : _temp2;\n    let url = new URL(request.url);\n    let method = request.method;\n    let location = createLocation(\"\", createPath(url), null, \"default\");\n    let matches = matchRoutes(dataRoutes, location, basename);\n    // SSR supports HEAD requests while SPA doesn't\n    if (!isValidMethod(method) && method !== \"HEAD\") {\n      let error = getInternalRouterError(405, {\n        method\n      });\n      let {\n        matches: methodNotAllowedMatches,\n        route\n      } = getShortCircuitMatches(dataRoutes);\n      return {\n        basename,\n        location,\n        matches: methodNotAllowedMatches,\n        loaderData: {},\n        actionData: null,\n        errors: {\n          [route.id]: error\n        },\n        statusCode: error.status,\n        loaderHeaders: {},\n        actionHeaders: {},\n        activeDeferreds: null\n      };\n    } else if (!matches) {\n      let error = getInternalRouterError(404, {\n        pathname: location.pathname\n      });\n      let {\n        matches: notFoundMatches,\n        route\n      } = getShortCircuitMatches(dataRoutes);\n      return {\n        basename,\n        location,\n        matches: notFoundMatches,\n        loaderData: {},\n        actionData: null,\n        errors: {\n          [route.id]: error\n        },\n        statusCode: error.status,\n        loaderHeaders: {},\n        actionHeaders: {},\n        activeDeferreds: null\n      };\n    }\n    let result = await queryImpl(request, location, matches, requestContext);\n    if (isResponse(result)) {\n      return result;\n    }\n    // When returning StaticHandlerContext, we patch back in the location here\n    // since we need it for React Context.  But this helps keep our submit and\n    // loadRouteData operating on a Request instead of a Location\n    return _extends({\n      location,\n      basename\n    }, result);\n  }\n  /**\n   * The queryRoute() method is intended for targeted route requests, either\n   * for fetch ?_data requests or resource route requests.  In this case, we\n   * are only ever calling a single action or loader, and we are returning the\n   * returned value directly.  In most cases, this will be a Response returned\n   * from the action/loader, but it may be a primitive or other value as well -\n   * and in such cases the calling context should handle that accordingly.\n   *\n   * We do respect the throw/return differentiation, so if an action/loader\n   * throws, then this method will throw the value.  This is important so we\n   * can do proper boundary identification in Remix where a thrown Response\n   * must go to the Catch Boundary but a returned Response is happy-path.\n   *\n   * One thing to note is that any Router-initiated Errors that make sense\n   * to associate with a status code will be thrown as an ErrorResponse\n   * instance which include the raw Error, such that the calling context can\n   * serialize the error as they see fit while including the proper response\n   * code.  Examples here are 404 and 405 errors that occur prior to reaching\n   * any user-defined loaders.\n   */\n  async function queryRoute(request, _temp3) {\n    let {\n      routeId,\n      requestContext\n    } = _temp3 === void 0 ? {} : _temp3;\n    let url = new URL(request.url);\n    let method = request.method;\n    let location = createLocation(\"\", createPath(url), null, \"default\");\n    let matches = matchRoutes(dataRoutes, location, basename);\n    // SSR supports HEAD requests while SPA doesn't\n    if (!isValidMethod(method) && method !== \"HEAD\" && method !== \"OPTIONS\") {\n      throw getInternalRouterError(405, {\n        method\n      });\n    } else if (!matches) {\n      throw getInternalRouterError(404, {\n        pathname: location.pathname\n      });\n    }\n    let match = routeId ? matches.find(m => m.route.id === routeId) : getTargetMatch(matches, location);\n    if (routeId && !match) {\n      throw getInternalRouterError(403, {\n        pathname: location.pathname,\n        routeId\n      });\n    } else if (!match) {\n      // This should never hit I don't think?\n      throw getInternalRouterError(404, {\n        pathname: location.pathname\n      });\n    }\n    let result = await queryImpl(request, location, matches, requestContext, match);\n    if (isResponse(result)) {\n      return result;\n    }\n    let error = result.errors ? Object.values(result.errors)[0] : undefined;\n    if (error !== undefined) {\n      // If we got back result.errors, that means the loader/action threw\n      // _something_ that wasn't a Response, but it's not guaranteed/required\n      // to be an `instanceof Error` either, so we have to use throw here to\n      // preserve the \"error\" state outside of queryImpl.\n      throw error;\n    }\n    // Pick off the right state value to return\n    if (result.actionData) {\n      return Object.values(result.actionData)[0];\n    }\n    if (result.loaderData) {\n      var _result$activeDeferre;\n      let data = Object.values(result.loaderData)[0];\n      if ((_result$activeDeferre = result.activeDeferreds) != null && _result$activeDeferre[match.route.id]) {\n        data[UNSAFE_DEFERRED_SYMBOL] = result.activeDeferreds[match.route.id];\n      }\n      return data;\n    }\n    return undefined;\n  }\n  async function queryImpl(request, location, matches, requestContext, routeMatch) {\n    invariant(request.signal, \"query()/queryRoute() requests must contain an AbortController signal\");\n    try {\n      if (isMutationMethod(request.method.toLowerCase())) {\n        let result = await submit(request, matches, routeMatch || getTargetMatch(matches, location), requestContext, routeMatch != null);\n        return result;\n      }\n      let result = await loadRouteData(request, matches, requestContext, routeMatch);\n      return isResponse(result) ? result : _extends({}, result, {\n        actionData: null,\n        actionHeaders: {}\n      });\n    } catch (e) {\n      // If the user threw/returned a Response in callLoaderOrAction, we throw\n      // it to bail out and then return or throw here based on whether the user\n      // returned or threw\n      if (isQueryRouteResponse(e)) {\n        if (e.type === ResultType.error && !isRedirectResponse(e.response)) {\n          throw e.response;\n        }\n        return e.response;\n      }\n      // Redirects are always returned since they don't propagate to catch\n      // boundaries\n      if (isRedirectResponse(e)) {\n        return e;\n      }\n      throw e;\n    }\n  }\n  async function submit(request, matches, actionMatch, requestContext, isRouteRequest) {\n    let result;\n    if (!actionMatch.route.action && !actionMatch.route.lazy) {\n      let error = getInternalRouterError(405, {\n        method: request.method,\n        pathname: new URL(request.url).pathname,\n        routeId: actionMatch.route.id\n      });\n      if (isRouteRequest) {\n        throw error;\n      }\n      result = {\n        type: ResultType.error,\n        error\n      };\n    } else {\n      result = await callLoaderOrAction(\"action\", request, actionMatch, matches, manifest, mapRouteProperties, basename, {\n        isStaticRequest: true,\n        isRouteRequest,\n        requestContext\n      });\n      if (request.signal.aborted) {\n        let method = isRouteRequest ? \"queryRoute\" : \"query\";\n        throw new Error(method + \"() call aborted\");\n      }\n    }\n    if (isRedirectResult(result)) {\n      // Uhhhh - this should never happen, we should always throw these from\n      // callLoaderOrAction, but the type narrowing here keeps TS happy and we\n      // can get back on the \"throw all redirect responses\" train here should\n      // this ever happen :/\n      throw new Response(null, {\n        status: result.status,\n        headers: {\n          Location: result.location\n        }\n      });\n    }\n    if (isDeferredResult(result)) {\n      let error = getInternalRouterError(400, {\n        type: \"defer-action\"\n      });\n      if (isRouteRequest) {\n        throw error;\n      }\n      result = {\n        type: ResultType.error,\n        error\n      };\n    }\n    if (isRouteRequest) {\n      // Note: This should only be non-Response values if we get here, since\n      // isRouteRequest should throw any Response received in callLoaderOrAction\n      if (isErrorResult(result)) {\n        throw result.error;\n      }\n      return {\n        matches: [actionMatch],\n        loaderData: {},\n        actionData: {\n          [actionMatch.route.id]: result.data\n        },\n        errors: null,\n        // Note: statusCode + headers are unused here since queryRoute will\n        // return the raw Response or value\n        statusCode: 200,\n        loaderHeaders: {},\n        actionHeaders: {},\n        activeDeferreds: null\n      };\n    }\n    if (isErrorResult(result)) {\n      // Store off the pending error - we use it to determine which loaders\n      // to call and will commit it when we complete the navigation\n      let boundaryMatch = findNearestBoundary(matches, actionMatch.route.id);\n      let context = await loadRouteData(request, matches, requestContext, undefined, {\n        [boundaryMatch.route.id]: result.error\n      });\n      // action status codes take precedence over loader status codes\n      return _extends({}, context, {\n        statusCode: isRouteErrorResponse(result.error) ? result.error.status : 500,\n        actionData: null,\n        actionHeaders: _extends({}, result.headers ? {\n          [actionMatch.route.id]: result.headers\n        } : {})\n      });\n    }\n    // Create a GET request for the loaders\n    let loaderRequest = new Request(request.url, {\n      headers: request.headers,\n      redirect: request.redirect,\n      signal: request.signal\n    });\n    let context = await loadRouteData(loaderRequest, matches, requestContext);\n    return _extends({}, context, result.statusCode ? {\n      statusCode: result.statusCode\n    } : {}, {\n      actionData: {\n        [actionMatch.route.id]: result.data\n      },\n      actionHeaders: _extends({}, result.headers ? {\n        [actionMatch.route.id]: result.headers\n      } : {})\n    });\n  }\n  async function loadRouteData(request, matches, requestContext, routeMatch, pendingActionError) {\n    let isRouteRequest = routeMatch != null;\n    // Short circuit if we have no loaders to run (queryRoute())\n    if (isRouteRequest && !(routeMatch != null && routeMatch.route.loader) && !(routeMatch != null && routeMatch.route.lazy)) {\n      throw getInternalRouterError(400, {\n        method: request.method,\n        pathname: new URL(request.url).pathname,\n        routeId: routeMatch == null ? void 0 : routeMatch.route.id\n      });\n    }\n    let requestMatches = routeMatch ? [routeMatch] : getLoaderMatchesUntilBoundary(matches, Object.keys(pendingActionError || {})[0]);\n    let matchesToLoad = requestMatches.filter(m => m.route.loader || m.route.lazy);\n    // Short circuit if we have no loaders to run (query())\n    if (matchesToLoad.length === 0) {\n      return {\n        matches,\n        // Add a null for all matched routes for proper revalidation on the client\n        loaderData: matches.reduce((acc, m) => Object.assign(acc, {\n          [m.route.id]: null\n        }), {}),\n        errors: pendingActionError || null,\n        statusCode: 200,\n        loaderHeaders: {},\n        activeDeferreds: null\n      };\n    }\n    let results = await Promise.all([...matchesToLoad.map(match => callLoaderOrAction(\"loader\", request, match, matches, manifest, mapRouteProperties, basename, {\n      isStaticRequest: true,\n      isRouteRequest,\n      requestContext\n    }))]);\n    if (request.signal.aborted) {\n      let method = isRouteRequest ? \"queryRoute\" : \"query\";\n      throw new Error(method + \"() call aborted\");\n    }\n    // Process and commit output from loaders\n    let activeDeferreds = new Map();\n    let context = processRouteLoaderData(matches, matchesToLoad, results, pendingActionError, activeDeferreds);\n    // Add a null for any non-loader matches for proper revalidation on the client\n    let executedLoaders = new Set(matchesToLoad.map(match => match.route.id));\n    matches.forEach(match => {\n      if (!executedLoaders.has(match.route.id)) {\n        context.loaderData[match.route.id] = null;\n      }\n    });\n    return _extends({}, context, {\n      matches,\n      activeDeferreds: activeDeferreds.size > 0 ? Object.fromEntries(activeDeferreds.entries()) : null\n    });\n  }\n  return {\n    dataRoutes,\n    query,\n    queryRoute\n  };\n}\n//#endregion\n////////////////////////////////////////////////////////////////////////////////\n//#region Helpers\n////////////////////////////////////////////////////////////////////////////////\n/**\n * Given an existing StaticHandlerContext and an error thrown at render time,\n * provide an updated StaticHandlerContext suitable for a second SSR render\n */\nfunction getStaticContextFromError(routes, context, error) {\n  let newContext = _extends({}, context, {\n    statusCode: 500,\n    errors: {\n      [context._deepestRenderedBoundaryId || routes[0].id]: error\n    }\n  });\n  return newContext;\n}\nfunction isSubmissionNavigation(opts) {\n  return opts != null && (\"formData\" in opts && opts.formData != null || \"body\" in opts && opts.body !== undefined);\n}\nfunction normalizeTo(location, matches, basename, prependBasename, to, fromRouteId, relative) {\n  let contextualMatches;\n  let activeRouteMatch;\n  if (fromRouteId != null && relative !== \"path\") {\n    // Grab matches up to the calling route so our route-relative logic is\n    // relative to the correct source route.  When using relative:path,\n    // fromRouteId is ignored since that is always relative to the current\n    // location path\n    contextualMatches = [];\n    for (let match of matches) {\n      contextualMatches.push(match);\n      if (match.route.id === fromRouteId) {\n        activeRouteMatch = match;\n        break;\n      }\n    }\n  } else {\n    contextualMatches = matches;\n    activeRouteMatch = matches[matches.length - 1];\n  }\n  // Resolve the relative path\n  let path = resolveTo(to ? to : \".\", getPathContributingMatches(contextualMatches).map(m => m.pathnameBase), stripBasename(location.pathname, basename) || location.pathname, relative === \"path\");\n  // When `to` is not specified we inherit search/hash from the current\n  // location, unlike when to=\".\" and we just inherit the path.\n  // See https://github.com/remix-run/remix/issues/927\n  if (to == null) {\n    path.search = location.search;\n    path.hash = location.hash;\n  }\n  // Add an ?index param for matched index routes if we don't already have one\n  if ((to == null || to === \"\" || to === \".\") && activeRouteMatch && activeRouteMatch.route.index && !hasNakedIndexQuery(path.search)) {\n    path.search = path.search ? path.search.replace(/^\\?/, \"?index&\") : \"?index\";\n  }\n  // If we're operating within a basename, prepend it to the pathname.  If\n  // this is a root navigation, then just use the raw basename which allows\n  // the basename to have full control over the presence of a trailing slash\n  // on root actions\n  if (prependBasename && basename !== \"/\") {\n    path.pathname = path.pathname === \"/\" ? basename : joinPaths([basename, path.pathname]);\n  }\n  return createPath(path);\n}\n// Normalize navigation options by converting formMethod=GET formData objects to\n// URLSearchParams so they behave identically to links with query params\nfunction normalizeNavigateOptions(normalizeFormMethod, isFetcher, path, opts) {\n  // Return location verbatim on non-submission navigations\n  if (!opts || !isSubmissionNavigation(opts)) {\n    return {\n      path\n    };\n  }\n  if (opts.formMethod && !isValidMethod(opts.formMethod)) {\n    return {\n      path,\n      error: getInternalRouterError(405, {\n        method: opts.formMethod\n      })\n    };\n  }\n  let getInvalidBodyError = () => ({\n    path,\n    error: getInternalRouterError(400, {\n      type: \"invalid-body\"\n    })\n  });\n  // Create a Submission on non-GET navigations\n  let rawFormMethod = opts.formMethod || \"get\";\n  let formMethod = normalizeFormMethod ? rawFormMethod.toUpperCase() : rawFormMethod.toLowerCase();\n  let formAction = stripHashFromPath(path);\n  if (opts.body !== undefined) {\n    if (opts.formEncType === \"text/plain\") {\n      // text only support POST/PUT/PATCH/DELETE submissions\n      if (!isMutationMethod(formMethod)) {\n        return getInvalidBodyError();\n      }\n      let text = typeof opts.body === \"string\" ? opts.body : opts.body instanceof FormData || opts.body instanceof URLSearchParams ?\n      // https://html.spec.whatwg.org/multipage/form-control-infrastructure.html#plain-text-form-data\n      Array.from(opts.body.entries()).reduce((acc, _ref3) => {\n        let [name, value] = _ref3;\n        return \"\" + acc + name + \"=\" + value + \"\\n\";\n      }, \"\") : String(opts.body);\n      return {\n        path,\n        submission: {\n          formMethod,\n          formAction,\n          formEncType: opts.formEncType,\n          formData: undefined,\n          json: undefined,\n          text\n        }\n      };\n    } else if (opts.formEncType === \"application/json\") {\n      // json only supports POST/PUT/PATCH/DELETE submissions\n      if (!isMutationMethod(formMethod)) {\n        return getInvalidBodyError();\n      }\n      try {\n        let json = typeof opts.body === \"string\" ? JSON.parse(opts.body) : opts.body;\n        return {\n          path,\n          submission: {\n            formMethod,\n            formAction,\n            formEncType: opts.formEncType,\n            formData: undefined,\n            json,\n            text: undefined\n          }\n        };\n      } catch (e) {\n        return getInvalidBodyError();\n      }\n    }\n  }\n  invariant(typeof FormData === \"function\", \"FormData is not available in this environment\");\n  let searchParams;\n  let formData;\n  if (opts.formData) {\n    searchParams = convertFormDataToSearchParams(opts.formData);\n    formData = opts.formData;\n  } else if (opts.body instanceof FormData) {\n    searchParams = convertFormDataToSearchParams(opts.body);\n    formData = opts.body;\n  } else if (opts.body instanceof URLSearchParams) {\n    searchParams = opts.body;\n    formData = convertSearchParamsToFormData(searchParams);\n  } else if (opts.body == null) {\n    searchParams = new URLSearchParams();\n    formData = new FormData();\n  } else {\n    try {\n      searchParams = new URLSearchParams(opts.body);\n      formData = convertSearchParamsToFormData(searchParams);\n    } catch (e) {\n      return getInvalidBodyError();\n    }\n  }\n  let submission = {\n    formMethod,\n    formAction,\n    formEncType: opts && opts.formEncType || \"application/x-www-form-urlencoded\",\n    formData,\n    json: undefined,\n    text: undefined\n  };\n  if (isMutationMethod(submission.formMethod)) {\n    return {\n      path,\n      submission\n    };\n  }\n  // Flatten submission onto URLSearchParams for GET submissions\n  let parsedPath = parsePath(path);\n  // On GET navigation submissions we can drop the ?index param from the\n  // resulting location since all loaders will run.  But fetcher GET submissions\n  // only run a single loader so we need to preserve any incoming ?index params\n  if (isFetcher && parsedPath.search && hasNakedIndexQuery(parsedPath.search)) {\n    searchParams.append(\"index\", \"\");\n  }\n  parsedPath.search = \"?\" + searchParams;\n  return {\n    path: createPath(parsedPath),\n    submission\n  };\n}\n// Filter out all routes below any caught error as they aren't going to\n// render so we don't need to load them\nfunction getLoaderMatchesUntilBoundary(matches, boundaryId) {\n  let boundaryMatches = matches;\n  if (boundaryId) {\n    let index = matches.findIndex(m => m.route.id === boundaryId);\n    if (index >= 0) {\n      boundaryMatches = matches.slice(0, index);\n    }\n  }\n  return boundaryMatches;\n}\nfunction getMatchesToLoad(history, state, matches, submission, location, isRevalidationRequired, cancelledDeferredRoutes, cancelledFetcherLoads, fetchLoadMatches, fetchRedirectIds, routesToUse, basename, pendingActionData, pendingError) {\n  let actionResult = pendingError ? Object.values(pendingError)[0] : pendingActionData ? Object.values(pendingActionData)[0] : undefined;\n  let currentUrl = history.createURL(state.location);\n  let nextUrl = history.createURL(location);\n  // Pick navigation matches that are net-new or qualify for revalidation\n  let boundaryId = pendingError ? Object.keys(pendingError)[0] : undefined;\n  let boundaryMatches = getLoaderMatchesUntilBoundary(matches, boundaryId);\n  let navigationMatches = boundaryMatches.filter((match, index) => {\n    if (match.route.lazy) {\n      // We haven't loaded this route yet so we don't know if it's got a loader!\n      return true;\n    }\n    if (match.route.loader == null) {\n      return false;\n    }\n    // Always call the loader on new route instances and pending defer cancellations\n    if (isNewLoader(state.loaderData, state.matches[index], match) || cancelledDeferredRoutes.some(id => id === match.route.id)) {\n      return true;\n    }\n    // This is the default implementation for when we revalidate.  If the route\n    // provides it's own implementation, then we give them full control but\n    // provide this value so they can leverage it if needed after they check\n    // their own specific use cases\n    let currentRouteMatch = state.matches[index];\n    let nextRouteMatch = match;\n    return shouldRevalidateLoader(match, _extends({\n      currentUrl,\n      currentParams: currentRouteMatch.params,\n      nextUrl,\n      nextParams: nextRouteMatch.params\n    }, submission, {\n      actionResult,\n      defaultShouldRevalidate:\n      // Forced revalidation due to submission, useRevalidator, or X-Remix-Revalidate\n      isRevalidationRequired ||\n      // Clicked the same link, resubmitted a GET form\n      currentUrl.pathname + currentUrl.search === nextUrl.pathname + nextUrl.search ||\n      // Search params affect all loaders\n      currentUrl.search !== nextUrl.search || isNewRouteInstance(currentRouteMatch, nextRouteMatch)\n    }));\n  });\n  // Pick fetcher.loads that need to be revalidated\n  let revalidatingFetchers = [];\n  fetchLoadMatches.forEach((f, key) => {\n    // Don't revalidate if fetcher won't be present in the subsequent render\n    if (!matches.some(m => m.route.id === f.routeId)) {\n      return;\n    }\n    let fetcherMatches = matchRoutes(routesToUse, f.path, basename);\n    // If the fetcher path no longer matches, push it in with null matches so\n    // we can trigger a 404 in callLoadersAndMaybeResolveData.  Note this is\n    // currently only a use-case for Remix HMR where the route tree can change\n    // at runtime and remove a route previously loaded via a fetcher\n    if (!fetcherMatches) {\n      revalidatingFetchers.push({\n        key,\n        routeId: f.routeId,\n        path: f.path,\n        matches: null,\n        match: null,\n        controller: null\n      });\n      return;\n    }\n    // Revalidating fetchers are decoupled from the route matches since they\n    // load from a static href.  They revalidate based on explicit revalidation\n    // (submission, useRevalidator, or X-Remix-Revalidate)\n    let fetcher = state.fetchers.get(key);\n    let fetcherMatch = getTargetMatch(fetcherMatches, f.path);\n    let shouldRevalidate = false;\n    if (fetchRedirectIds.has(key)) {\n      // Never trigger a revalidation of an actively redirecting fetcher\n      shouldRevalidate = false;\n    } else if (cancelledFetcherLoads.includes(key)) {\n      // Always revalidate if the fetcher was cancelled\n      shouldRevalidate = true;\n    } else if (fetcher && fetcher.state !== \"idle\" && fetcher.data === undefined) {\n      // If the fetcher hasn't ever completed loading yet, then this isn't a\n      // revalidation, it would just be a brand new load if an explicit\n      // revalidation is required\n      shouldRevalidate = isRevalidationRequired;\n    } else {\n      // Otherwise fall back on any user-defined shouldRevalidate, defaulting\n      // to explicit revalidations only\n      shouldRevalidate = shouldRevalidateLoader(fetcherMatch, _extends({\n        currentUrl,\n        currentParams: state.matches[state.matches.length - 1].params,\n        nextUrl,\n        nextParams: matches[matches.length - 1].params\n      }, submission, {\n        actionResult,\n        defaultShouldRevalidate: isRevalidationRequired\n      }));\n    }\n    if (shouldRevalidate) {\n      revalidatingFetchers.push({\n        key,\n        routeId: f.routeId,\n        path: f.path,\n        matches: fetcherMatches,\n        match: fetcherMatch,\n        controller: new AbortController()\n      });\n    }\n  });\n  return [navigationMatches, revalidatingFetchers];\n}\nfunction isNewLoader(currentLoaderData, currentMatch, match) {\n  let isNew =\n  // [a] -> [a, b]\n  !currentMatch ||\n  // [a, b] -> [a, c]\n  match.route.id !== currentMatch.route.id;\n  // Handle the case that we don't have data for a re-used route, potentially\n  // from a prior error or from a cancelled pending deferred\n  let isMissingData = currentLoaderData[match.route.id] === undefined;\n  // Always load if this is a net-new route or we don't yet have data\n  return isNew || isMissingData;\n}\nfunction isNewRouteInstance(currentMatch, match) {\n  let currentPath = currentMatch.route.path;\n  return (\n    // param change for this match, /users/123 -> /users/456\n    currentMatch.pathname !== match.pathname ||\n    // splat param changed, which is not present in match.path\n    // e.g. /files/images/avatar.jpg -> files/finances.xls\n    currentPath != null && currentPath.endsWith(\"*\") && currentMatch.params[\"*\"] !== match.params[\"*\"]\n  );\n}\nfunction shouldRevalidateLoader(loaderMatch, arg) {\n  if (loaderMatch.route.shouldRevalidate) {\n    let routeChoice = loaderMatch.route.shouldRevalidate(arg);\n    if (typeof routeChoice === \"boolean\") {\n      return routeChoice;\n    }\n  }\n  return arg.defaultShouldRevalidate;\n}\n/**\n * Execute route.lazy() methods to lazily load route modules (loader, action,\n * shouldRevalidate) and update the routeManifest in place which shares objects\n * with dataRoutes so those get updated as well.\n */\nasync function loadLazyRouteModule(route, mapRouteProperties, manifest) {\n  if (!route.lazy) {\n    return;\n  }\n  let lazyRoute = await route.lazy();\n  // If the lazy route function was executed and removed by another parallel\n  // call then we can return - first lazy() to finish wins because the return\n  // value of lazy is expected to be static\n  if (!route.lazy) {\n    return;\n  }\n  let routeToUpdate = manifest[route.id];\n  invariant(routeToUpdate, \"No route found in manifest\");\n  // Update the route in place.  This should be safe because there's no way\n  // we could yet be sitting on this route as we can't get there without\n  // resolving lazy() first.\n  //\n  // This is different than the HMR \"update\" use-case where we may actively be\n  // on the route being updated.  The main concern boils down to \"does this\n  // mutation affect any ongoing navigations or any current state.matches\n  // values?\".  If not, it should be safe to update in place.\n  let routeUpdates = {};\n  for (let lazyRouteProperty in lazyRoute) {\n    let staticRouteValue = routeToUpdate[lazyRouteProperty];\n    let isPropertyStaticallyDefined = staticRouteValue !== undefined &&\n    // This property isn't static since it should always be updated based\n    // on the route updates\n    lazyRouteProperty !== \"hasErrorBoundary\";\n    warning(!isPropertyStaticallyDefined, \"Route \\\"\" + routeToUpdate.id + \"\\\" has a static property \\\"\" + lazyRouteProperty + \"\\\" \" + \"defined but its lazy function is also returning a value for this property. \" + (\"The lazy route property \\\"\" + lazyRouteProperty + \"\\\" will be ignored.\"));\n    if (!isPropertyStaticallyDefined && !immutableRouteKeys.has(lazyRouteProperty)) {\n      routeUpdates[lazyRouteProperty] = lazyRoute[lazyRouteProperty];\n    }\n  }\n  // Mutate the route with the provided updates.  Do this first so we pass\n  // the updated version to mapRouteProperties\n  Object.assign(routeToUpdate, routeUpdates);\n  // Mutate the `hasErrorBoundary` property on the route based on the route\n  // updates and remove the `lazy` function so we don't resolve the lazy\n  // route again.\n  Object.assign(routeToUpdate, _extends({}, mapRouteProperties(routeToUpdate), {\n    lazy: undefined\n  }));\n}\nasync function callLoaderOrAction(type, request, match, matches, manifest, mapRouteProperties, basename, opts) {\n  if (opts === void 0) {\n    opts = {};\n  }\n  let resultType;\n  let result;\n  let onReject;\n  let runHandler = handler => {\n    // Setup a promise we can race against so that abort signals short circuit\n    let reject;\n    let abortPromise = new Promise((_, r) => reject = r);\n    onReject = () => reject();\n    request.signal.addEventListener(\"abort\", onReject);\n    return Promise.race([handler({\n      request,\n      params: match.params,\n      context: opts.requestContext\n    }), abortPromise]);\n  };\n  try {\n    let handler = match.route[type];\n    if (match.route.lazy) {\n      if (handler) {\n        // Run statically defined handler in parallel with lazy()\n        let values = await Promise.all([runHandler(handler), loadLazyRouteModule(match.route, mapRouteProperties, manifest)]);\n        result = values[0];\n      } else {\n        // Load lazy route module, then run any returned handler\n        await loadLazyRouteModule(match.route, mapRouteProperties, manifest);\n        handler = match.route[type];\n        if (handler) {\n          // Handler still run even if we got interrupted to maintain consistency\n          // with un-abortable behavior of handler execution on non-lazy or\n          // previously-lazy-loaded routes\n          result = await runHandler(handler);\n        } else if (type === \"action\") {\n          let url = new URL(request.url);\n          let pathname = url.pathname + url.search;\n          throw getInternalRouterError(405, {\n            method: request.method,\n            pathname,\n            routeId: match.route.id\n          });\n        } else {\n          // lazy() route has no loader to run.  Short circuit here so we don't\n          // hit the invariant below that errors on returning undefined.\n          return {\n            type: ResultType.data,\n            data: undefined\n          };\n        }\n      }\n    } else if (!handler) {\n      let url = new URL(request.url);\n      let pathname = url.pathname + url.search;\n      throw getInternalRouterError(404, {\n        pathname\n      });\n    } else {\n      result = await runHandler(handler);\n    }\n    invariant(result !== undefined, \"You defined \" + (type === \"action\" ? \"an action\" : \"a loader\") + \" for route \" + (\"\\\"\" + match.route.id + \"\\\" but didn't return anything from your `\" + type + \"` \") + \"function. Please return a value or `null`.\");\n  } catch (e) {\n    resultType = ResultType.error;\n    result = e;\n  } finally {\n    if (onReject) {\n      request.signal.removeEventListener(\"abort\", onReject);\n    }\n  }\n  if (isResponse(result)) {\n    let status = result.status;\n    // Process redirects\n    if (redirectStatusCodes.has(status)) {\n      let location = result.headers.get(\"Location\");\n      invariant(location, \"Redirects returned/thrown from loaders/actions must have a Location header\");\n      // Support relative routing in internal redirects\n      if (!ABSOLUTE_URL_REGEX.test(location)) {\n        location = normalizeTo(new URL(request.url), matches.slice(0, matches.indexOf(match) + 1), basename, true, location);\n      } else if (!opts.isStaticRequest) {\n        // Strip off the protocol+origin for same-origin + same-basename absolute\n        // redirects. If this is a static request, we can let it go back to the\n        // browser as-is\n        let currentUrl = new URL(request.url);\n        let url = location.startsWith(\"//\") ? new URL(currentUrl.protocol + location) : new URL(location);\n        let isSameBasename = stripBasename(url.pathname, basename) != null;\n        if (url.origin === currentUrl.origin && isSameBasename) {\n          location = url.pathname + url.search + url.hash;\n        }\n      }\n      // Don't process redirects in the router during static requests requests.\n      // Instead, throw the Response and let the server handle it with an HTTP\n      // redirect.  We also update the Location header in place in this flow so\n      // basename and relative routing is taken into account\n      if (opts.isStaticRequest) {\n        result.headers.set(\"Location\", location);\n        throw result;\n      }\n      return {\n        type: ResultType.redirect,\n        status,\n        location,\n        revalidate: result.headers.get(\"X-Remix-Revalidate\") !== null\n      };\n    }\n    // For SSR single-route requests, we want to hand Responses back directly\n    // without unwrapping.  We do this with the QueryRouteResponse wrapper\n    // interface so we can know whether it was returned or thrown\n    if (opts.isRouteRequest) {\n      // eslint-disable-next-line no-throw-literal\n      throw {\n        type: resultType || ResultType.data,\n        response: result\n      };\n    }\n    let data;\n    let contentType = result.headers.get(\"Content-Type\");\n    // Check between word boundaries instead of startsWith() due to the last\n    // paragraph of https://httpwg.org/specs/rfc9110.html#field.content-type\n    if (contentType && /\\bapplication\\/json\\b/.test(contentType)) {\n      data = await result.json();\n    } else {\n      data = await result.text();\n    }\n    if (resultType === ResultType.error) {\n      return {\n        type: resultType,\n        error: new ErrorResponse(status, result.statusText, data),\n        headers: result.headers\n      };\n    }\n    return {\n      type: ResultType.data,\n      data,\n      statusCode: result.status,\n      headers: result.headers\n    };\n  }\n  if (resultType === ResultType.error) {\n    return {\n      type: resultType,\n      error: result\n    };\n  }\n  if (isDeferredData(result)) {\n    var _result$init, _result$init2;\n    return {\n      type: ResultType.deferred,\n      deferredData: result,\n      statusCode: (_result$init = result.init) == null ? void 0 : _result$init.status,\n      headers: ((_result$init2 = result.init) == null ? void 0 : _result$init2.headers) && new Headers(result.init.headers)\n    };\n  }\n  return {\n    type: ResultType.data,\n    data: result\n  };\n}\n// Utility method for creating the Request instances for loaders/actions during\n// client-side navigations and fetches.  During SSR we will always have a\n// Request instance from the static handler (query/queryRoute)\nfunction createClientSideRequest(history, location, signal, submission) {\n  let url = history.createURL(stripHashFromPath(location)).toString();\n  let init = {\n    signal\n  };\n  if (submission && isMutationMethod(submission.formMethod)) {\n    let {\n      formMethod,\n      formEncType\n    } = submission;\n    // Didn't think we needed this but it turns out unlike other methods, patch\n    // won't be properly normalized to uppercase and results in a 405 error.\n    // See: https://fetch.spec.whatwg.org/#concept-method\n    init.method = formMethod.toUpperCase();\n    if (formEncType === \"application/json\") {\n      init.headers = new Headers({\n        \"Content-Type\": formEncType\n      });\n      init.body = JSON.stringify(submission.json);\n    } else if (formEncType === \"text/plain\") {\n      // Content-Type is inferred (https://fetch.spec.whatwg.org/#dom-request)\n      init.body = submission.text;\n    } else if (formEncType === \"application/x-www-form-urlencoded\" && submission.formData) {\n      // Content-Type is inferred (https://fetch.spec.whatwg.org/#dom-request)\n      init.body = convertFormDataToSearchParams(submission.formData);\n    } else {\n      // Content-Type is inferred (https://fetch.spec.whatwg.org/#dom-request)\n      init.body = submission.formData;\n    }\n  }\n  return new Request(url, init);\n}\nfunction convertFormDataToSearchParams(formData) {\n  let searchParams = new URLSearchParams();\n  for (let [key, value] of formData.entries()) {\n    // https://html.spec.whatwg.org/multipage/form-control-infrastructure.html#converting-an-entry-list-to-a-list-of-name-value-pairs\n    searchParams.append(key, typeof value === \"string\" ? value : value.name);\n  }\n  return searchParams;\n}\nfunction convertSearchParamsToFormData(searchParams) {\n  let formData = new FormData();\n  for (let [key, value] of searchParams.entries()) {\n    formData.append(key, value);\n  }\n  return formData;\n}\nfunction processRouteLoaderData(matches, matchesToLoad, results, pendingError, activeDeferreds) {\n  // Fill in loaderData/errors from our loaders\n  let loaderData = {};\n  let errors = null;\n  let statusCode;\n  let foundError = false;\n  let loaderHeaders = {};\n  // Process loader results into state.loaderData/state.errors\n  results.forEach((result, index) => {\n    let id = matchesToLoad[index].route.id;\n    invariant(!isRedirectResult(result), \"Cannot handle redirect results in processLoaderData\");\n    if (isErrorResult(result)) {\n      // Look upwards from the matched route for the closest ancestor\n      // error boundary, defaulting to the root match\n      let boundaryMatch = findNearestBoundary(matches, id);\n      let error = result.error;\n      // If we have a pending action error, we report it at the highest-route\n      // that throws a loader error, and then clear it out to indicate that\n      // it was consumed\n      if (pendingError) {\n        error = Object.values(pendingError)[0];\n        pendingError = undefined;\n      }\n      errors = errors || {};\n      // Prefer higher error values if lower errors bubble to the same boundary\n      if (errors[boundaryMatch.route.id] == null) {\n        errors[boundaryMatch.route.id] = error;\n      }\n      // Clear our any prior loaderData for the throwing route\n      loaderData[id] = undefined;\n      // Once we find our first (highest) error, we set the status code and\n      // prevent deeper status codes from overriding\n      if (!foundError) {\n        foundError = true;\n        statusCode = isRouteErrorResponse(result.error) ? result.error.status : 500;\n      }\n      if (result.headers) {\n        loaderHeaders[id] = result.headers;\n      }\n    } else {\n      if (isDeferredResult(result)) {\n        activeDeferreds.set(id, result.deferredData);\n        loaderData[id] = result.deferredData.data;\n      } else {\n        loaderData[id] = result.data;\n      }\n      // Error status codes always override success status codes, but if all\n      // loaders are successful we take the deepest status code.\n      if (result.statusCode != null && result.statusCode !== 200 && !foundError) {\n        statusCode = result.statusCode;\n      }\n      if (result.headers) {\n        loaderHeaders[id] = result.headers;\n      }\n    }\n  });\n  // If we didn't consume the pending action error (i.e., all loaders\n  // resolved), then consume it here.  Also clear out any loaderData for the\n  // throwing route\n  if (pendingError) {\n    errors = pendingError;\n    loaderData[Object.keys(pendingError)[0]] = undefined;\n  }\n  return {\n    loaderData,\n    errors,\n    statusCode: statusCode || 200,\n    loaderHeaders\n  };\n}\nfunction processLoaderData(state, matches, matchesToLoad, results, pendingError, revalidatingFetchers, fetcherResults, activeDeferreds) {\n  let {\n    loaderData,\n    errors\n  } = processRouteLoaderData(matches, matchesToLoad, results, pendingError, activeDeferreds);\n  // Process results from our revalidating fetchers\n  for (let index = 0; index < revalidatingFetchers.length; index++) {\n    let {\n      key,\n      match,\n      controller\n    } = revalidatingFetchers[index];\n    invariant(fetcherResults !== undefined && fetcherResults[index] !== undefined, \"Did not find corresponding fetcher result\");\n    let result = fetcherResults[index];\n    // Process fetcher non-redirect errors\n    if (controller && controller.signal.aborted) {\n      // Nothing to do for aborted fetchers\n      continue;\n    } else if (isErrorResult(result)) {\n      let boundaryMatch = findNearestBoundary(state.matches, match == null ? void 0 : match.route.id);\n      if (!(errors && errors[boundaryMatch.route.id])) {\n        errors = _extends({}, errors, {\n          [boundaryMatch.route.id]: result.error\n        });\n      }\n      state.fetchers.delete(key);\n    } else if (isRedirectResult(result)) {\n      // Should never get here, redirects should get processed above, but we\n      // keep this to type narrow to a success result in the else\n      invariant(false, \"Unhandled fetcher revalidation redirect\");\n    } else if (isDeferredResult(result)) {\n      // Should never get here, deferred data should be awaited for fetchers\n      // in resolveDeferredResults\n      invariant(false, \"Unhandled fetcher deferred data\");\n    } else {\n      let doneFetcher = getDoneFetcher(result.data);\n      state.fetchers.set(key, doneFetcher);\n    }\n  }\n  return {\n    loaderData,\n    errors\n  };\n}\nfunction mergeLoaderData(loaderData, newLoaderData, matches, errors) {\n  let mergedLoaderData = _extends({}, newLoaderData);\n  for (let match of matches) {\n    let id = match.route.id;\n    if (newLoaderData.hasOwnProperty(id)) {\n      if (newLoaderData[id] !== undefined) {\n        mergedLoaderData[id] = newLoaderData[id];\n      }\n    } else if (loaderData[id] !== undefined && match.route.loader) {\n      // Preserve existing keys not included in newLoaderData and where a loader\n      // wasn't removed by HMR\n      mergedLoaderData[id] = loaderData[id];\n    }\n    if (errors && errors.hasOwnProperty(id)) {\n      // Don't keep any loader data below the boundary\n      break;\n    }\n  }\n  return mergedLoaderData;\n}\n// Find the nearest error boundary, looking upwards from the leaf route (or the\n// route specified by routeId) for the closest ancestor error boundary,\n// defaulting to the root match\nfunction findNearestBoundary(matches, routeId) {\n  let eligibleMatches = routeId ? matches.slice(0, matches.findIndex(m => m.route.id === routeId) + 1) : [...matches];\n  return eligibleMatches.reverse().find(m => m.route.hasErrorBoundary === true) || matches[0];\n}\nfunction getShortCircuitMatches(routes) {\n  // Prefer a root layout route if present, otherwise shim in a route object\n  let route = routes.find(r => r.index || !r.path || r.path === \"/\") || {\n    id: \"__shim-error-route__\"\n  };\n  return {\n    matches: [{\n      params: {},\n      pathname: \"\",\n      pathnameBase: \"\",\n      route\n    }],\n    route\n  };\n}\nfunction getInternalRouterError(status, _temp4) {\n  let {\n    pathname,\n    routeId,\n    method,\n    type\n  } = _temp4 === void 0 ? {} : _temp4;\n  let statusText = \"Unknown Server Error\";\n  let errorMessage = \"Unknown @remix-run/router error\";\n  if (status === 400) {\n    statusText = \"Bad Request\";\n    if (method && pathname && routeId) {\n      errorMessage = \"You made a \" + method + \" request to \\\"\" + pathname + \"\\\" but \" + (\"did not provide a `loader` for route \\\"\" + routeId + \"\\\", \") + \"so there is no way to handle the request.\";\n    } else if (type === \"defer-action\") {\n      errorMessage = \"defer() is not supported in actions\";\n    } else if (type === \"invalid-body\") {\n      errorMessage = \"Unable to encode submission body\";\n    }\n  } else if (status === 403) {\n    statusText = \"Forbidden\";\n    errorMessage = \"Route \\\"\" + routeId + \"\\\" does not match URL \\\"\" + pathname + \"\\\"\";\n  } else if (status === 404) {\n    statusText = \"Not Found\";\n    errorMessage = \"No route matches URL \\\"\" + pathname + \"\\\"\";\n  } else if (status === 405) {\n    statusText = \"Method Not Allowed\";\n    if (method && pathname && routeId) {\n      errorMessage = \"You made a \" + method.toUpperCase() + \" request to \\\"\" + pathname + \"\\\" but \" + (\"did not provide an `action` for route \\\"\" + routeId + \"\\\", \") + \"so there is no way to handle the request.\";\n    } else if (method) {\n      errorMessage = \"Invalid request method \\\"\" + method.toUpperCase() + \"\\\"\";\n    }\n  }\n  return new ErrorResponse(status || 500, statusText, new Error(errorMessage), true);\n}\n// Find any returned redirect errors, starting from the lowest match\nfunction findRedirect(results) {\n  for (let i = results.length - 1; i >= 0; i--) {\n    let result = results[i];\n    if (isRedirectResult(result)) {\n      return {\n        result,\n        idx: i\n      };\n    }\n  }\n}\nfunction stripHashFromPath(path) {\n  let parsedPath = typeof path === \"string\" ? parsePath(path) : path;\n  return createPath(_extends({}, parsedPath, {\n    hash: \"\"\n  }));\n}\nfunction isHashChangeOnly(a, b) {\n  if (a.pathname !== b.pathname || a.search !== b.search) {\n    return false;\n  }\n  if (a.hash === \"\") {\n    // /page -> /page#hash\n    return b.hash !== \"\";\n  } else if (a.hash === b.hash) {\n    // /page#hash -> /page#hash\n    return true;\n  } else if (b.hash !== \"\") {\n    // /page#hash -> /page#other\n    return true;\n  }\n  // If the hash is removed the browser will re-perform a request to the server\n  // /page#hash -> /page\n  return false;\n}\nfunction isDeferredResult(result) {\n  return result.type === ResultType.deferred;\n}\nfunction isErrorResult(result) {\n  return result.type === ResultType.error;\n}\nfunction isRedirectResult(result) {\n  return (result && result.type) === ResultType.redirect;\n}\nfunction isDeferredData(value) {\n  let deferred = value;\n  return deferred && typeof deferred === \"object\" && typeof deferred.data === \"object\" && typeof deferred.subscribe === \"function\" && typeof deferred.cancel === \"function\" && typeof deferred.resolveData === \"function\";\n}\nfunction isResponse(value) {\n  return value != null && typeof value.status === \"number\" && typeof value.statusText === \"string\" && typeof value.headers === \"object\" && typeof value.body !== \"undefined\";\n}\nfunction isRedirectResponse(result) {\n  if (!isResponse(result)) {\n    return false;\n  }\n  let status = result.status;\n  let location = result.headers.get(\"Location\");\n  return status >= 300 && status <= 399 && location != null;\n}\nfunction isQueryRouteResponse(obj) {\n  return obj && isResponse(obj.response) && (obj.type === ResultType.data || ResultType.error);\n}\nfunction isValidMethod(method) {\n  return validRequestMethods.has(method.toLowerCase());\n}\nfunction isMutationMethod(method) {\n  return validMutationMethods.has(method.toLowerCase());\n}\nasync function resolveDeferredResults(currentMatches, matchesToLoad, results, signals, isFetcher, currentLoaderData) {\n  for (let index = 0; index < results.length; index++) {\n    let result = results[index];\n    let match = matchesToLoad[index];\n    // If we don't have a match, then we can have a deferred result to do\n    // anything with.  This is for revalidating fetchers where the route was\n    // removed during HMR\n    if (!match) {\n      continue;\n    }\n    let currentMatch = currentMatches.find(m => m.route.id === match.route.id);\n    let isRevalidatingLoader = currentMatch != null && !isNewRouteInstance(currentMatch, match) && (currentLoaderData && currentLoaderData[match.route.id]) !== undefined;\n    if (isDeferredResult(result) && (isFetcher || isRevalidatingLoader)) {\n      // Note: we do not have to touch activeDeferreds here since we race them\n      // against the signal in resolveDeferredData and they'll get aborted\n      // there if needed\n      let signal = signals[index];\n      invariant(signal, \"Expected an AbortSignal for revalidating fetcher deferred result\");\n      await resolveDeferredData(result, signal, isFetcher).then(result => {\n        if (result) {\n          results[index] = result || results[index];\n        }\n      });\n    }\n  }\n}\nasync function resolveDeferredData(result, signal, unwrap) {\n  if (unwrap === void 0) {\n    unwrap = false;\n  }\n  let aborted = await result.deferredData.resolveData(signal);\n  if (aborted) {\n    return;\n  }\n  if (unwrap) {\n    try {\n      return {\n        type: ResultType.data,\n        data: result.deferredData.unwrappedData\n      };\n    } catch (e) {\n      // Handle any TrackedPromise._error values encountered while unwrapping\n      return {\n        type: ResultType.error,\n        error: e\n      };\n    }\n  }\n  return {\n    type: ResultType.data,\n    data: result.deferredData.data\n  };\n}\nfunction hasNakedIndexQuery(search) {\n  return new URLSearchParams(search).getAll(\"index\").some(v => v === \"\");\n}\n// Note: This should match the format exported by useMatches, so if you change\n// this please also change that :)  Eventually we'll DRY this up\nfunction createUseMatchesMatch(match, loaderData) {\n  let {\n    route,\n    pathname,\n    params\n  } = match;\n  return {\n    id: route.id,\n    pathname,\n    params,\n    data: loaderData[route.id],\n    handle: route.handle\n  };\n}\nfunction getTargetMatch(matches, location) {\n  let search = typeof location === \"string\" ? parsePath(location).search : location.search;\n  if (matches[matches.length - 1].route.index && hasNakedIndexQuery(search || \"\")) {\n    // Return the leaf index route when index is present\n    return matches[matches.length - 1];\n  }\n  // Otherwise grab the deepest \"path contributing\" match (ignoring index and\n  // pathless layout routes)\n  let pathMatches = getPathContributingMatches(matches);\n  return pathMatches[pathMatches.length - 1];\n}\nfunction getSubmissionFromNavigation(navigation) {\n  let {\n    formMethod,\n    formAction,\n    formEncType,\n    text,\n    formData,\n    json\n  } = navigation;\n  if (!formMethod || !formAction || !formEncType) {\n    return;\n  }\n  if (text != null) {\n    return {\n      formMethod,\n      formAction,\n      formEncType,\n      formData: undefined,\n      json: undefined,\n      text\n    };\n  } else if (formData != null) {\n    return {\n      formMethod,\n      formAction,\n      formEncType,\n      formData,\n      json: undefined,\n      text: undefined\n    };\n  } else if (json !== undefined) {\n    return {\n      formMethod,\n      formAction,\n      formEncType,\n      formData: undefined,\n      json,\n      text: undefined\n    };\n  }\n}\nfunction getLoadingNavigation(location, submission) {\n  if (submission) {\n    let navigation = {\n      state: \"loading\",\n      location,\n      formMethod: submission.formMethod,\n      formAction: submission.formAction,\n      formEncType: submission.formEncType,\n      formData: submission.formData,\n      json: submission.json,\n      text: submission.text\n    };\n    return navigation;\n  } else {\n    let navigation = {\n      state: \"loading\",\n      location,\n      formMethod: undefined,\n      formAction: undefined,\n      formEncType: undefined,\n      formData: undefined,\n      json: undefined,\n      text: undefined\n    };\n    return navigation;\n  }\n}\nfunction getSubmittingNavigation(location, submission) {\n  let navigation = {\n    state: \"submitting\",\n    location,\n    formMethod: submission.formMethod,\n    formAction: submission.formAction,\n    formEncType: submission.formEncType,\n    formData: submission.formData,\n    json: submission.json,\n    text: submission.text\n  };\n  return navigation;\n}\nfunction getLoadingFetcher(submission, data) {\n  if (submission) {\n    let fetcher = {\n      state: \"loading\",\n      formMethod: submission.formMethod,\n      formAction: submission.formAction,\n      formEncType: submission.formEncType,\n      formData: submission.formData,\n      json: submission.json,\n      text: submission.text,\n      data,\n      \" _hasFetcherDoneAnything \": true\n    };\n    return fetcher;\n  } else {\n    let fetcher = {\n      state: \"loading\",\n      formMethod: undefined,\n      formAction: undefined,\n      formEncType: undefined,\n      formData: undefined,\n      json: undefined,\n      text: undefined,\n      data,\n      \" _hasFetcherDoneAnything \": true\n    };\n    return fetcher;\n  }\n}\nfunction getSubmittingFetcher(submission, existingFetcher) {\n  let fetcher = {\n    state: \"submitting\",\n    formMethod: submission.formMethod,\n    formAction: submission.formAction,\n    formEncType: submission.formEncType,\n    formData: submission.formData,\n    json: submission.json,\n    text: submission.text,\n    data: existingFetcher ? existingFetcher.data : undefined,\n    \" _hasFetcherDoneAnything \": true\n  };\n  return fetcher;\n}\nfunction getDoneFetcher(data) {\n  let fetcher = {\n    state: \"idle\",\n    formMethod: undefined,\n    formAction: undefined,\n    formEncType: undefined,\n    formData: undefined,\n    json: undefined,\n    text: undefined,\n    data,\n    \" _hasFetcherDoneAnything \": true\n  };\n  return fetcher;\n}\n//#endregion\n\nexport { AbortedDeferredError, Action, ErrorResponse, IDLE_BLOCKER, IDLE_FETCHER, IDLE_NAVIGATION, UNSAFE_DEFERRED_SYMBOL, DeferredData as UNSAFE_DeferredData, convertRoutesToDataRoutes as UNSAFE_convertRoutesToDataRoutes, getPathContributingMatches as UNSAFE_getPathContributingMatches, invariant as UNSAFE_invariant, warning as UNSAFE_warning, createBrowserHistory, createHashHistory, createMemoryHistory, createPath, createRouter, createStaticHandler, defer, generatePath, getStaticContextFromError, getToPathname, isDeferredData, isRouteErrorResponse, joinPaths, json, matchPath, matchRoutes, normalizePathname, parsePath, redirect, resolvePath, resolveTo, stripBasename };", "map": {"version": 3, "names": ["Action", "PopStateEventType", "createMemoryHistory", "options", "initialEntries", "initialIndex", "v5Compat", "entries", "map", "entry", "index", "createMemoryLocation", "state", "undefined", "clampIndex", "length", "action", "Pop", "listener", "n", "Math", "min", "max", "getCurrentLocation", "to", "key", "location", "createLocation", "pathname", "warning", "char<PERSON>t", "JSON", "stringify", "createHref", "createPath", "history", "createURL", "URL", "encodeLocation", "path", "parsePath", "search", "hash", "push", "<PERSON><PERSON>", "nextLocation", "splice", "delta", "replace", "Replace", "go", "nextIndex", "listen", "fn", "createBrowserHistory", "createBrowserLocation", "window", "globalHistory", "usr", "createBrowserHref", "getUrlBasedHistory", "createHashHistory", "createHashLocation", "substr", "createHashHref", "base", "document", "querySelector", "href", "getAttribute", "url", "hashIndex", "indexOf", "slice", "validateHashLocation", "invariant", "value", "message", "Error", "cond", "console", "warn", "e", "create<PERSON><PERSON>", "random", "toString", "getHistoryState", "idx", "current", "_extends", "_ref", "parsed<PERSON><PERSON>", "searchIndex", "getLocation", "validateLocation", "defaultView", "getIndex", "replaceState", "handlePop", "historyState", "pushState", "error", "DOMException", "name", "assign", "origin", "addEventListener", "removeEventListener", "ResultType", "immutableRouteKeys", "Set", "isIndexRoute", "route", "convertRoutesToDataRoutes", "routes", "mapRouteProperties", "parentPath", "manifest", "treePath", "id", "join", "children", "indexRoute", "pathOrLayoutRoute", "matchRoutes", "locationArg", "basename", "stripBasename", "branches", "flattenRoutes", "rankRouteBranches", "matches", "i", "matchRouteBranch", "safelyDecodeURI", "parents<PERSON>eta", "flattenRoute", "relativePath", "meta", "caseSensitive", "childrenIndex", "startsWith", "joinPaths", "routesMeta", "concat", "score", "computeScore", "for<PERSON>ach", "_route$path", "includes", "exploded", "explodeOptionalSegments", "segments", "split", "first", "rest", "isOptional", "endsWith", "required", "restExploded", "result", "subpath", "sort", "a", "b", "compareIndexes", "paramRe", "dynamicSegmentValue", "indexRouteValue", "emptySegmentValue", "staticSegmentValue", "splatPenalty", "isSplat", "s", "initialScore", "some", "filter", "reduce", "segment", "test", "siblings", "every", "branch", "matchedParams", "matchedPathname", "end", "remainingPathname", "match", "matchPath", "Object", "params", "pathnameBase", "normalizePathname", "generatePath", "originalPath", "prefix", "p", "String", "array", "isLastSegment", "star", "keyMatch", "optional", "param", "pattern", "matcher", "paramNames", "compilePath", "captureGroups", "memo", "paramName", "splatValue", "safelyDecodeURIComponent", "regexpSource", "_", "RegExp", "decodeURI", "decodeURIComponent", "toLowerCase", "startIndex", "nextChar", "<PERSON><PERSON><PERSON>", "fromPathname", "toPathname", "resolvePathname", "normalizeSearch", "normalizeHash", "relativeSegments", "pop", "getInvalidPathError", "char", "field", "dest", "getPathContributingMatches", "resolveTo", "to<PERSON><PERSON>", "routePathnames", "locationPathname", "isPathRelative", "isEmptyPath", "from", "routePathnameIndex", "toSegments", "shift", "hasExplicitTrailingSlash", "hasCurrentTrailingSlash", "getToPathname", "paths", "json", "data", "init", "responseInit", "status", "headers", "Headers", "has", "set", "Response", "Aborted<PERSON>eferredError", "DeferredData", "constructor", "pendingKeysSet", "subscribers", "deferred<PERSON><PERSON><PERSON>", "Array", "isArray", "reject", "abortPromise", "Promise", "r", "controller", "AbortController", "onAbort", "unlistenAbortSignal", "signal", "acc", "trackPromise", "done", "add", "promise", "race", "then", "onSettle", "catch", "defineProperty", "get", "aborted", "delete", "undefinedError", "emit", "<PERSON><PERSON><PERSON>", "subscriber", "subscribe", "cancel", "abort", "v", "k", "resolveData", "resolve", "size", "unwrappedData", "_ref2", "unwrapTrackedPromise", "<PERSON><PERSON><PERSON><PERSON>", "isTrackedPromise", "_tracked", "_error", "_data", "defer", "redirect", "ErrorResponse", "statusText", "internal", "isRouteErrorResponse", "validMutationMethodsArr", "validMutationMethods", "validRequestMethodsArr", "validRequestMethods", "redirectStatusCodes", "redirectPreserveMethodStatusCodes", "IDLE_NAVIGATION", "formMethod", "formAction", "formEncType", "formData", "text", "IDLE_FETCHER", "IDLE_BLOCKER", "proceed", "reset", "ABSOLUTE_URL_REGEX", "defaultMapRouteProperties", "hasErrorBou<PERSON>ry", "Boolean", "createRouter", "routerWindow", "<PERSON><PERSON><PERSON><PERSON>", "createElement", "isServer", "detectErrorBoundary", "dataRoutes", "inFlightDataRoutes", "future", "v7_normalizeFormMethod", "v7_prependBasename", "unlistenHistory", "savedScrollPositions", "getScrollRestorationKey", "getScrollPosition", "initialScrollRestored", "hydrationData", "initialMatches", "initialErrors", "getInternalRouterError", "getShortCircuitMatches", "initialized", "m", "lazy", "loader", "router", "historyAction", "navigation", "restoreScrollPosition", "preventScrollReset", "revalidation", "loaderData", "actionData", "errors", "fetchers", "Map", "blockers", "pendingAction", "pendingPreventScrollReset", "pendingNavigationController", "isUninterruptedRevalidation", "isRevalidationRequired", "cancelledDeferredRoutes", "cancelledFetcherLoads", "fetchControllers", "incrementingLoadId", "pendingNavigationLoadId", "fetchReloadIds", "fetchRedirectIds", "fetchLoadMatches", "activeDeferreds", "blockerFunctions", "ignoreNextHistoryUpdate", "initialize", "blockerKey", "shouldBlockNavigation", "currentLocation", "updateBlocker", "updateState", "startNavigation", "dispose", "clear", "deleteFetcher", "deleteBlocker", "newState", "completeNavigation", "_location$state", "_location$state2", "isActionReload", "isMutationMethod", "_isRedirect", "keys", "mergeLoaderData", "getSavedScrollPosition", "navigate", "opts", "normalizedPath", "normalizeTo", "fromRouteId", "relative", "submission", "normalizeNavigateOptions", "userReplace", "pendingError", "revalidate", "interruptActiveLoads", "startUninterruptedRevalidation", "overrideNavigation", "saveScrollPosition", "routesToUse", "loadingNavigation", "notFoundMatches", "cancelActiveDeferreds", "isHashChangeOnly", "request", "createClientSideRequest", "pendingActionData", "findNearestBoundary", "actionOutput", "handleAction", "shortCircuited", "pendingActionError", "getLoadingNavigation", "Request", "handleLoaders", "fetcherSubmission", "getSubmittingNavigation", "actionMatch", "getTargetMatch", "type", "method", "routeId", "callLoaderOrAction", "isRedirectResult", "startRedirectNavigation", "isErrorResult", "boundaryMatch", "isDeferredResult", "activeSubmission", "getSubmissionFromNavigation", "matchesToLoad", "revalidatingFetchers", "getMatchesToLoad", "updatedFetchers", "markFetchRedirectsDone", "rf", "fetcher", "revalidatingFetcher", "getLoadingFetcher", "abort<PERSON><PERSON><PERSON>", "abortPendingFetchRevalidations", "f", "results", "loaderResults", "fetcherResults", "callLoadersAndMaybeResolveData", "findRedirect", "fetcher<PERSON>ey", "processLoaderData", "deferredData", "didAbortFetchLoads", "abortStaleFetchLoads", "shouldUpdateFetchers", "getFetcher", "fetch", "setFetcherError", "handleFetcherAction", "handleFetcherLoader", "requestMatches", "existingFetcher", "getSubmittingFetcher", "abortController", "fetchRequest", "originatingLoadId", "actionResult", "done<PERSON>etcher", "getDoneFetcher", "loadingFetcher", "isFetchActionRedirect", "revalidationRequest", "loadId", "loadFetcher", "staleKey", "resolveDeferredData", "_temp", "redirectLocation", "_isFetchActionRedirect", "isDifferentBasename", "redirectHistoryAction", "currentMatches", "fetchersToLoad", "all", "resolveDeferredResults", "markFetchersDone", "done<PERSON><PERSON><PERSON>", "landedId", "yeeted<PERSON><PERSON>s", "get<PERSON><PERSON>er", "blocker", "newBlocker", "blockerFunction", "predicate", "cancelledRouteIds", "dfd", "enableScrollRestoration", "positions", "getPosition", "<PERSON><PERSON><PERSON>", "y", "getScrollKey", "createUseMatchesMatch", "_internalSetRoutes", "newRoutes", "_internalFetchControllers", "_internalActiveDeferreds", "UNSAFE_DEFERRED_SYMBOL", "Symbol", "createStaticHandler", "query", "_temp2", "requestContext", "isValidMethod", "methodNotAllowedMatches", "statusCode", "loaderHeaders", "actionHeaders", "queryImpl", "isResponse", "queryRoute", "_temp3", "find", "values", "_result$activeDeferre", "routeMatch", "submit", "loadRouteData", "isQueryRouteResponse", "isRedirectResponse", "response", "isRouteRequest", "isStaticRequest", "Location", "context", "loaderRequest", "getLoaderMatchesUntilBoundary", "processRouteLoaderData", "executedLoaders", "fromEntries", "getStaticContextFromError", "newContext", "_deepestRenderedBoundaryId", "isSubmissionNavigation", "body", "prependBasename", "contextualMatches", "activeRouteMatch", "hasNakedIndexQuery", "normalizeFormMethod", "isFetcher", "getInvalidBodyError", "rawFormMethod", "toUpperCase", "stripHashFromPath", "FormData", "URLSearchParams", "_ref3", "parse", "searchParams", "convertFormDataToSearchParams", "convertSearchParamsToFormData", "append", "boundaryId", "boundaryMatches", "findIndex", "currentUrl", "nextUrl", "navigationMatches", "is<PERSON>ew<PERSON><PERSON>der", "currentRouteMatch", "nextRouteMatch", "shouldRevalidateLoader", "currentParams", "nextParams", "defaultShouldRevalidate", "isNewRouteInstance", "fetcherMatches", "fetcherMatch", "shouldRevalidate", "currentLoaderData", "currentMatch", "isNew", "isMissingData", "currentPath", "loaderMatch", "arg", "routeChoice", "loadLazyRouteModule", "lazyRoute", "routeToUpdate", "routeUpdates", "lazyRouteProperty", "staticRouteValue", "isPropertyStaticallyDefined", "resultType", "onReject", "<PERSON><PERSON><PERSON><PERSON>", "handler", "protocol", "isSameBasename", "contentType", "isDeferredData", "_result$init", "_result$init2", "deferred", "found<PERSON><PERSON>r", "newLoaderData", "mergedLoaderData", "hasOwnProperty", "eligibleMatches", "reverse", "_temp4", "errorMessage", "obj", "signals", "isRevalidatingLoader", "unwrap", "getAll", "handle", "pathMatches"], "sources": ["D:\\Projects\\practice\\bigcommerce-chatbot-app\\client\\node_modules\\@remix-run\\router\\history.ts", "D:\\Projects\\practice\\bigcommerce-chatbot-app\\client\\node_modules\\@remix-run\\router\\utils.ts", "D:\\Projects\\practice\\bigcommerce-chatbot-app\\client\\node_modules\\@remix-run\\router\\router.ts"], "sourcesContent": ["////////////////////////////////////////////////////////////////////////////////\n//#region Types and Constants\n////////////////////////////////////////////////////////////////////////////////\n\n/**\n * Actions represent the type of change to a location value.\n */\nexport enum Action {\n  /**\n   * A POP indicates a change to an arbitrary index in the history stack, such\n   * as a back or forward navigation. It does not describe the direction of the\n   * navigation, only that the current index changed.\n   *\n   * Note: This is the default action for newly created history objects.\n   */\n  Pop = \"POP\",\n\n  /**\n   * A PUSH indicates a new entry being added to the history stack, such as when\n   * a link is clicked and a new page loads. When this happens, all subsequent\n   * entries in the stack are lost.\n   */\n  Push = \"PUSH\",\n\n  /**\n   * A REPLACE indicates the entry at the current index in the history stack\n   * being replaced by a new one.\n   */\n  Replace = \"REPLACE\",\n}\n\n/**\n * The pathname, search, and hash values of a URL.\n */\nexport interface Path {\n  /**\n   * A URL pathname, beginning with a /.\n   */\n  pathname: string;\n\n  /**\n   * A URL search string, beginning with a ?.\n   */\n  search: string;\n\n  /**\n   * A URL fragment identifier, beginning with a #.\n   */\n  hash: string;\n}\n\n/**\n * An entry in a history stack. A location contains information about the\n * URL path, as well as possibly some arbitrary state and a key.\n */\nexport interface Location extends Path {\n  /**\n   * A value of arbitrary data associated with this location.\n   */\n  state: any;\n\n  /**\n   * A unique string associated with this location. May be used to safely store\n   * and retrieve data in some other storage API, like `localStorage`.\n   *\n   * Note: This value is always \"default\" on the initial location.\n   */\n  key: string;\n}\n\n/**\n * A change to the current location.\n */\nexport interface Update {\n  /**\n   * The action that triggered the change.\n   */\n  action: Action;\n\n  /**\n   * The new location.\n   */\n  location: Location;\n\n  /**\n   * The delta between this location and the former location in the history stack\n   */\n  delta: number | null;\n}\n\n/**\n * A function that receives notifications about location changes.\n */\nexport interface Listener {\n  (update: Update): void;\n}\n\n/**\n * Describes a location that is the destination of some navigation, either via\n * `history.push` or `history.replace`. May be either a URL or the pieces of a\n * URL path.\n */\nexport type To = string | Partial<Path>;\n\n/**\n * A history is an interface to the navigation stack. The history serves as the\n * source of truth for the current location, as well as provides a set of\n * methods that may be used to change it.\n *\n * It is similar to the DOM's `window.history` object, but with a smaller, more\n * focused API.\n */\nexport interface History {\n  /**\n   * The last action that modified the current location. This will always be\n   * Action.Pop when a history instance is first created. This value is mutable.\n   */\n  readonly action: Action;\n\n  /**\n   * The current location. This value is mutable.\n   */\n  readonly location: Location;\n\n  /**\n   * Returns a valid href for the given `to` value that may be used as\n   * the value of an <a href> attribute.\n   *\n   * @param to - The destination URL\n   */\n  createHref(to: To): string;\n\n  /**\n   * Returns a URL for the given `to` value\n   *\n   * @param to - The destination URL\n   */\n  createURL(to: To): URL;\n\n  /**\n   * Encode a location the same way window.history would do (no-op for memory\n   * history) so we ensure our PUSH/REPLACE navigations for data routers\n   * behave the same as POP\n   *\n   * @param to Unencoded path\n   */\n  encodeLocation(to: To): Path;\n\n  /**\n   * Pushes a new location onto the history stack, increasing its length by one.\n   * If there were any entries in the stack after the current one, they are\n   * lost.\n   *\n   * @param to - The new URL\n   * @param state - Data to associate with the new location\n   */\n  push(to: To, state?: any): void;\n\n  /**\n   * Replaces the current location in the history stack with a new one.  The\n   * location that was replaced will no longer be available.\n   *\n   * @param to - The new URL\n   * @param state - Data to associate with the new location\n   */\n  replace(to: To, state?: any): void;\n\n  /**\n   * Navigates `n` entries backward/forward in the history stack relative to the\n   * current index. For example, a \"back\" navigation would use go(-1).\n   *\n   * @param delta - The delta in the stack index\n   */\n  go(delta: number): void;\n\n  /**\n   * Sets up a listener that will be called whenever the current location\n   * changes.\n   *\n   * @param listener - A function that will be called when the location changes\n   * @returns unlisten - A function that may be used to stop listening\n   */\n  listen(listener: Listener): () => void;\n}\n\ntype HistoryState = {\n  usr: any;\n  key?: string;\n  idx: number;\n};\n\nconst PopStateEventType = \"popstate\";\n//#endregion\n\n////////////////////////////////////////////////////////////////////////////////\n//#region Memory History\n////////////////////////////////////////////////////////////////////////////////\n\n/**\n * A user-supplied object that describes a location. Used when providing\n * entries to `createMemoryHistory` via its `initialEntries` option.\n */\nexport type InitialEntry = string | Partial<Location>;\n\nexport type MemoryHistoryOptions = {\n  initialEntries?: InitialEntry[];\n  initialIndex?: number;\n  v5Compat?: boolean;\n};\n\n/**\n * A memory history stores locations in memory. This is useful in stateful\n * environments where there is no web browser, such as node tests or React\n * Native.\n */\nexport interface MemoryHistory extends History {\n  /**\n   * The current index in the history stack.\n   */\n  readonly index: number;\n}\n\n/**\n * Memory history stores the current location in memory. It is designed for use\n * in stateful non-browser environments like tests and React Native.\n */\nexport function createMemoryHistory(\n  options: MemoryHistoryOptions = {}\n): MemoryHistory {\n  let { initialEntries = [\"/\"], initialIndex, v5Compat = false } = options;\n  let entries: Location[]; // Declare so we can access from createMemoryLocation\n  entries = initialEntries.map((entry, index) =>\n    createMemoryLocation(\n      entry,\n      typeof entry === \"string\" ? null : entry.state,\n      index === 0 ? \"default\" : undefined\n    )\n  );\n  let index = clampIndex(\n    initialIndex == null ? entries.length - 1 : initialIndex\n  );\n  let action = Action.Pop;\n  let listener: Listener | null = null;\n\n  function clampIndex(n: number): number {\n    return Math.min(Math.max(n, 0), entries.length - 1);\n  }\n  function getCurrentLocation(): Location {\n    return entries[index];\n  }\n  function createMemoryLocation(\n    to: To,\n    state: any = null,\n    key?: string\n  ): Location {\n    let location = createLocation(\n      entries ? getCurrentLocation().pathname : \"/\",\n      to,\n      state,\n      key\n    );\n    warning(\n      location.pathname.charAt(0) === \"/\",\n      `relative pathnames are not supported in memory history: ${JSON.stringify(\n        to\n      )}`\n    );\n    return location;\n  }\n\n  function createHref(to: To) {\n    return typeof to === \"string\" ? to : createPath(to);\n  }\n\n  let history: MemoryHistory = {\n    get index() {\n      return index;\n    },\n    get action() {\n      return action;\n    },\n    get location() {\n      return getCurrentLocation();\n    },\n    createHref,\n    createURL(to) {\n      return new URL(createHref(to), \"http://localhost\");\n    },\n    encodeLocation(to: To) {\n      let path = typeof to === \"string\" ? parsePath(to) : to;\n      return {\n        pathname: path.pathname || \"\",\n        search: path.search || \"\",\n        hash: path.hash || \"\",\n      };\n    },\n    push(to, state) {\n      action = Action.Push;\n      let nextLocation = createMemoryLocation(to, state);\n      index += 1;\n      entries.splice(index, entries.length, nextLocation);\n      if (v5Compat && listener) {\n        listener({ action, location: nextLocation, delta: 1 });\n      }\n    },\n    replace(to, state) {\n      action = Action.Replace;\n      let nextLocation = createMemoryLocation(to, state);\n      entries[index] = nextLocation;\n      if (v5Compat && listener) {\n        listener({ action, location: nextLocation, delta: 0 });\n      }\n    },\n    go(delta) {\n      action = Action.Pop;\n      let nextIndex = clampIndex(index + delta);\n      let nextLocation = entries[nextIndex];\n      index = nextIndex;\n      if (listener) {\n        listener({ action, location: nextLocation, delta });\n      }\n    },\n    listen(fn: Listener) {\n      listener = fn;\n      return () => {\n        listener = null;\n      };\n    },\n  };\n\n  return history;\n}\n//#endregion\n\n////////////////////////////////////////////////////////////////////////////////\n//#region Browser History\n////////////////////////////////////////////////////////////////////////////////\n\n/**\n * A browser history stores the current location in regular URLs in a web\n * browser environment. This is the standard for most web apps and provides the\n * cleanest URLs the browser's address bar.\n *\n * @see https://github.com/remix-run/history/tree/main/docs/api-reference.md#browserhistory\n */\nexport interface BrowserHistory extends UrlHistory {}\n\nexport type BrowserHistoryOptions = UrlHistoryOptions;\n\n/**\n * Browser history stores the location in regular URLs. This is the standard for\n * most web apps, but it requires some configuration on the server to ensure you\n * serve the same app at multiple URLs.\n *\n * @see https://github.com/remix-run/history/tree/main/docs/api-reference.md#createbrowserhistory\n */\nexport function createBrowserHistory(\n  options: BrowserHistoryOptions = {}\n): BrowserHistory {\n  function createBrowserLocation(\n    window: Window,\n    globalHistory: Window[\"history\"]\n  ) {\n    let { pathname, search, hash } = window.location;\n    return createLocation(\n      \"\",\n      { pathname, search, hash },\n      // state defaults to `null` because `window.history.state` does\n      (globalHistory.state && globalHistory.state.usr) || null,\n      (globalHistory.state && globalHistory.state.key) || \"default\"\n    );\n  }\n\n  function createBrowserHref(window: Window, to: To) {\n    return typeof to === \"string\" ? to : createPath(to);\n  }\n\n  return getUrlBasedHistory(\n    createBrowserLocation,\n    createBrowserHref,\n    null,\n    options\n  );\n}\n//#endregion\n\n////////////////////////////////////////////////////////////////////////////////\n//#region Hash History\n////////////////////////////////////////////////////////////////////////////////\n\n/**\n * A hash history stores the current location in the fragment identifier portion\n * of the URL in a web browser environment.\n *\n * This is ideal for apps that do not control the server for some reason\n * (because the fragment identifier is never sent to the server), including some\n * shared hosting environments that do not provide fine-grained controls over\n * which pages are served at which URLs.\n *\n * @see https://github.com/remix-run/history/tree/main/docs/api-reference.md#hashhistory\n */\nexport interface HashHistory extends UrlHistory {}\n\nexport type HashHistoryOptions = UrlHistoryOptions;\n\n/**\n * Hash history stores the location in window.location.hash. This makes it ideal\n * for situations where you don't want to send the location to the server for\n * some reason, either because you do cannot configure it or the URL space is\n * reserved for something else.\n *\n * @see https://github.com/remix-run/history/tree/main/docs/api-reference.md#createhashhistory\n */\nexport function createHashHistory(\n  options: HashHistoryOptions = {}\n): HashHistory {\n  function createHashLocation(\n    window: Window,\n    globalHistory: Window[\"history\"]\n  ) {\n    let {\n      pathname = \"/\",\n      search = \"\",\n      hash = \"\",\n    } = parsePath(window.location.hash.substr(1));\n    return createLocation(\n      \"\",\n      { pathname, search, hash },\n      // state defaults to `null` because `window.history.state` does\n      (globalHistory.state && globalHistory.state.usr) || null,\n      (globalHistory.state && globalHistory.state.key) || \"default\"\n    );\n  }\n\n  function createHashHref(window: Window, to: To) {\n    let base = window.document.querySelector(\"base\");\n    let href = \"\";\n\n    if (base && base.getAttribute(\"href\")) {\n      let url = window.location.href;\n      let hashIndex = url.indexOf(\"#\");\n      href = hashIndex === -1 ? url : url.slice(0, hashIndex);\n    }\n\n    return href + \"#\" + (typeof to === \"string\" ? to : createPath(to));\n  }\n\n  function validateHashLocation(location: Location, to: To) {\n    warning(\n      location.pathname.charAt(0) === \"/\",\n      `relative pathnames are not supported in hash history.push(${JSON.stringify(\n        to\n      )})`\n    );\n  }\n\n  return getUrlBasedHistory(\n    createHashLocation,\n    createHashHref,\n    validateHashLocation,\n    options\n  );\n}\n//#endregion\n\n////////////////////////////////////////////////////////////////////////////////\n//#region UTILS\n////////////////////////////////////////////////////////////////////////////////\n\n/**\n * @private\n */\nexport function invariant(value: boolean, message?: string): asserts value;\nexport function invariant<T>(\n  value: T | null | undefined,\n  message?: string\n): asserts value is T;\nexport function invariant(value: any, message?: string) {\n  if (value === false || value === null || typeof value === \"undefined\") {\n    throw new Error(message);\n  }\n}\n\nexport function warning(cond: any, message: string) {\n  if (!cond) {\n    // eslint-disable-next-line no-console\n    if (typeof console !== \"undefined\") console.warn(message);\n\n    try {\n      // Welcome to debugging history!\n      //\n      // This error is thrown as a convenience so you can more easily\n      // find the source for a warning that appears in the console by\n      // enabling \"pause on exceptions\" in your JavaScript debugger.\n      throw new Error(message);\n      // eslint-disable-next-line no-empty\n    } catch (e) {}\n  }\n}\n\nfunction createKey() {\n  return Math.random().toString(36).substr(2, 8);\n}\n\n/**\n * For browser-based histories, we combine the state and key into an object\n */\nfunction getHistoryState(location: Location, index: number): HistoryState {\n  return {\n    usr: location.state,\n    key: location.key,\n    idx: index,\n  };\n}\n\n/**\n * Creates a Location object with a unique key from the given Path\n */\nexport function createLocation(\n  current: string | Location,\n  to: To,\n  state: any = null,\n  key?: string\n): Readonly<Location> {\n  let location: Readonly<Location> = {\n    pathname: typeof current === \"string\" ? current : current.pathname,\n    search: \"\",\n    hash: \"\",\n    ...(typeof to === \"string\" ? parsePath(to) : to),\n    state,\n    // TODO: This could be cleaned up.  push/replace should probably just take\n    // full Locations now and avoid the need to run through this flow at all\n    // But that's a pretty big refactor to the current test suite so going to\n    // keep as is for the time being and just let any incoming keys take precedence\n    key: (to && (to as Location).key) || key || createKey(),\n  };\n  return location;\n}\n\n/**\n * Creates a string URL path from the given pathname, search, and hash components.\n */\nexport function createPath({\n  pathname = \"/\",\n  search = \"\",\n  hash = \"\",\n}: Partial<Path>) {\n  if (search && search !== \"?\")\n    pathname += search.charAt(0) === \"?\" ? search : \"?\" + search;\n  if (hash && hash !== \"#\")\n    pathname += hash.charAt(0) === \"#\" ? hash : \"#\" + hash;\n  return pathname;\n}\n\n/**\n * Parses a string URL path into its separate pathname, search, and hash components.\n */\nexport function parsePath(path: string): Partial<Path> {\n  let parsedPath: Partial<Path> = {};\n\n  if (path) {\n    let hashIndex = path.indexOf(\"#\");\n    if (hashIndex >= 0) {\n      parsedPath.hash = path.substr(hashIndex);\n      path = path.substr(0, hashIndex);\n    }\n\n    let searchIndex = path.indexOf(\"?\");\n    if (searchIndex >= 0) {\n      parsedPath.search = path.substr(searchIndex);\n      path = path.substr(0, searchIndex);\n    }\n\n    if (path) {\n      parsedPath.pathname = path;\n    }\n  }\n\n  return parsedPath;\n}\n\nexport interface UrlHistory extends History {}\n\nexport type UrlHistoryOptions = {\n  window?: Window;\n  v5Compat?: boolean;\n};\n\nfunction getUrlBasedHistory(\n  getLocation: (window: Window, globalHistory: Window[\"history\"]) => Location,\n  createHref: (window: Window, to: To) => string,\n  validateLocation: ((location: Location, to: To) => void) | null,\n  options: UrlHistoryOptions = {}\n): UrlHistory {\n  let { window = document.defaultView!, v5Compat = false } = options;\n  let globalHistory = window.history;\n  let action = Action.Pop;\n  let listener: Listener | null = null;\n\n  let index = getIndex()!;\n  // Index should only be null when we initialize. If not, it's because the\n  // user called history.pushState or history.replaceState directly, in which\n  // case we should log a warning as it will result in bugs.\n  if (index == null) {\n    index = 0;\n    globalHistory.replaceState({ ...globalHistory.state, idx: index }, \"\");\n  }\n\n  function getIndex(): number {\n    let state = globalHistory.state || { idx: null };\n    return state.idx;\n  }\n\n  function handlePop() {\n    action = Action.Pop;\n    let nextIndex = getIndex();\n    let delta = nextIndex == null ? null : nextIndex - index;\n    index = nextIndex;\n    if (listener) {\n      listener({ action, location: history.location, delta });\n    }\n  }\n\n  function push(to: To, state?: any) {\n    action = Action.Push;\n    let location = createLocation(history.location, to, state);\n    if (validateLocation) validateLocation(location, to);\n\n    index = getIndex() + 1;\n    let historyState = getHistoryState(location, index);\n    let url = history.createHref(location);\n\n    // try...catch because iOS limits us to 100 pushState calls :/\n    try {\n      globalHistory.pushState(historyState, \"\", url);\n    } catch (error) {\n      // If the exception is because `state` can't be serialized, let that throw\n      // outwards just like a replace call would so the dev knows the cause\n      // https://html.spec.whatwg.org/multipage/nav-history-apis.html#shared-history-push/replace-state-steps\n      // https://html.spec.whatwg.org/multipage/structured-data.html#structuredserializeinternal\n      if (error instanceof DOMException && error.name === \"DataCloneError\") {\n        throw error;\n      }\n      // They are going to lose state here, but there is no real\n      // way to warn them about it since the page will refresh...\n      window.location.assign(url);\n    }\n\n    if (v5Compat && listener) {\n      listener({ action, location: history.location, delta: 1 });\n    }\n  }\n\n  function replace(to: To, state?: any) {\n    action = Action.Replace;\n    let location = createLocation(history.location, to, state);\n    if (validateLocation) validateLocation(location, to);\n\n    index = getIndex();\n    let historyState = getHistoryState(location, index);\n    let url = history.createHref(location);\n    globalHistory.replaceState(historyState, \"\", url);\n\n    if (v5Compat && listener) {\n      listener({ action, location: history.location, delta: 0 });\n    }\n  }\n\n  function createURL(to: To): URL {\n    // window.location.origin is \"null\" (the literal string value) in Firefox\n    // under certain conditions, notably when serving from a local HTML file\n    // See https://bugzilla.mozilla.org/show_bug.cgi?id=878297\n    let base =\n      window.location.origin !== \"null\"\n        ? window.location.origin\n        : window.location.href;\n\n    let href = typeof to === \"string\" ? to : createPath(to);\n    invariant(\n      base,\n      `No window.location.(origin|href) available to create URL for href: ${href}`\n    );\n    return new URL(href, base);\n  }\n\n  let history: History = {\n    get action() {\n      return action;\n    },\n    get location() {\n      return getLocation(window, globalHistory);\n    },\n    listen(fn: Listener) {\n      if (listener) {\n        throw new Error(\"A history only accepts one active listener\");\n      }\n      window.addEventListener(PopStateEventType, handlePop);\n      listener = fn;\n\n      return () => {\n        window.removeEventListener(PopStateEventType, handlePop);\n        listener = null;\n      };\n    },\n    createHref(to) {\n      return createHref(window, to);\n    },\n    createURL,\n    encodeLocation(to) {\n      // Encode a Location the same way window.location would\n      let url = createURL(to);\n      return {\n        pathname: url.pathname,\n        search: url.search,\n        hash: url.hash,\n      };\n    },\n    push,\n    replace,\n    go(n) {\n      return globalHistory.go(n);\n    },\n  };\n\n  return history;\n}\n\n//#endregion\n", "import type { Location, Path, To } from \"./history\";\nimport { warning, invariant, parsePath } from \"./history\";\n\n/**\n * Map of routeId -> data returned from a loader/action/error\n */\nexport interface RouteData {\n  [routeId: string]: any;\n}\n\nexport enum ResultType {\n  data = \"data\",\n  deferred = \"deferred\",\n  redirect = \"redirect\",\n  error = \"error\",\n}\n\n/**\n * Successful result from a loader or action\n */\nexport interface SuccessResult {\n  type: ResultType.data;\n  data: any;\n  statusCode?: number;\n  headers?: Headers;\n}\n\n/**\n * Successful defer() result from a loader or action\n */\nexport interface DeferredResult {\n  type: ResultType.deferred;\n  deferredData: DeferredData;\n  statusCode?: number;\n  headers?: Headers;\n}\n\n/**\n * Redirect result from a loader or action\n */\nexport interface RedirectResult {\n  type: ResultType.redirect;\n  status: number;\n  location: string;\n  revalidate: boolean;\n}\n\n/**\n * Unsuccessful result from a loader or action\n */\nexport interface ErrorResult {\n  type: ResultType.error;\n  error: any;\n  headers?: Headers;\n}\n\n/**\n * Result from a loader or action - potentially successful or unsuccessful\n */\nexport type DataResult =\n  | SuccessResult\n  | DeferredResult\n  | RedirectResult\n  | ErrorResult;\n\ntype LowerCaseFormMethod = \"get\" | \"post\" | \"put\" | \"patch\" | \"delete\";\ntype UpperCaseFormMethod = Uppercase<LowerCaseFormMethod>;\n\n/**\n * Users can specify either lowercase or uppercase form methods on <Form>,\n * useSubmit(), <fetcher.Form>, etc.\n */\nexport type HTMLFormMethod = LowerCaseFormMethod | UpperCaseFormMethod;\n\n/**\n * Active navigation/fetcher form methods are exposed in lowercase on the\n * RouterState\n */\nexport type FormMethod = LowerCaseFormMethod;\nexport type MutationFormMethod = Exclude<FormMethod, \"get\">;\n\n/**\n * In v7, active navigation/fetcher form methods are exposed in uppercase on the\n * RouterState.  This is to align with the normalization done via fetch().\n */\nexport type V7_FormMethod = UpperCaseFormMethod;\nexport type V7_MutationFormMethod = Exclude<V7_FormMethod, \"GET\">;\n\nexport type FormEncType =\n  | \"application/x-www-form-urlencoded\"\n  | \"multipart/form-data\"\n  | \"application/json\"\n  | \"text/plain\";\n\n// Thanks https://github.com/sindresorhus/type-fest!\ntype JsonObject = { [Key in string]: JsonValue } & {\n  [Key in string]?: JsonValue | undefined;\n};\ntype JsonArray = JsonValue[] | readonly JsonValue[];\ntype JsonPrimitive = string | number | boolean | null;\ntype JsonValue = JsonPrimitive | JsonObject | JsonArray;\n\n/**\n * @private\n * Internal interface to pass around for action submissions, not intended for\n * external consumption\n */\nexport type Submission =\n  | {\n      formMethod: FormMethod | V7_FormMethod;\n      formAction: string;\n      formEncType: FormEncType;\n      formData: FormData;\n      json: undefined;\n      text: undefined;\n    }\n  | {\n      formMethod: FormMethod | V7_FormMethod;\n      formAction: string;\n      formEncType: FormEncType;\n      formData: undefined;\n      json: JsonValue;\n      text: undefined;\n    }\n  | {\n      formMethod: FormMethod | V7_FormMethod;\n      formAction: string;\n      formEncType: FormEncType;\n      formData: undefined;\n      json: undefined;\n      text: string;\n    };\n\n/**\n * @private\n * Arguments passed to route loader/action functions.  Same for now but we keep\n * this as a private implementation detail in case they diverge in the future.\n */\ninterface DataFunctionArgs {\n  request: Request;\n  params: Params;\n  context?: any;\n}\n\n/**\n * Arguments passed to loader functions\n */\nexport interface LoaderFunctionArgs extends DataFunctionArgs {}\n\n/**\n * Arguments passed to action functions\n */\nexport interface ActionFunctionArgs extends DataFunctionArgs {}\n\n/**\n * Loaders and actions can return anything except `undefined` (`null` is a\n * valid return value if there is no data to return).  Responses are preferred\n * and will ease any future migration to Remix\n */\ntype DataFunctionValue = Response | NonNullable<unknown> | null;\n\n/**\n * Route loader function signature\n */\nexport interface LoaderFunction {\n  (args: LoaderFunctionArgs): Promise<DataFunctionValue> | DataFunctionValue;\n}\n\n/**\n * Route action function signature\n */\nexport interface ActionFunction {\n  (args: ActionFunctionArgs): Promise<DataFunctionValue> | DataFunctionValue;\n}\n\n/**\n * Route shouldRevalidate function signature.  This runs after any submission\n * (navigation or fetcher), so we flatten the navigation/fetcher submission\n * onto the arguments.  It shouldn't matter whether it came from a navigation\n * or a fetcher, what really matters is the URLs and the formData since loaders\n * have to re-run based on the data models that were potentially mutated.\n */\nexport interface ShouldRevalidateFunction {\n  (args: {\n    currentUrl: URL;\n    currentParams: AgnosticDataRouteMatch[\"params\"];\n    nextUrl: URL;\n    nextParams: AgnosticDataRouteMatch[\"params\"];\n    formMethod?: Submission[\"formMethod\"];\n    formAction?: Submission[\"formAction\"];\n    formEncType?: Submission[\"formEncType\"];\n    text?: Submission[\"text\"];\n    formData?: Submission[\"formData\"];\n    json?: Submission[\"json\"];\n    actionResult?: DataResult;\n    defaultShouldRevalidate: boolean;\n  }): boolean;\n}\n\n/**\n * Function provided by the framework-aware layers to set `hasErrorBoundary`\n * from the framework-aware `errorElement` prop\n *\n * @deprecated Use `mapRouteProperties` instead\n */\nexport interface DetectErrorBoundaryFunction {\n  (route: AgnosticRouteObject): boolean;\n}\n\n/**\n * Function provided by the framework-aware layers to set any framework-specific\n * properties from framework-agnostic properties\n */\nexport interface MapRoutePropertiesFunction {\n  (route: AgnosticRouteObject): {\n    hasErrorBoundary: boolean;\n  } & Record<string, any>;\n}\n\n/**\n * Keys we cannot change from within a lazy() function. We spread all other keys\n * onto the route. Either they're meaningful to the router, or they'll get\n * ignored.\n */\nexport type ImmutableRouteKey =\n  | \"lazy\"\n  | \"caseSensitive\"\n  | \"path\"\n  | \"id\"\n  | \"index\"\n  | \"children\";\n\nexport const immutableRouteKeys = new Set<ImmutableRouteKey>([\n  \"lazy\",\n  \"caseSensitive\",\n  \"path\",\n  \"id\",\n  \"index\",\n  \"children\",\n]);\n\ntype RequireOne<T, Key = keyof T> = Exclude<\n  {\n    [K in keyof T]: K extends Key ? Omit<T, K> & Required<Pick<T, K>> : never;\n  }[keyof T],\n  undefined\n>;\n\n/**\n * lazy() function to load a route definition, which can add non-matching\n * related properties to a route\n */\nexport interface LazyRouteFunction<R extends AgnosticRouteObject> {\n  (): Promise<RequireOne<Omit<R, ImmutableRouteKey>>>;\n}\n\n/**\n * Base RouteObject with common props shared by all types of routes\n */\ntype AgnosticBaseRouteObject = {\n  caseSensitive?: boolean;\n  path?: string;\n  id?: string;\n  loader?: LoaderFunction;\n  action?: ActionFunction;\n  hasErrorBoundary?: boolean;\n  shouldRevalidate?: ShouldRevalidateFunction;\n  handle?: any;\n  lazy?: LazyRouteFunction<AgnosticBaseRouteObject>;\n};\n\n/**\n * Index routes must not have children\n */\nexport type AgnosticIndexRouteObject = AgnosticBaseRouteObject & {\n  children?: undefined;\n  index: true;\n};\n\n/**\n * Non-index routes may have children, but cannot have index\n */\nexport type AgnosticNonIndexRouteObject = AgnosticBaseRouteObject & {\n  children?: AgnosticRouteObject[];\n  index?: false;\n};\n\n/**\n * A route object represents a logical route, with (optionally) its child\n * routes organized in a tree-like structure.\n */\nexport type AgnosticRouteObject =\n  | AgnosticIndexRouteObject\n  | AgnosticNonIndexRouteObject;\n\nexport type AgnosticDataIndexRouteObject = AgnosticIndexRouteObject & {\n  id: string;\n};\n\nexport type AgnosticDataNonIndexRouteObject = AgnosticNonIndexRouteObject & {\n  children?: AgnosticDataRouteObject[];\n  id: string;\n};\n\n/**\n * A data route object, which is just a RouteObject with a required unique ID\n */\nexport type AgnosticDataRouteObject =\n  | AgnosticDataIndexRouteObject\n  | AgnosticDataNonIndexRouteObject;\n\nexport type RouteManifest = Record<string, AgnosticDataRouteObject | undefined>;\n\n// Recursive helper for finding path parameters in the absence of wildcards\ntype _PathParam<Path extends string> =\n  // split path into individual path segments\n  Path extends `${infer L}/${infer R}`\n    ? _PathParam<L> | _PathParam<R>\n    : // find params after `:`\n    Path extends `:${infer Param}`\n    ? Param extends `${infer Optional}?`\n      ? Optional\n      : Param\n    : // otherwise, there aren't any params present\n      never;\n\n/**\n * Examples:\n * \"/a/b/*\" -> \"*\"\n * \":a\" -> \"a\"\n * \"/a/:b\" -> \"b\"\n * \"/a/blahblahblah:b\" -> \"b\"\n * \"/:a/:b\" -> \"a\" | \"b\"\n * \"/:a/b/:c/*\" -> \"a\" | \"c\" | \"*\"\n */\ntype PathParam<Path extends string> =\n  // check if path is just a wildcard\n  Path extends \"*\" | \"/*\"\n    ? \"*\"\n    : // look for wildcard at the end of the path\n    Path extends `${infer Rest}/*`\n    ? \"*\" | _PathParam<Rest>\n    : // look for params in the absence of wildcards\n      _PathParam<Path>;\n\n// Attempt to parse the given string segment. If it fails, then just return the\n// plain string type as a default fallback. Otherwise return the union of the\n// parsed string literals that were referenced as dynamic segments in the route.\nexport type ParamParseKey<Segment extends string> =\n  // if could not find path params, fallback to `string`\n  [PathParam<Segment>] extends [never] ? string : PathParam<Segment>;\n\n/**\n * The parameters that were parsed from the URL path.\n */\nexport type Params<Key extends string = string> = {\n  readonly [key in Key]: string | undefined;\n};\n\n/**\n * A RouteMatch contains info about how a route matched a URL.\n */\nexport interface AgnosticRouteMatch<\n  ParamKey extends string = string,\n  RouteObjectType extends AgnosticRouteObject = AgnosticRouteObject\n> {\n  /**\n   * The names and values of dynamic parameters in the URL.\n   */\n  params: Params<ParamKey>;\n  /**\n   * The portion of the URL pathname that was matched.\n   */\n  pathname: string;\n  /**\n   * The portion of the URL pathname that was matched before child routes.\n   */\n  pathnameBase: string;\n  /**\n   * The route object that was used to match.\n   */\n  route: RouteObjectType;\n}\n\nexport interface AgnosticDataRouteMatch\n  extends AgnosticRouteMatch<string, AgnosticDataRouteObject> {}\n\nfunction isIndexRoute(\n  route: AgnosticRouteObject\n): route is AgnosticIndexRouteObject {\n  return route.index === true;\n}\n\n// Walk the route tree generating unique IDs where necessary so we are working\n// solely with AgnosticDataRouteObject's within the Router\nexport function convertRoutesToDataRoutes(\n  routes: AgnosticRouteObject[],\n  mapRouteProperties: MapRoutePropertiesFunction,\n  parentPath: number[] = [],\n  manifest: RouteManifest = {}\n): AgnosticDataRouteObject[] {\n  return routes.map((route, index) => {\n    let treePath = [...parentPath, index];\n    let id = typeof route.id === \"string\" ? route.id : treePath.join(\"-\");\n    invariant(\n      route.index !== true || !route.children,\n      `Cannot specify children on an index route`\n    );\n    invariant(\n      !manifest[id],\n      `Found a route id collision on id \"${id}\".  Route ` +\n        \"id's must be globally unique within Data Router usages\"\n    );\n\n    if (isIndexRoute(route)) {\n      let indexRoute: AgnosticDataIndexRouteObject = {\n        ...route,\n        ...mapRouteProperties(route),\n        id,\n      };\n      manifest[id] = indexRoute;\n      return indexRoute;\n    } else {\n      let pathOrLayoutRoute: AgnosticDataNonIndexRouteObject = {\n        ...route,\n        ...mapRouteProperties(route),\n        id,\n        children: undefined,\n      };\n      manifest[id] = pathOrLayoutRoute;\n\n      if (route.children) {\n        pathOrLayoutRoute.children = convertRoutesToDataRoutes(\n          route.children,\n          mapRouteProperties,\n          treePath,\n          manifest\n        );\n      }\n\n      return pathOrLayoutRoute;\n    }\n  });\n}\n\n/**\n * Matches the given routes to a location and returns the match data.\n *\n * @see https://reactrouter.com/utils/match-routes\n */\nexport function matchRoutes<\n  RouteObjectType extends AgnosticRouteObject = AgnosticRouteObject\n>(\n  routes: RouteObjectType[],\n  locationArg: Partial<Location> | string,\n  basename = \"/\"\n): AgnosticRouteMatch<string, RouteObjectType>[] | null {\n  let location =\n    typeof locationArg === \"string\" ? parsePath(locationArg) : locationArg;\n\n  let pathname = stripBasename(location.pathname || \"/\", basename);\n\n  if (pathname == null) {\n    return null;\n  }\n\n  let branches = flattenRoutes(routes);\n  rankRouteBranches(branches);\n\n  let matches = null;\n  for (let i = 0; matches == null && i < branches.length; ++i) {\n    matches = matchRouteBranch<string, RouteObjectType>(\n      branches[i],\n      // Incoming pathnames are generally encoded from either window.location\n      // or from router.navigate, but we want to match against the unencoded\n      // paths in the route definitions.  Memory router locations won't be\n      // encoded here but there also shouldn't be anything to decode so this\n      // should be a safe operation.  This avoids needing matchRoutes to be\n      // history-aware.\n      safelyDecodeURI(pathname)\n    );\n  }\n\n  return matches;\n}\n\ninterface RouteMeta<\n  RouteObjectType extends AgnosticRouteObject = AgnosticRouteObject\n> {\n  relativePath: string;\n  caseSensitive: boolean;\n  childrenIndex: number;\n  route: RouteObjectType;\n}\n\ninterface RouteBranch<\n  RouteObjectType extends AgnosticRouteObject = AgnosticRouteObject\n> {\n  path: string;\n  score: number;\n  routesMeta: RouteMeta<RouteObjectType>[];\n}\n\nfunction flattenRoutes<\n  RouteObjectType extends AgnosticRouteObject = AgnosticRouteObject\n>(\n  routes: RouteObjectType[],\n  branches: RouteBranch<RouteObjectType>[] = [],\n  parentsMeta: RouteMeta<RouteObjectType>[] = [],\n  parentPath = \"\"\n): RouteBranch<RouteObjectType>[] {\n  let flattenRoute = (\n    route: RouteObjectType,\n    index: number,\n    relativePath?: string\n  ) => {\n    let meta: RouteMeta<RouteObjectType> = {\n      relativePath:\n        relativePath === undefined ? route.path || \"\" : relativePath,\n      caseSensitive: route.caseSensitive === true,\n      childrenIndex: index,\n      route,\n    };\n\n    if (meta.relativePath.startsWith(\"/\")) {\n      invariant(\n        meta.relativePath.startsWith(parentPath),\n        `Absolute route path \"${meta.relativePath}\" nested under path ` +\n          `\"${parentPath}\" is not valid. An absolute child route path ` +\n          `must start with the combined path of all its parent routes.`\n      );\n\n      meta.relativePath = meta.relativePath.slice(parentPath.length);\n    }\n\n    let path = joinPaths([parentPath, meta.relativePath]);\n    let routesMeta = parentsMeta.concat(meta);\n\n    // Add the children before adding this route to the array so we traverse the\n    // route tree depth-first and child routes appear before their parents in\n    // the \"flattened\" version.\n    if (route.children && route.children.length > 0) {\n      invariant(\n        // Our types know better, but runtime JS may not!\n        // @ts-expect-error\n        route.index !== true,\n        `Index routes must not have child routes. Please remove ` +\n          `all child routes from route path \"${path}\".`\n      );\n\n      flattenRoutes(route.children, branches, routesMeta, path);\n    }\n\n    // Routes without a path shouldn't ever match by themselves unless they are\n    // index routes, so don't add them to the list of possible branches.\n    if (route.path == null && !route.index) {\n      return;\n    }\n\n    branches.push({\n      path,\n      score: computeScore(path, route.index),\n      routesMeta,\n    });\n  };\n  routes.forEach((route, index) => {\n    // coarse-grain check for optional params\n    if (route.path === \"\" || !route.path?.includes(\"?\")) {\n      flattenRoute(route, index);\n    } else {\n      for (let exploded of explodeOptionalSegments(route.path)) {\n        flattenRoute(route, index, exploded);\n      }\n    }\n  });\n\n  return branches;\n}\n\n/**\n * Computes all combinations of optional path segments for a given path,\n * excluding combinations that are ambiguous and of lower priority.\n *\n * For example, `/one/:two?/three/:four?/:five?` explodes to:\n * - `/one/three`\n * - `/one/:two/three`\n * - `/one/three/:four`\n * - `/one/three/:five`\n * - `/one/:two/three/:four`\n * - `/one/:two/three/:five`\n * - `/one/three/:four/:five`\n * - `/one/:two/three/:four/:five`\n */\nfunction explodeOptionalSegments(path: string): string[] {\n  let segments = path.split(\"/\");\n  if (segments.length === 0) return [];\n\n  let [first, ...rest] = segments;\n\n  // Optional path segments are denoted by a trailing `?`\n  let isOptional = first.endsWith(\"?\");\n  // Compute the corresponding required segment: `foo?` -> `foo`\n  let required = first.replace(/\\?$/, \"\");\n\n  if (rest.length === 0) {\n    // Intepret empty string as omitting an optional segment\n    // `[\"one\", \"\", \"three\"]` corresponds to omitting `:two` from `/one/:two?/three` -> `/one/three`\n    return isOptional ? [required, \"\"] : [required];\n  }\n\n  let restExploded = explodeOptionalSegments(rest.join(\"/\"));\n\n  let result: string[] = [];\n\n  // All child paths with the prefix.  Do this for all children before the\n  // optional version for all children so we get consistent ordering where the\n  // parent optional aspect is preferred as required.  Otherwise, we can get\n  // child sections interspersed where deeper optional segments are higher than\n  // parent optional segments, where for example, /:two would explodes _earlier_\n  // then /:one.  By always including the parent as required _for all children_\n  // first, we avoid this issue\n  result.push(\n    ...restExploded.map((subpath) =>\n      subpath === \"\" ? required : [required, subpath].join(\"/\")\n    )\n  );\n\n  // Then if this is an optional value, add all child versions without\n  if (isOptional) {\n    result.push(...restExploded);\n  }\n\n  // for absolute paths, ensure `/` instead of empty segment\n  return result.map((exploded) =>\n    path.startsWith(\"/\") && exploded === \"\" ? \"/\" : exploded\n  );\n}\n\nfunction rankRouteBranches(branches: RouteBranch[]): void {\n  branches.sort((a, b) =>\n    a.score !== b.score\n      ? b.score - a.score // Higher score first\n      : compareIndexes(\n          a.routesMeta.map((meta) => meta.childrenIndex),\n          b.routesMeta.map((meta) => meta.childrenIndex)\n        )\n  );\n}\n\nconst paramRe = /^:\\w+$/;\nconst dynamicSegmentValue = 3;\nconst indexRouteValue = 2;\nconst emptySegmentValue = 1;\nconst staticSegmentValue = 10;\nconst splatPenalty = -2;\nconst isSplat = (s: string) => s === \"*\";\n\nfunction computeScore(path: string, index: boolean | undefined): number {\n  let segments = path.split(\"/\");\n  let initialScore = segments.length;\n  if (segments.some(isSplat)) {\n    initialScore += splatPenalty;\n  }\n\n  if (index) {\n    initialScore += indexRouteValue;\n  }\n\n  return segments\n    .filter((s) => !isSplat(s))\n    .reduce(\n      (score, segment) =>\n        score +\n        (paramRe.test(segment)\n          ? dynamicSegmentValue\n          : segment === \"\"\n          ? emptySegmentValue\n          : staticSegmentValue),\n      initialScore\n    );\n}\n\nfunction compareIndexes(a: number[], b: number[]): number {\n  let siblings =\n    a.length === b.length && a.slice(0, -1).every((n, i) => n === b[i]);\n\n  return siblings\n    ? // If two routes are siblings, we should try to match the earlier sibling\n      // first. This allows people to have fine-grained control over the matching\n      // behavior by simply putting routes with identical paths in the order they\n      // want them tried.\n      a[a.length - 1] - b[b.length - 1]\n    : // Otherwise, it doesn't really make sense to rank non-siblings by index,\n      // so they sort equally.\n      0;\n}\n\nfunction matchRouteBranch<\n  ParamKey extends string = string,\n  RouteObjectType extends AgnosticRouteObject = AgnosticRouteObject\n>(\n  branch: RouteBranch<RouteObjectType>,\n  pathname: string\n): AgnosticRouteMatch<ParamKey, RouteObjectType>[] | null {\n  let { routesMeta } = branch;\n\n  let matchedParams = {};\n  let matchedPathname = \"/\";\n  let matches: AgnosticRouteMatch<ParamKey, RouteObjectType>[] = [];\n  for (let i = 0; i < routesMeta.length; ++i) {\n    let meta = routesMeta[i];\n    let end = i === routesMeta.length - 1;\n    let remainingPathname =\n      matchedPathname === \"/\"\n        ? pathname\n        : pathname.slice(matchedPathname.length) || \"/\";\n    let match = matchPath(\n      { path: meta.relativePath, caseSensitive: meta.caseSensitive, end },\n      remainingPathname\n    );\n\n    if (!match) return null;\n\n    Object.assign(matchedParams, match.params);\n\n    let route = meta.route;\n\n    matches.push({\n      // TODO: Can this as be avoided?\n      params: matchedParams as Params<ParamKey>,\n      pathname: joinPaths([matchedPathname, match.pathname]),\n      pathnameBase: normalizePathname(\n        joinPaths([matchedPathname, match.pathnameBase])\n      ),\n      route,\n    });\n\n    if (match.pathnameBase !== \"/\") {\n      matchedPathname = joinPaths([matchedPathname, match.pathnameBase]);\n    }\n  }\n\n  return matches;\n}\n\n/**\n * Returns a path with params interpolated.\n *\n * @see https://reactrouter.com/utils/generate-path\n */\nexport function generatePath<Path extends string>(\n  originalPath: Path,\n  params: {\n    [key in PathParam<Path>]: string | null;\n  } = {} as any\n): string {\n  let path: string = originalPath;\n  if (path.endsWith(\"*\") && path !== \"*\" && !path.endsWith(\"/*\")) {\n    warning(\n      false,\n      `Route path \"${path}\" will be treated as if it were ` +\n        `\"${path.replace(/\\*$/, \"/*\")}\" because the \\`*\\` character must ` +\n        `always follow a \\`/\\` in the pattern. To get rid of this warning, ` +\n        `please change the route path to \"${path.replace(/\\*$/, \"/*\")}\".`\n    );\n    path = path.replace(/\\*$/, \"/*\") as Path;\n  }\n\n  // ensure `/` is added at the beginning if the path is absolute\n  const prefix = path.startsWith(\"/\") ? \"/\" : \"\";\n\n  const stringify = (p: any) =>\n    p == null ? \"\" : typeof p === \"string\" ? p : String(p);\n\n  const segments = path\n    .split(/\\/+/)\n    .map((segment, index, array) => {\n      const isLastSegment = index === array.length - 1;\n\n      // only apply the splat if it's the last segment\n      if (isLastSegment && segment === \"*\") {\n        const star = \"*\" as PathParam<Path>;\n        // Apply the splat\n        return stringify(params[star]);\n      }\n\n      const keyMatch = segment.match(/^:(\\w+)(\\??)$/);\n      if (keyMatch) {\n        const [, key, optional] = keyMatch;\n        let param = params[key as PathParam<Path>];\n        invariant(optional === \"?\" || param != null, `Missing \":${key}\" param`);\n        return stringify(param);\n      }\n\n      // Remove any optional markers from optional static segments\n      return segment.replace(/\\?$/g, \"\");\n    })\n    // Remove empty segments\n    .filter((segment) => !!segment);\n\n  return prefix + segments.join(\"/\");\n}\n\n/**\n * A PathPattern is used to match on some portion of a URL pathname.\n */\nexport interface PathPattern<Path extends string = string> {\n  /**\n   * A string to match against a URL pathname. May contain `:id`-style segments\n   * to indicate placeholders for dynamic parameters. May also end with `/*` to\n   * indicate matching the rest of the URL pathname.\n   */\n  path: Path;\n  /**\n   * Should be `true` if the static portions of the `path` should be matched in\n   * the same case.\n   */\n  caseSensitive?: boolean;\n  /**\n   * Should be `true` if this pattern should match the entire URL pathname.\n   */\n  end?: boolean;\n}\n\n/**\n * A PathMatch contains info about how a PathPattern matched on a URL pathname.\n */\nexport interface PathMatch<ParamKey extends string = string> {\n  /**\n   * The names and values of dynamic parameters in the URL.\n   */\n  params: Params<ParamKey>;\n  /**\n   * The portion of the URL pathname that was matched.\n   */\n  pathname: string;\n  /**\n   * The portion of the URL pathname that was matched before child routes.\n   */\n  pathnameBase: string;\n  /**\n   * The pattern that was used to match.\n   */\n  pattern: PathPattern;\n}\n\ntype Mutable<T> = {\n  -readonly [P in keyof T]: T[P];\n};\n\n/**\n * Performs pattern matching on a URL pathname and returns information about\n * the match.\n *\n * @see https://reactrouter.com/utils/match-path\n */\nexport function matchPath<\n  ParamKey extends ParamParseKey<Path>,\n  Path extends string\n>(\n  pattern: PathPattern<Path> | Path,\n  pathname: string\n): PathMatch<ParamKey> | null {\n  if (typeof pattern === \"string\") {\n    pattern = { path: pattern, caseSensitive: false, end: true };\n  }\n\n  let [matcher, paramNames] = compilePath(\n    pattern.path,\n    pattern.caseSensitive,\n    pattern.end\n  );\n\n  let match = pathname.match(matcher);\n  if (!match) return null;\n\n  let matchedPathname = match[0];\n  let pathnameBase = matchedPathname.replace(/(.)\\/+$/, \"$1\");\n  let captureGroups = match.slice(1);\n  let params: Params = paramNames.reduce<Mutable<Params>>(\n    (memo, paramName, index) => {\n      // We need to compute the pathnameBase here using the raw splat value\n      // instead of using params[\"*\"] later because it will be decoded then\n      if (paramName === \"*\") {\n        let splatValue = captureGroups[index] || \"\";\n        pathnameBase = matchedPathname\n          .slice(0, matchedPathname.length - splatValue.length)\n          .replace(/(.)\\/+$/, \"$1\");\n      }\n\n      memo[paramName] = safelyDecodeURIComponent(\n        captureGroups[index] || \"\",\n        paramName\n      );\n      return memo;\n    },\n    {}\n  );\n\n  return {\n    params,\n    pathname: matchedPathname,\n    pathnameBase,\n    pattern,\n  };\n}\n\nfunction compilePath(\n  path: string,\n  caseSensitive = false,\n  end = true\n): [RegExp, string[]] {\n  warning(\n    path === \"*\" || !path.endsWith(\"*\") || path.endsWith(\"/*\"),\n    `Route path \"${path}\" will be treated as if it were ` +\n      `\"${path.replace(/\\*$/, \"/*\")}\" because the \\`*\\` character must ` +\n      `always follow a \\`/\\` in the pattern. To get rid of this warning, ` +\n      `please change the route path to \"${path.replace(/\\*$/, \"/*\")}\".`\n  );\n\n  let paramNames: string[] = [];\n  let regexpSource =\n    \"^\" +\n    path\n      .replace(/\\/*\\*?$/, \"\") // Ignore trailing / and /*, we'll handle it below\n      .replace(/^\\/*/, \"/\") // Make sure it has a leading /\n      .replace(/[\\\\.*+^$?{}|()[\\]]/g, \"\\\\$&\") // Escape special regex chars\n      .replace(/\\/:(\\w+)/g, (_: string, paramName: string) => {\n        paramNames.push(paramName);\n        return \"/([^\\\\/]+)\";\n      });\n\n  if (path.endsWith(\"*\")) {\n    paramNames.push(\"*\");\n    regexpSource +=\n      path === \"*\" || path === \"/*\"\n        ? \"(.*)$\" // Already matched the initial /, just match the rest\n        : \"(?:\\\\/(.+)|\\\\/*)$\"; // Don't include the / in params[\"*\"]\n  } else if (end) {\n    // When matching to the end, ignore trailing slashes\n    regexpSource += \"\\\\/*$\";\n  } else if (path !== \"\" && path !== \"/\") {\n    // If our path is non-empty and contains anything beyond an initial slash,\n    // then we have _some_ form of path in our regex so we should expect to\n    // match only if we find the end of this path segment.  Look for an optional\n    // non-captured trailing slash (to match a portion of the URL) or the end\n    // of the path (if we've matched to the end).  We used to do this with a\n    // word boundary but that gives false positives on routes like\n    // /user-preferences since `-` counts as a word boundary.\n    regexpSource += \"(?:(?=\\\\/|$))\";\n  } else {\n    // Nothing to match for \"\" or \"/\"\n  }\n\n  let matcher = new RegExp(regexpSource, caseSensitive ? undefined : \"i\");\n\n  return [matcher, paramNames];\n}\n\nfunction safelyDecodeURI(value: string) {\n  try {\n    return decodeURI(value);\n  } catch (error) {\n    warning(\n      false,\n      `The URL path \"${value}\" could not be decoded because it is is a ` +\n        `malformed URL segment. This is probably due to a bad percent ` +\n        `encoding (${error}).`\n    );\n\n    return value;\n  }\n}\n\nfunction safelyDecodeURIComponent(value: string, paramName: string) {\n  try {\n    return decodeURIComponent(value);\n  } catch (error) {\n    warning(\n      false,\n      `The value for the URL param \"${paramName}\" will not be decoded because` +\n        ` the string \"${value}\" is a malformed URL segment. This is probably` +\n        ` due to a bad percent encoding (${error}).`\n    );\n\n    return value;\n  }\n}\n\n/**\n * @private\n */\nexport function stripBasename(\n  pathname: string,\n  basename: string\n): string | null {\n  if (basename === \"/\") return pathname;\n\n  if (!pathname.toLowerCase().startsWith(basename.toLowerCase())) {\n    return null;\n  }\n\n  // We want to leave trailing slash behavior in the user's control, so if they\n  // specify a basename with a trailing slash, we should support it\n  let startIndex = basename.endsWith(\"/\")\n    ? basename.length - 1\n    : basename.length;\n  let nextChar = pathname.charAt(startIndex);\n  if (nextChar && nextChar !== \"/\") {\n    // pathname does not start with basename/\n    return null;\n  }\n\n  return pathname.slice(startIndex) || \"/\";\n}\n\n/**\n * Returns a resolved path object relative to the given pathname.\n *\n * @see https://reactrouter.com/utils/resolve-path\n */\nexport function resolvePath(to: To, fromPathname = \"/\"): Path {\n  let {\n    pathname: toPathname,\n    search = \"\",\n    hash = \"\",\n  } = typeof to === \"string\" ? parsePath(to) : to;\n\n  let pathname = toPathname\n    ? toPathname.startsWith(\"/\")\n      ? toPathname\n      : resolvePathname(toPathname, fromPathname)\n    : fromPathname;\n\n  return {\n    pathname,\n    search: normalizeSearch(search),\n    hash: normalizeHash(hash),\n  };\n}\n\nfunction resolvePathname(relativePath: string, fromPathname: string): string {\n  let segments = fromPathname.replace(/\\/+$/, \"\").split(\"/\");\n  let relativeSegments = relativePath.split(\"/\");\n\n  relativeSegments.forEach((segment) => {\n    if (segment === \"..\") {\n      // Keep the root \"\" segment so the pathname starts at /\n      if (segments.length > 1) segments.pop();\n    } else if (segment !== \".\") {\n      segments.push(segment);\n    }\n  });\n\n  return segments.length > 1 ? segments.join(\"/\") : \"/\";\n}\n\nfunction getInvalidPathError(\n  char: string,\n  field: string,\n  dest: string,\n  path: Partial<Path>\n) {\n  return (\n    `Cannot include a '${char}' character in a manually specified ` +\n    `\\`to.${field}\\` field [${JSON.stringify(\n      path\n    )}].  Please separate it out to the ` +\n    `\\`to.${dest}\\` field. Alternatively you may provide the full path as ` +\n    `a string in <Link to=\"...\"> and the router will parse it for you.`\n  );\n}\n\n/**\n * @private\n *\n * When processing relative navigation we want to ignore ancestor routes that\n * do not contribute to the path, such that index/pathless layout routes don't\n * interfere.\n *\n * For example, when moving a route element into an index route and/or a\n * pathless layout route, relative link behavior contained within should stay\n * the same.  Both of the following examples should link back to the root:\n *\n *   <Route path=\"/\">\n *     <Route path=\"accounts\" element={<Link to=\"..\"}>\n *   </Route>\n *\n *   <Route path=\"/\">\n *     <Route path=\"accounts\">\n *       <Route element={<AccountsLayout />}>       // <-- Does not contribute\n *         <Route index element={<Link to=\"..\"} />  // <-- Does not contribute\n *       </Route\n *     </Route>\n *   </Route>\n */\nexport function getPathContributingMatches<\n  T extends AgnosticRouteMatch = AgnosticRouteMatch\n>(matches: T[]) {\n  return matches.filter(\n    (match, index) =>\n      index === 0 || (match.route.path && match.route.path.length > 0)\n  );\n}\n\n/**\n * @private\n */\nexport function resolveTo(\n  toArg: To,\n  routePathnames: string[],\n  locationPathname: string,\n  isPathRelative = false\n): Path {\n  let to: Partial<Path>;\n  if (typeof toArg === \"string\") {\n    to = parsePath(toArg);\n  } else {\n    to = { ...toArg };\n\n    invariant(\n      !to.pathname || !to.pathname.includes(\"?\"),\n      getInvalidPathError(\"?\", \"pathname\", \"search\", to)\n    );\n    invariant(\n      !to.pathname || !to.pathname.includes(\"#\"),\n      getInvalidPathError(\"#\", \"pathname\", \"hash\", to)\n    );\n    invariant(\n      !to.search || !to.search.includes(\"#\"),\n      getInvalidPathError(\"#\", \"search\", \"hash\", to)\n    );\n  }\n\n  let isEmptyPath = toArg === \"\" || to.pathname === \"\";\n  let toPathname = isEmptyPath ? \"/\" : to.pathname;\n\n  let from: string;\n\n  // Routing is relative to the current pathname if explicitly requested.\n  //\n  // If a pathname is explicitly provided in `to`, it should be relative to the\n  // route context. This is explained in `Note on `<Link to>` values` in our\n  // migration guide from v5 as a means of disambiguation between `to` values\n  // that begin with `/` and those that do not. However, this is problematic for\n  // `to` values that do not provide a pathname. `to` can simply be a search or\n  // hash string, in which case we should assume that the navigation is relative\n  // to the current location's pathname and *not* the route pathname.\n  if (isPathRelative || toPathname == null) {\n    from = locationPathname;\n  } else {\n    let routePathnameIndex = routePathnames.length - 1;\n\n    if (toPathname.startsWith(\"..\")) {\n      let toSegments = toPathname.split(\"/\");\n\n      // Each leading .. segment means \"go up one route\" instead of \"go up one\n      // URL segment\".  This is a key difference from how <a href> works and a\n      // major reason we call this a \"to\" value instead of a \"href\".\n      while (toSegments[0] === \"..\") {\n        toSegments.shift();\n        routePathnameIndex -= 1;\n      }\n\n      to.pathname = toSegments.join(\"/\");\n    }\n\n    // If there are more \"..\" segments than parent routes, resolve relative to\n    // the root / URL.\n    from = routePathnameIndex >= 0 ? routePathnames[routePathnameIndex] : \"/\";\n  }\n\n  let path = resolvePath(to, from);\n\n  // Ensure the pathname has a trailing slash if the original \"to\" had one\n  let hasExplicitTrailingSlash =\n    toPathname && toPathname !== \"/\" && toPathname.endsWith(\"/\");\n  // Or if this was a link to the current path which has a trailing slash\n  let hasCurrentTrailingSlash =\n    (isEmptyPath || toPathname === \".\") && locationPathname.endsWith(\"/\");\n  if (\n    !path.pathname.endsWith(\"/\") &&\n    (hasExplicitTrailingSlash || hasCurrentTrailingSlash)\n  ) {\n    path.pathname += \"/\";\n  }\n\n  return path;\n}\n\n/**\n * @private\n */\nexport function getToPathname(to: To): string | undefined {\n  // Empty strings should be treated the same as / paths\n  return to === \"\" || (to as Path).pathname === \"\"\n    ? \"/\"\n    : typeof to === \"string\"\n    ? parsePath(to).pathname\n    : to.pathname;\n}\n\n/**\n * @private\n */\nexport const joinPaths = (paths: string[]): string =>\n  paths.join(\"/\").replace(/\\/\\/+/g, \"/\");\n\n/**\n * @private\n */\nexport const normalizePathname = (pathname: string): string =>\n  pathname.replace(/\\/+$/, \"\").replace(/^\\/*/, \"/\");\n\n/**\n * @private\n */\nexport const normalizeSearch = (search: string): string =>\n  !search || search === \"?\"\n    ? \"\"\n    : search.startsWith(\"?\")\n    ? search\n    : \"?\" + search;\n\n/**\n * @private\n */\nexport const normalizeHash = (hash: string): string =>\n  !hash || hash === \"#\" ? \"\" : hash.startsWith(\"#\") ? hash : \"#\" + hash;\n\nexport type JsonFunction = <Data>(\n  data: Data,\n  init?: number | ResponseInit\n) => Response;\n\n/**\n * This is a shortcut for creating `application/json` responses. Converts `data`\n * to JSON and sets the `Content-Type` header.\n */\nexport const json: JsonFunction = (data, init = {}) => {\n  let responseInit = typeof init === \"number\" ? { status: init } : init;\n\n  let headers = new Headers(responseInit.headers);\n  if (!headers.has(\"Content-Type\")) {\n    headers.set(\"Content-Type\", \"application/json; charset=utf-8\");\n  }\n\n  return new Response(JSON.stringify(data), {\n    ...responseInit,\n    headers,\n  });\n};\n\nexport interface TrackedPromise extends Promise<any> {\n  _tracked?: boolean;\n  _data?: any;\n  _error?: any;\n}\n\nexport class AbortedDeferredError extends Error {}\n\nexport class DeferredData {\n  private pendingKeysSet: Set<string> = new Set<string>();\n  private controller: AbortController;\n  private abortPromise: Promise<void>;\n  private unlistenAbortSignal: () => void;\n  private subscribers: Set<(aborted: boolean, settledKey?: string) => void> =\n    new Set();\n  data: Record<string, unknown>;\n  init?: ResponseInit;\n  deferredKeys: string[] = [];\n\n  constructor(data: Record<string, unknown>, responseInit?: ResponseInit) {\n    invariant(\n      data && typeof data === \"object\" && !Array.isArray(data),\n      \"defer() only accepts plain objects\"\n    );\n\n    // Set up an AbortController + Promise we can race against to exit early\n    // cancellation\n    let reject: (e: AbortedDeferredError) => void;\n    this.abortPromise = new Promise((_, r) => (reject = r));\n    this.controller = new AbortController();\n    let onAbort = () =>\n      reject(new AbortedDeferredError(\"Deferred data aborted\"));\n    this.unlistenAbortSignal = () =>\n      this.controller.signal.removeEventListener(\"abort\", onAbort);\n    this.controller.signal.addEventListener(\"abort\", onAbort);\n\n    this.data = Object.entries(data).reduce(\n      (acc, [key, value]) =>\n        Object.assign(acc, {\n          [key]: this.trackPromise(key, value),\n        }),\n      {}\n    );\n\n    if (this.done) {\n      // All incoming values were resolved\n      this.unlistenAbortSignal();\n    }\n\n    this.init = responseInit;\n  }\n\n  private trackPromise(\n    key: string,\n    value: Promise<unknown> | unknown\n  ): TrackedPromise | unknown {\n    if (!(value instanceof Promise)) {\n      return value;\n    }\n\n    this.deferredKeys.push(key);\n    this.pendingKeysSet.add(key);\n\n    // We store a little wrapper promise that will be extended with\n    // _data/_error props upon resolve/reject\n    let promise: TrackedPromise = Promise.race([value, this.abortPromise]).then(\n      (data) => this.onSettle(promise, key, undefined, data as unknown),\n      (error) => this.onSettle(promise, key, error as unknown)\n    );\n\n    // Register rejection listeners to avoid uncaught promise rejections on\n    // errors or aborted deferred values\n    promise.catch(() => {});\n\n    Object.defineProperty(promise, \"_tracked\", { get: () => true });\n    return promise;\n  }\n\n  private onSettle(\n    promise: TrackedPromise,\n    key: string,\n    error: unknown,\n    data?: unknown\n  ): unknown {\n    if (\n      this.controller.signal.aborted &&\n      error instanceof AbortedDeferredError\n    ) {\n      this.unlistenAbortSignal();\n      Object.defineProperty(promise, \"_error\", { get: () => error });\n      return Promise.reject(error);\n    }\n\n    this.pendingKeysSet.delete(key);\n\n    if (this.done) {\n      // Nothing left to abort!\n      this.unlistenAbortSignal();\n    }\n\n    // If the promise was resolved/rejected with undefined, we'll throw an error as you\n    // should always resolve with a value or null\n    if (error === undefined && data === undefined) {\n      let undefinedError = new Error(\n        `Deferred data for key \"${key}\" resolved/rejected with \\`undefined\\`, ` +\n          `you must resolve/reject with a value or \\`null\\`.`\n      );\n      Object.defineProperty(promise, \"_error\", { get: () => undefinedError });\n      this.emit(false, key);\n      return Promise.reject(undefinedError);\n    }\n\n    if (data === undefined) {\n      Object.defineProperty(promise, \"_error\", { get: () => error });\n      this.emit(false, key);\n      return Promise.reject(error);\n    }\n\n    Object.defineProperty(promise, \"_data\", { get: () => data });\n    this.emit(false, key);\n    return data;\n  }\n\n  private emit(aborted: boolean, settledKey?: string) {\n    this.subscribers.forEach((subscriber) => subscriber(aborted, settledKey));\n  }\n\n  subscribe(fn: (aborted: boolean, settledKey?: string) => void) {\n    this.subscribers.add(fn);\n    return () => this.subscribers.delete(fn);\n  }\n\n  cancel() {\n    this.controller.abort();\n    this.pendingKeysSet.forEach((v, k) => this.pendingKeysSet.delete(k));\n    this.emit(true);\n  }\n\n  async resolveData(signal: AbortSignal) {\n    let aborted = false;\n    if (!this.done) {\n      let onAbort = () => this.cancel();\n      signal.addEventListener(\"abort\", onAbort);\n      aborted = await new Promise((resolve) => {\n        this.subscribe((aborted) => {\n          signal.removeEventListener(\"abort\", onAbort);\n          if (aborted || this.done) {\n            resolve(aborted);\n          }\n        });\n      });\n    }\n    return aborted;\n  }\n\n  get done() {\n    return this.pendingKeysSet.size === 0;\n  }\n\n  get unwrappedData() {\n    invariant(\n      this.data !== null && this.done,\n      \"Can only unwrap data on initialized and settled deferreds\"\n    );\n\n    return Object.entries(this.data).reduce(\n      (acc, [key, value]) =>\n        Object.assign(acc, {\n          [key]: unwrapTrackedPromise(value),\n        }),\n      {}\n    );\n  }\n\n  get pendingKeys() {\n    return Array.from(this.pendingKeysSet);\n  }\n}\n\nfunction isTrackedPromise(value: any): value is TrackedPromise {\n  return (\n    value instanceof Promise && (value as TrackedPromise)._tracked === true\n  );\n}\n\nfunction unwrapTrackedPromise(value: any) {\n  if (!isTrackedPromise(value)) {\n    return value;\n  }\n\n  if (value._error) {\n    throw value._error;\n  }\n  return value._data;\n}\n\nexport type DeferFunction = (\n  data: Record<string, unknown>,\n  init?: number | ResponseInit\n) => DeferredData;\n\nexport const defer: DeferFunction = (data, init = {}) => {\n  let responseInit = typeof init === \"number\" ? { status: init } : init;\n\n  return new DeferredData(data, responseInit);\n};\n\nexport type RedirectFunction = (\n  url: string,\n  init?: number | ResponseInit\n) => Response;\n\n/**\n * A redirect response. Sets the status code and the `Location` header.\n * Defaults to \"302 Found\".\n */\nexport const redirect: RedirectFunction = (url, init = 302) => {\n  let responseInit = init;\n  if (typeof responseInit === \"number\") {\n    responseInit = { status: responseInit };\n  } else if (typeof responseInit.status === \"undefined\") {\n    responseInit.status = 302;\n  }\n\n  let headers = new Headers(responseInit.headers);\n  headers.set(\"Location\", url);\n\n  return new Response(null, {\n    ...responseInit,\n    headers,\n  });\n};\n\n/**\n * @private\n * Utility class we use to hold auto-unwrapped 4xx/5xx Response bodies\n */\nexport class ErrorResponse {\n  status: number;\n  statusText: string;\n  data: any;\n  error?: Error;\n  internal: boolean;\n\n  constructor(\n    status: number,\n    statusText: string | undefined,\n    data: any,\n    internal = false\n  ) {\n    this.status = status;\n    this.statusText = statusText || \"\";\n    this.internal = internal;\n    if (data instanceof Error) {\n      this.data = data.toString();\n      this.error = data;\n    } else {\n      this.data = data;\n    }\n  }\n}\n\n/**\n * Check if the given error is an ErrorResponse generated from a 4xx/5xx\n * Response thrown from an action/loader\n */\nexport function isRouteErrorResponse(error: any): error is ErrorResponse {\n  return (\n    error != null &&\n    typeof error.status === \"number\" &&\n    typeof error.statusText === \"string\" &&\n    typeof error.internal === \"boolean\" &&\n    \"data\" in error\n  );\n}\n", "import type { History, Location, Path, To } from \"./history\";\nimport {\n  Action as HistoryAction,\n  createLocation,\n  createPath,\n  invariant,\n  parsePath,\n  warning,\n} from \"./history\";\nimport type {\n  DataResult,\n  DeferredData,\n  AgnosticDataRouteMatch,\n  AgnosticDataRouteObject,\n  DeferredResult,\n  ErrorResult,\n  FormEncType,\n  FormMethod,\n  DetectErrorBoundaryFunction,\n  RedirectResult,\n  RouteData,\n  AgnosticRouteObject,\n  Submission,\n  SuccessResult,\n  AgnosticRouteMatch,\n  ShouldRevalidateFunction,\n  RouteManifest,\n  ImmutableRouteKey,\n  ActionFunction,\n  LoaderFunction,\n  V7_MutationFormMethod,\n  V7_FormMethod,\n  HTMLFormMethod,\n  MutationFormMethod,\n  MapRoutePropertiesFunction,\n} from \"./utils\";\nimport {\n  ErrorResponse,\n  ResultType,\n  convertRoutesToDataRoutes,\n  getPathContributingMatches,\n  immutableRouteKeys,\n  isRouteErrorResponse,\n  joinPaths,\n  matchRoutes,\n  resolveTo,\n  stripBasename,\n} from \"./utils\";\n\n////////////////////////////////////////////////////////////////////////////////\n//#region Types and Constants\n////////////////////////////////////////////////////////////////////////////////\n\n/**\n * A Router instance manages all navigation and data loading/mutations\n */\nexport interface Router {\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Return the basename for the router\n   */\n  get basename(): RouterInit[\"basename\"];\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Return the current state of the router\n   */\n  get state(): RouterState;\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Return the routes for this router instance\n   */\n  get routes(): AgnosticDataRouteObject[];\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Initialize the router, including adding history listeners and kicking off\n   * initial data fetches.  Returns a function to cleanup listeners and abort\n   * any in-progress loads\n   */\n  initialize(): Router;\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Subscribe to router.state updates\n   *\n   * @param fn function to call with the new state\n   */\n  subscribe(fn: RouterSubscriber): () => void;\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Enable scroll restoration behavior in the router\n   *\n   * @param savedScrollPositions Object that will manage positions, in case\n   *                             it's being restored from sessionStorage\n   * @param getScrollPosition    Function to get the active Y scroll position\n   * @param getKey               Function to get the key to use for restoration\n   */\n  enableScrollRestoration(\n    savedScrollPositions: Record<string, number>,\n    getScrollPosition: GetScrollPositionFunction,\n    getKey?: GetScrollRestorationKeyFunction\n  ): () => void;\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Navigate forward/backward in the history stack\n   * @param to Delta to move in the history stack\n   */\n  navigate(to: number): Promise<void>;\n\n  /**\n   * Navigate to the given path\n   * @param to Path to navigate to\n   * @param opts Navigation options (method, submission, etc.)\n   */\n  navigate(to: To | null, opts?: RouterNavigateOptions): Promise<void>;\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Trigger a fetcher load/submission\n   *\n   * @param key     Fetcher key\n   * @param routeId Route that owns the fetcher\n   * @param href    href to fetch\n   * @param opts    Fetcher options, (method, submission, etc.)\n   */\n  fetch(\n    key: string,\n    routeId: string,\n    href: string | null,\n    opts?: RouterFetchOptions\n  ): void;\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Trigger a revalidation of all current route loaders and fetcher loads\n   */\n  revalidate(): void;\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Utility function to create an href for the given location\n   * @param location\n   */\n  createHref(location: Location | URL): string;\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Utility function to URL encode a destination path according to the internal\n   * history implementation\n   * @param to\n   */\n  encodeLocation(to: To): Path;\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Get/create a fetcher for the given key\n   * @param key\n   */\n  getFetcher<TData = any>(key?: string): Fetcher<TData>;\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Delete the fetcher for a given key\n   * @param key\n   */\n  deleteFetcher(key?: string): void;\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Cleanup listeners and abort any in-progress loads\n   */\n  dispose(): void;\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Get a navigation blocker\n   * @param key The identifier for the blocker\n   * @param fn The blocker function implementation\n   */\n  getBlocker(key: string, fn: BlockerFunction): Blocker;\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Delete a navigation blocker\n   * @param key The identifier for the blocker\n   */\n  deleteBlocker(key: string): void;\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * HMR needs to pass in-flight route updates to React Router\n   * TODO: Replace this with granular route update APIs (addRoute, updateRoute, deleteRoute)\n   */\n  _internalSetRoutes(routes: AgnosticRouteObject[]): void;\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Internal fetch AbortControllers accessed by unit tests\n   */\n  _internalFetchControllers: Map<string, AbortController>;\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Internal pending DeferredData instances accessed by unit tests\n   */\n  _internalActiveDeferreds: Map<string, DeferredData>;\n}\n\n/**\n * State maintained internally by the router.  During a navigation, all states\n * reflect the the \"old\" location unless otherwise noted.\n */\nexport interface RouterState {\n  /**\n   * The action of the most recent navigation\n   */\n  historyAction: HistoryAction;\n\n  /**\n   * The current location reflected by the router\n   */\n  location: Location;\n\n  /**\n   * The current set of route matches\n   */\n  matches: AgnosticDataRouteMatch[];\n\n  /**\n   * Tracks whether we've completed our initial data load\n   */\n  initialized: boolean;\n\n  /**\n   * Current scroll position we should start at for a new view\n   *  - number -> scroll position to restore to\n   *  - false -> do not restore scroll at all (used during submissions)\n   *  - null -> don't have a saved position, scroll to hash or top of page\n   */\n  restoreScrollPosition: number | false | null;\n\n  /**\n   * Indicate whether this navigation should skip resetting the scroll position\n   * if we are unable to restore the scroll position\n   */\n  preventScrollReset: boolean;\n\n  /**\n   * Tracks the state of the current navigation\n   */\n  navigation: Navigation;\n\n  /**\n   * Tracks any in-progress revalidations\n   */\n  revalidation: RevalidationState;\n\n  /**\n   * Data from the loaders for the current matches\n   */\n  loaderData: RouteData;\n\n  /**\n   * Data from the action for the current matches\n   */\n  actionData: RouteData | null;\n\n  /**\n   * Errors caught from loaders for the current matches\n   */\n  errors: RouteData | null;\n\n  /**\n   * Map of current fetchers\n   */\n  fetchers: Map<string, Fetcher>;\n\n  /**\n   * Map of current blockers\n   */\n  blockers: Map<string, Blocker>;\n}\n\n/**\n * Data that can be passed into hydrate a Router from SSR\n */\nexport type HydrationState = Partial<\n  Pick<RouterState, \"loaderData\" | \"actionData\" | \"errors\">\n>;\n\n/**\n * Future flags to toggle new feature behavior\n */\nexport interface FutureConfig {\n  v7_normalizeFormMethod: boolean;\n  v7_prependBasename: boolean;\n}\n\n/**\n * Initialization options for createRouter\n */\nexport interface RouterInit {\n  routes: AgnosticRouteObject[];\n  history: History;\n  basename?: string;\n  /**\n   * @deprecated Use `mapRouteProperties` instead\n   */\n  detectErrorBoundary?: DetectErrorBoundaryFunction;\n  mapRouteProperties?: MapRoutePropertiesFunction;\n  future?: Partial<FutureConfig>;\n  hydrationData?: HydrationState;\n  window?: Window;\n}\n\n/**\n * State returned from a server-side query() call\n */\nexport interface StaticHandlerContext {\n  basename: Router[\"basename\"];\n  location: RouterState[\"location\"];\n  matches: RouterState[\"matches\"];\n  loaderData: RouterState[\"loaderData\"];\n  actionData: RouterState[\"actionData\"];\n  errors: RouterState[\"errors\"];\n  statusCode: number;\n  loaderHeaders: Record<string, Headers>;\n  actionHeaders: Record<string, Headers>;\n  activeDeferreds: Record<string, DeferredData> | null;\n  _deepestRenderedBoundaryId?: string | null;\n}\n\n/**\n * A StaticHandler instance manages a singular SSR navigation/fetch event\n */\nexport interface StaticHandler {\n  dataRoutes: AgnosticDataRouteObject[];\n  query(\n    request: Request,\n    opts?: { requestContext?: unknown }\n  ): Promise<StaticHandlerContext | Response>;\n  queryRoute(\n    request: Request,\n    opts?: { routeId?: string; requestContext?: unknown }\n  ): Promise<any>;\n}\n\n/**\n * Subscriber function signature for changes to router state\n */\nexport interface RouterSubscriber {\n  (state: RouterState): void;\n}\n\ninterface UseMatchesMatch {\n  id: string;\n  pathname: string;\n  params: AgnosticRouteMatch[\"params\"];\n  data: unknown;\n  handle: unknown;\n}\n\n/**\n * Function signature for determining the key to be used in scroll restoration\n * for a given location\n */\nexport interface GetScrollRestorationKeyFunction {\n  (location: Location, matches: UseMatchesMatch[]): string | null;\n}\n\n/**\n * Function signature for determining the current scroll position\n */\nexport interface GetScrollPositionFunction {\n  (): number;\n}\n\nexport type RelativeRoutingType = \"route\" | \"path\";\n\n// Allowed for any navigation or fetch\ntype BaseNavigateOrFetchOptions = {\n  preventScrollReset?: boolean;\n  relative?: RelativeRoutingType;\n};\n\n// Only allowed for navigations\ntype BaseNavigateOptions = BaseNavigateOrFetchOptions & {\n  replace?: boolean;\n  state?: any;\n  fromRouteId?: string;\n};\n\n// Only allowed for submission navigations\ntype BaseSubmissionOptions = {\n  formMethod?: HTMLFormMethod;\n  formEncType?: FormEncType;\n} & (\n  | { formData: FormData; body?: undefined }\n  | { formData?: undefined; body: any }\n);\n\n/**\n * Options for a navigate() call for a normal (non-submission) navigation\n */\ntype LinkNavigateOptions = BaseNavigateOptions;\n\n/**\n * Options for a navigate() call for a submission navigation\n */\ntype SubmissionNavigateOptions = BaseNavigateOptions & BaseSubmissionOptions;\n\n/**\n * Options to pass to navigate() for a navigation\n */\nexport type RouterNavigateOptions =\n  | LinkNavigateOptions\n  | SubmissionNavigateOptions;\n\n/**\n * Options for a fetch() load\n */\ntype LoadFetchOptions = BaseNavigateOrFetchOptions;\n\n/**\n * Options for a fetch() submission\n */\ntype SubmitFetchOptions = BaseNavigateOrFetchOptions & BaseSubmissionOptions;\n\n/**\n * Options to pass to fetch()\n */\nexport type RouterFetchOptions = LoadFetchOptions | SubmitFetchOptions;\n\n/**\n * Potential states for state.navigation\n */\nexport type NavigationStates = {\n  Idle: {\n    state: \"idle\";\n    location: undefined;\n    formMethod: undefined;\n    formAction: undefined;\n    formEncType: undefined;\n    formData: undefined;\n    json: undefined;\n    text: undefined;\n  };\n  Loading: {\n    state: \"loading\";\n    location: Location;\n    formMethod: Submission[\"formMethod\"] | undefined;\n    formAction: Submission[\"formAction\"] | undefined;\n    formEncType: Submission[\"formEncType\"] | undefined;\n    formData: Submission[\"formData\"] | undefined;\n    json: Submission[\"json\"] | undefined;\n    text: Submission[\"text\"] | undefined;\n  };\n  Submitting: {\n    state: \"submitting\";\n    location: Location;\n    formMethod: Submission[\"formMethod\"];\n    formAction: Submission[\"formAction\"];\n    formEncType: Submission[\"formEncType\"];\n    formData: Submission[\"formData\"];\n    json: Submission[\"json\"];\n    text: Submission[\"text\"];\n  };\n};\n\nexport type Navigation = NavigationStates[keyof NavigationStates];\n\nexport type RevalidationState = \"idle\" | \"loading\";\n\n/**\n * Potential states for fetchers\n */\ntype FetcherStates<TData = any> = {\n  Idle: {\n    state: \"idle\";\n    formMethod: undefined;\n    formAction: undefined;\n    formEncType: undefined;\n    text: undefined;\n    formData: undefined;\n    json: undefined;\n    data: TData | undefined;\n    \" _hasFetcherDoneAnything \"?: boolean;\n  };\n  Loading: {\n    state: \"loading\";\n    formMethod: Submission[\"formMethod\"] | undefined;\n    formAction: Submission[\"formAction\"] | undefined;\n    formEncType: Submission[\"formEncType\"] | undefined;\n    text: Submission[\"text\"] | undefined;\n    formData: Submission[\"formData\"] | undefined;\n    json: Submission[\"json\"] | undefined;\n    data: TData | undefined;\n    \" _hasFetcherDoneAnything \"?: boolean;\n  };\n  Submitting: {\n    state: \"submitting\";\n    formMethod: Submission[\"formMethod\"];\n    formAction: Submission[\"formAction\"];\n    formEncType: Submission[\"formEncType\"];\n    text: Submission[\"text\"];\n    formData: Submission[\"formData\"];\n    json: Submission[\"json\"];\n    data: TData | undefined;\n    \" _hasFetcherDoneAnything \"?: boolean;\n  };\n};\n\nexport type Fetcher<TData = any> =\n  FetcherStates<TData>[keyof FetcherStates<TData>];\n\ninterface BlockerBlocked {\n  state: \"blocked\";\n  reset(): void;\n  proceed(): void;\n  location: Location;\n}\n\ninterface BlockerUnblocked {\n  state: \"unblocked\";\n  reset: undefined;\n  proceed: undefined;\n  location: undefined;\n}\n\ninterface BlockerProceeding {\n  state: \"proceeding\";\n  reset: undefined;\n  proceed: undefined;\n  location: Location;\n}\n\nexport type Blocker = BlockerUnblocked | BlockerBlocked | BlockerProceeding;\n\nexport type BlockerFunction = (args: {\n  currentLocation: Location;\n  nextLocation: Location;\n  historyAction: HistoryAction;\n}) => boolean;\n\ninterface ShortCircuitable {\n  /**\n   * startNavigation does not need to complete the navigation because we\n   * redirected or got interrupted\n   */\n  shortCircuited?: boolean;\n}\n\ninterface HandleActionResult extends ShortCircuitable {\n  /**\n   * Error thrown from the current action, keyed by the route containing the\n   * error boundary to render the error.  To be committed to the state after\n   * loaders have completed\n   */\n  pendingActionError?: RouteData;\n  /**\n   * Data returned from the current action, keyed by the route owning the action.\n   * To be committed to the state after loaders have completed\n   */\n  pendingActionData?: RouteData;\n}\n\ninterface HandleLoadersResult extends ShortCircuitable {\n  /**\n   * loaderData returned from the current set of loaders\n   */\n  loaderData?: RouterState[\"loaderData\"];\n  /**\n   * errors thrown from the current set of loaders\n   */\n  errors?: RouterState[\"errors\"];\n}\n\n/**\n * Cached info for active fetcher.load() instances so they can participate\n * in revalidation\n */\ninterface FetchLoadMatch {\n  routeId: string;\n  path: string;\n}\n\n/**\n * Identified fetcher.load() calls that need to be revalidated\n */\ninterface RevalidatingFetcher extends FetchLoadMatch {\n  key: string;\n  match: AgnosticDataRouteMatch | null;\n  matches: AgnosticDataRouteMatch[] | null;\n  controller: AbortController | null;\n}\n\n/**\n * Wrapper object to allow us to throw any response out from callLoaderOrAction\n * for queryRouter while preserving whether or not it was thrown or returned\n * from the loader/action\n */\ninterface QueryRouteResponse {\n  type: ResultType.data | ResultType.error;\n  response: Response;\n}\n\nconst validMutationMethodsArr: MutationFormMethod[] = [\n  \"post\",\n  \"put\",\n  \"patch\",\n  \"delete\",\n];\nconst validMutationMethods = new Set<MutationFormMethod>(\n  validMutationMethodsArr\n);\n\nconst validRequestMethodsArr: FormMethod[] = [\n  \"get\",\n  ...validMutationMethodsArr,\n];\nconst validRequestMethods = new Set<FormMethod>(validRequestMethodsArr);\n\nconst redirectStatusCodes = new Set([301, 302, 303, 307, 308]);\nconst redirectPreserveMethodStatusCodes = new Set([307, 308]);\n\nexport const IDLE_NAVIGATION: NavigationStates[\"Idle\"] = {\n  state: \"idle\",\n  location: undefined,\n  formMethod: undefined,\n  formAction: undefined,\n  formEncType: undefined,\n  formData: undefined,\n  json: undefined,\n  text: undefined,\n};\n\nexport const IDLE_FETCHER: FetcherStates[\"Idle\"] = {\n  state: \"idle\",\n  data: undefined,\n  formMethod: undefined,\n  formAction: undefined,\n  formEncType: undefined,\n  formData: undefined,\n  json: undefined,\n  text: undefined,\n};\n\nexport const IDLE_BLOCKER: BlockerUnblocked = {\n  state: \"unblocked\",\n  proceed: undefined,\n  reset: undefined,\n  location: undefined,\n};\n\nconst ABSOLUTE_URL_REGEX = /^(?:[a-z][a-z0-9+.-]*:|\\/\\/)/i;\n\nconst defaultMapRouteProperties: MapRoutePropertiesFunction = (route) => ({\n  hasErrorBoundary: Boolean(route.hasErrorBoundary),\n});\n\n//#endregion\n\n////////////////////////////////////////////////////////////////////////////////\n//#region createRouter\n////////////////////////////////////////////////////////////////////////////////\n\n/**\n * Create a router and listen to history POP navigations\n */\nexport function createRouter(init: RouterInit): Router {\n  const routerWindow = init.window\n    ? init.window\n    : typeof window !== \"undefined\"\n    ? window\n    : undefined;\n  const isBrowser =\n    typeof routerWindow !== \"undefined\" &&\n    typeof routerWindow.document !== \"undefined\" &&\n    typeof routerWindow.document.createElement !== \"undefined\";\n  const isServer = !isBrowser;\n\n  invariant(\n    init.routes.length > 0,\n    \"You must provide a non-empty routes array to createRouter\"\n  );\n\n  let mapRouteProperties: MapRoutePropertiesFunction;\n  if (init.mapRouteProperties) {\n    mapRouteProperties = init.mapRouteProperties;\n  } else if (init.detectErrorBoundary) {\n    // If they are still using the deprecated version, wrap it with the new API\n    let detectErrorBoundary = init.detectErrorBoundary;\n    mapRouteProperties = (route) => ({\n      hasErrorBoundary: detectErrorBoundary(route),\n    });\n  } else {\n    mapRouteProperties = defaultMapRouteProperties;\n  }\n\n  // Routes keyed by ID\n  let manifest: RouteManifest = {};\n  // Routes in tree format for matching\n  let dataRoutes = convertRoutesToDataRoutes(\n    init.routes,\n    mapRouteProperties,\n    undefined,\n    manifest\n  );\n  let inFlightDataRoutes: AgnosticDataRouteObject[] | undefined;\n  let basename = init.basename || \"/\";\n  // Config driven behavior flags\n  let future: FutureConfig = {\n    v7_normalizeFormMethod: false,\n    v7_prependBasename: false,\n    ...init.future,\n  };\n  // Cleanup function for history\n  let unlistenHistory: (() => void) | null = null;\n  // Externally-provided functions to call on all state changes\n  let subscribers = new Set<RouterSubscriber>();\n  // Externally-provided object to hold scroll restoration locations during routing\n  let savedScrollPositions: Record<string, number> | null = null;\n  // Externally-provided function to get scroll restoration keys\n  let getScrollRestorationKey: GetScrollRestorationKeyFunction | null = null;\n  // Externally-provided function to get current scroll position\n  let getScrollPosition: GetScrollPositionFunction | null = null;\n  // One-time flag to control the initial hydration scroll restoration.  Because\n  // we don't get the saved positions from <ScrollRestoration /> until _after_\n  // the initial render, we need to manually trigger a separate updateState to\n  // send along the restoreScrollPosition\n  // Set to true if we have `hydrationData` since we assume we were SSR'd and that\n  // SSR did the initial scroll restoration.\n  let initialScrollRestored = init.hydrationData != null;\n\n  let initialMatches = matchRoutes(dataRoutes, init.history.location, basename);\n  let initialErrors: RouteData | null = null;\n\n  if (initialMatches == null) {\n    // If we do not match a user-provided-route, fall back to the root\n    // to allow the error boundary to take over\n    let error = getInternalRouterError(404, {\n      pathname: init.history.location.pathname,\n    });\n    let { matches, route } = getShortCircuitMatches(dataRoutes);\n    initialMatches = matches;\n    initialErrors = { [route.id]: error };\n  }\n\n  let initialized =\n    // All initialMatches need to be loaded before we're ready.  If we have lazy\n    // functions around still then we'll need to run them in initialize()\n    !initialMatches.some((m) => m.route.lazy) &&\n    // And we have to either have no loaders or have been provided hydrationData\n    (!initialMatches.some((m) => m.route.loader) || init.hydrationData != null);\n\n  let router: Router;\n  let state: RouterState = {\n    historyAction: init.history.action,\n    location: init.history.location,\n    matches: initialMatches,\n    initialized,\n    navigation: IDLE_NAVIGATION,\n    // Don't restore on initial updateState() if we were SSR'd\n    restoreScrollPosition: init.hydrationData != null ? false : null,\n    preventScrollReset: false,\n    revalidation: \"idle\",\n    loaderData: (init.hydrationData && init.hydrationData.loaderData) || {},\n    actionData: (init.hydrationData && init.hydrationData.actionData) || null,\n    errors: (init.hydrationData && init.hydrationData.errors) || initialErrors,\n    fetchers: new Map(),\n    blockers: new Map(),\n  };\n\n  // -- Stateful internal variables to manage navigations --\n  // Current navigation in progress (to be committed in completeNavigation)\n  let pendingAction: HistoryAction = HistoryAction.Pop;\n\n  // Should the current navigation prevent the scroll reset if scroll cannot\n  // be restored?\n  let pendingPreventScrollReset = false;\n\n  // AbortController for the active navigation\n  let pendingNavigationController: AbortController | null;\n\n  // We use this to avoid touching history in completeNavigation if a\n  // revalidation is entirely uninterrupted\n  let isUninterruptedRevalidation = false;\n\n  // Use this internal flag to force revalidation of all loaders:\n  //  - submissions (completed or interrupted)\n  //  - useRevalidator()\n  //  - X-Remix-Revalidate (from redirect)\n  let isRevalidationRequired = false;\n\n  // Use this internal array to capture routes that require revalidation due\n  // to a cancelled deferred on action submission\n  let cancelledDeferredRoutes: string[] = [];\n\n  // Use this internal array to capture fetcher loads that were cancelled by an\n  // action navigation and require revalidation\n  let cancelledFetcherLoads: string[] = [];\n\n  // AbortControllers for any in-flight fetchers\n  let fetchControllers = new Map<string, AbortController>();\n\n  // Track loads based on the order in which they started\n  let incrementingLoadId = 0;\n\n  // Track the outstanding pending navigation data load to be compared against\n  // the globally incrementing load when a fetcher load lands after a completed\n  // navigation\n  let pendingNavigationLoadId = -1;\n\n  // Fetchers that triggered data reloads as a result of their actions\n  let fetchReloadIds = new Map<string, number>();\n\n  // Fetchers that triggered redirect navigations\n  let fetchRedirectIds = new Set<string>();\n\n  // Most recent href/match for fetcher.load calls for fetchers\n  let fetchLoadMatches = new Map<string, FetchLoadMatch>();\n\n  // Store DeferredData instances for active route matches.  When a\n  // route loader returns defer() we stick one in here.  Then, when a nested\n  // promise resolves we update loaderData.  If a new navigation starts we\n  // cancel active deferreds for eliminated routes.\n  let activeDeferreds = new Map<string, DeferredData>();\n\n  // Store blocker functions in a separate Map outside of router state since\n  // we don't need to update UI state if they change\n  let blockerFunctions = new Map<string, BlockerFunction>();\n\n  // Flag to ignore the next history update, so we can revert the URL change on\n  // a POP navigation that was blocked by the user without touching router state\n  let ignoreNextHistoryUpdate = false;\n\n  // Initialize the router, all side effects should be kicked off from here.\n  // Implemented as a Fluent API for ease of:\n  //   let router = createRouter(init).initialize();\n  function initialize() {\n    // If history informs us of a POP navigation, start the navigation but do not update\n    // state.  We'll update our own state once the navigation completes\n    unlistenHistory = init.history.listen(\n      ({ action: historyAction, location, delta }) => {\n        // Ignore this event if it was just us resetting the URL from a\n        // blocked POP navigation\n        if (ignoreNextHistoryUpdate) {\n          ignoreNextHistoryUpdate = false;\n          return;\n        }\n\n        warning(\n          blockerFunctions.size === 0 || delta != null,\n          \"You are trying to use a blocker on a POP navigation to a location \" +\n            \"that was not created by @remix-run/router. This will fail silently in \" +\n            \"production. This can happen if you are navigating outside the router \" +\n            \"via `window.history.pushState`/`window.location.hash` instead of using \" +\n            \"router navigation APIs.  This can also happen if you are using \" +\n            \"createHashRouter and the user manually changes the URL.\"\n        );\n\n        let blockerKey = shouldBlockNavigation({\n          currentLocation: state.location,\n          nextLocation: location,\n          historyAction,\n        });\n\n        if (blockerKey && delta != null) {\n          // Restore the URL to match the current UI, but don't update router state\n          ignoreNextHistoryUpdate = true;\n          init.history.go(delta * -1);\n\n          // Put the blocker into a blocked state\n          updateBlocker(blockerKey, {\n            state: \"blocked\",\n            location,\n            proceed() {\n              updateBlocker(blockerKey!, {\n                state: \"proceeding\",\n                proceed: undefined,\n                reset: undefined,\n                location,\n              });\n              // Re-do the same POP navigation we just blocked\n              init.history.go(delta);\n            },\n            reset() {\n              let blockers = new Map(state.blockers);\n              blockers.set(blockerKey!, IDLE_BLOCKER);\n              updateState({ blockers });\n            },\n          });\n          return;\n        }\n\n        return startNavigation(historyAction, location);\n      }\n    );\n\n    // Kick off initial data load if needed.  Use Pop to avoid modifying history\n    // Note we don't do any handling of lazy here.  For SPA's it'll get handled\n    // in the normal navigation flow.  For SSR it's expected that lazy modules are\n    // resolved prior to router creation since we can't go into a fallbackElement\n    // UI for SSR'd apps\n    if (!state.initialized) {\n      startNavigation(HistoryAction.Pop, state.location);\n    }\n\n    return router;\n  }\n\n  // Clean up a router and it's side effects\n  function dispose() {\n    if (unlistenHistory) {\n      unlistenHistory();\n    }\n    subscribers.clear();\n    pendingNavigationController && pendingNavigationController.abort();\n    state.fetchers.forEach((_, key) => deleteFetcher(key));\n    state.blockers.forEach((_, key) => deleteBlocker(key));\n  }\n\n  // Subscribe to state updates for the router\n  function subscribe(fn: RouterSubscriber) {\n    subscribers.add(fn);\n    return () => subscribers.delete(fn);\n  }\n\n  // Update our state and notify the calling context of the change\n  function updateState(newState: Partial<RouterState>): void {\n    state = {\n      ...state,\n      ...newState,\n    };\n    subscribers.forEach((subscriber) => subscriber(state));\n  }\n\n  // Complete a navigation returning the state.navigation back to the IDLE_NAVIGATION\n  // and setting state.[historyAction/location/matches] to the new route.\n  // - Location is a required param\n  // - Navigation will always be set to IDLE_NAVIGATION\n  // - Can pass any other state in newState\n  function completeNavigation(\n    location: Location,\n    newState: Partial<Omit<RouterState, \"action\" | \"location\" | \"navigation\">>\n  ): void {\n    // Deduce if we're in a loading/actionReload state:\n    // - We have committed actionData in the store\n    // - The current navigation was a mutation submission\n    // - We're past the submitting state and into the loading state\n    // - The location being loaded is not the result of a redirect\n    let isActionReload =\n      state.actionData != null &&\n      state.navigation.formMethod != null &&\n      isMutationMethod(state.navigation.formMethod) &&\n      state.navigation.state === \"loading\" &&\n      location.state?._isRedirect !== true;\n\n    let actionData: RouteData | null;\n    if (newState.actionData) {\n      if (Object.keys(newState.actionData).length > 0) {\n        actionData = newState.actionData;\n      } else {\n        // Empty actionData -> clear prior actionData due to an action error\n        actionData = null;\n      }\n    } else if (isActionReload) {\n      // Keep the current data if we're wrapping up the action reload\n      actionData = state.actionData;\n    } else {\n      // Clear actionData on any other completed navigations\n      actionData = null;\n    }\n\n    // Always preserve any existing loaderData from re-used routes\n    let loaderData = newState.loaderData\n      ? mergeLoaderData(\n          state.loaderData,\n          newState.loaderData,\n          newState.matches || [],\n          newState.errors\n        )\n      : state.loaderData;\n\n    // On a successful navigation we can assume we got through all blockers\n    // so we can start fresh\n    let blockers = state.blockers;\n    if (blockers.size > 0) {\n      blockers = new Map(blockers);\n      blockers.forEach((_, k) => blockers.set(k, IDLE_BLOCKER));\n    }\n\n    // Always respect the user flag.  Otherwise don't reset on mutation\n    // submission navigations unless they redirect\n    let preventScrollReset =\n      pendingPreventScrollReset === true ||\n      (state.navigation.formMethod != null &&\n        isMutationMethod(state.navigation.formMethod) &&\n        location.state?._isRedirect !== true);\n\n    if (inFlightDataRoutes) {\n      dataRoutes = inFlightDataRoutes;\n      inFlightDataRoutes = undefined;\n    }\n\n    if (isUninterruptedRevalidation) {\n      // If this was an uninterrupted revalidation then do not touch history\n    } else if (pendingAction === HistoryAction.Pop) {\n      // Do nothing for POP - URL has already been updated\n    } else if (pendingAction === HistoryAction.Push) {\n      init.history.push(location, location.state);\n    } else if (pendingAction === HistoryAction.Replace) {\n      init.history.replace(location, location.state);\n    }\n\n    updateState({\n      ...newState, // matches, errors, fetchers go through as-is\n      actionData,\n      loaderData,\n      historyAction: pendingAction,\n      location,\n      initialized: true,\n      navigation: IDLE_NAVIGATION,\n      revalidation: \"idle\",\n      restoreScrollPosition: getSavedScrollPosition(\n        location,\n        newState.matches || state.matches\n      ),\n      preventScrollReset,\n      blockers,\n    });\n\n    // Reset stateful navigation vars\n    pendingAction = HistoryAction.Pop;\n    pendingPreventScrollReset = false;\n    isUninterruptedRevalidation = false;\n    isRevalidationRequired = false;\n    cancelledDeferredRoutes = [];\n    cancelledFetcherLoads = [];\n  }\n\n  // Trigger a navigation event, which can either be a numerical POP or a PUSH\n  // replace with an optional submission\n  async function navigate(\n    to: number | To | null,\n    opts?: RouterNavigateOptions\n  ): Promise<void> {\n    if (typeof to === \"number\") {\n      init.history.go(to);\n      return;\n    }\n\n    let normalizedPath = normalizeTo(\n      state.location,\n      state.matches,\n      basename,\n      future.v7_prependBasename,\n      to,\n      opts?.fromRouteId,\n      opts?.relative\n    );\n    let { path, submission, error } = normalizeNavigateOptions(\n      future.v7_normalizeFormMethod,\n      false,\n      normalizedPath,\n      opts\n    );\n\n    let currentLocation = state.location;\n    let nextLocation = createLocation(state.location, path, opts && opts.state);\n\n    // When using navigate as a PUSH/REPLACE we aren't reading an already-encoded\n    // URL from window.location, so we need to encode it here so the behavior\n    // remains the same as POP and non-data-router usages.  new URL() does all\n    // the same encoding we'd get from a history.pushState/window.location read\n    // without having to touch history\n    nextLocation = {\n      ...nextLocation,\n      ...init.history.encodeLocation(nextLocation),\n    };\n\n    let userReplace = opts && opts.replace != null ? opts.replace : undefined;\n\n    let historyAction = HistoryAction.Push;\n\n    if (userReplace === true) {\n      historyAction = HistoryAction.Replace;\n    } else if (userReplace === false) {\n      // no-op\n    } else if (\n      submission != null &&\n      isMutationMethod(submission.formMethod) &&\n      submission.formAction === state.location.pathname + state.location.search\n    ) {\n      // By default on submissions to the current location we REPLACE so that\n      // users don't have to double-click the back button to get to the prior\n      // location.  If the user redirects to a different location from the\n      // action/loader this will be ignored and the redirect will be a PUSH\n      historyAction = HistoryAction.Replace;\n    }\n\n    let preventScrollReset =\n      opts && \"preventScrollReset\" in opts\n        ? opts.preventScrollReset === true\n        : undefined;\n\n    let blockerKey = shouldBlockNavigation({\n      currentLocation,\n      nextLocation,\n      historyAction,\n    });\n\n    if (blockerKey) {\n      // Put the blocker into a blocked state\n      updateBlocker(blockerKey, {\n        state: \"blocked\",\n        location: nextLocation,\n        proceed() {\n          updateBlocker(blockerKey!, {\n            state: \"proceeding\",\n            proceed: undefined,\n            reset: undefined,\n            location: nextLocation,\n          });\n          // Send the same navigation through\n          navigate(to, opts);\n        },\n        reset() {\n          let blockers = new Map(state.blockers);\n          blockers.set(blockerKey!, IDLE_BLOCKER);\n          updateState({ blockers });\n        },\n      });\n      return;\n    }\n\n    return await startNavigation(historyAction, nextLocation, {\n      submission,\n      // Send through the formData serialization error if we have one so we can\n      // render at the right error boundary after we match routes\n      pendingError: error,\n      preventScrollReset,\n      replace: opts && opts.replace,\n    });\n  }\n\n  // Revalidate all current loaders.  If a navigation is in progress or if this\n  // is interrupted by a navigation, allow this to \"succeed\" by calling all\n  // loaders during the next loader round\n  function revalidate() {\n    interruptActiveLoads();\n    updateState({ revalidation: \"loading\" });\n\n    // If we're currently submitting an action, we don't need to start a new\n    // navigation, we'll just let the follow up loader execution call all loaders\n    if (state.navigation.state === \"submitting\") {\n      return;\n    }\n\n    // If we're currently in an idle state, start a new navigation for the current\n    // action/location and mark it as uninterrupted, which will skip the history\n    // update in completeNavigation\n    if (state.navigation.state === \"idle\") {\n      startNavigation(state.historyAction, state.location, {\n        startUninterruptedRevalidation: true,\n      });\n      return;\n    }\n\n    // Otherwise, if we're currently in a loading state, just start a new\n    // navigation to the navigation.location but do not trigger an uninterrupted\n    // revalidation so that history correctly updates once the navigation completes\n    startNavigation(\n      pendingAction || state.historyAction,\n      state.navigation.location,\n      { overrideNavigation: state.navigation }\n    );\n  }\n\n  // Start a navigation to the given action/location.  Can optionally provide a\n  // overrideNavigation which will override the normalLoad in the case of a redirect\n  // navigation\n  async function startNavigation(\n    historyAction: HistoryAction,\n    location: Location,\n    opts?: {\n      submission?: Submission;\n      fetcherSubmission?: Submission;\n      overrideNavigation?: Navigation;\n      pendingError?: ErrorResponse;\n      startUninterruptedRevalidation?: boolean;\n      preventScrollReset?: boolean;\n      replace?: boolean;\n    }\n  ): Promise<void> {\n    // Abort any in-progress navigations and start a new one. Unset any ongoing\n    // uninterrupted revalidations unless told otherwise, since we want this\n    // new navigation to update history normally\n    pendingNavigationController && pendingNavigationController.abort();\n    pendingNavigationController = null;\n    pendingAction = historyAction;\n    isUninterruptedRevalidation =\n      (opts && opts.startUninterruptedRevalidation) === true;\n\n    // Save the current scroll position every time we start a new navigation,\n    // and track whether we should reset scroll on completion\n    saveScrollPosition(state.location, state.matches);\n    pendingPreventScrollReset = (opts && opts.preventScrollReset) === true;\n\n    let routesToUse = inFlightDataRoutes || dataRoutes;\n    let loadingNavigation = opts && opts.overrideNavigation;\n    let matches = matchRoutes(routesToUse, location, basename);\n\n    // Short circuit with a 404 on the root error boundary if we match nothing\n    if (!matches) {\n      let error = getInternalRouterError(404, { pathname: location.pathname });\n      let { matches: notFoundMatches, route } =\n        getShortCircuitMatches(routesToUse);\n      // Cancel all pending deferred on 404s since we don't keep any routes\n      cancelActiveDeferreds();\n      completeNavigation(location, {\n        matches: notFoundMatches,\n        loaderData: {},\n        errors: {\n          [route.id]: error,\n        },\n      });\n      return;\n    }\n\n    // Short circuit if it's only a hash change and not a revalidation or\n    // mutation submission.\n    //\n    // Ignore on initial page loads because since the initial load will always\n    // be \"same hash\".  For example, on /page#hash and submit a <Form method=\"post\">\n    // which will default to a navigation to /page\n    if (\n      state.initialized &&\n      !isRevalidationRequired &&\n      isHashChangeOnly(state.location, location) &&\n      !(opts && opts.submission && isMutationMethod(opts.submission.formMethod))\n    ) {\n      completeNavigation(location, { matches });\n      return;\n    }\n\n    // Create a controller/Request for this navigation\n    pendingNavigationController = new AbortController();\n    let request = createClientSideRequest(\n      init.history,\n      location,\n      pendingNavigationController.signal,\n      opts && opts.submission\n    );\n    let pendingActionData: RouteData | undefined;\n    let pendingError: RouteData | undefined;\n\n    if (opts && opts.pendingError) {\n      // If we have a pendingError, it means the user attempted a GET submission\n      // with binary FormData so assign here and skip to handleLoaders.  That\n      // way we handle calling loaders above the boundary etc.  It's not really\n      // different from an actionError in that sense.\n      pendingError = {\n        [findNearestBoundary(matches).route.id]: opts.pendingError,\n      };\n    } else if (\n      opts &&\n      opts.submission &&\n      isMutationMethod(opts.submission.formMethod)\n    ) {\n      // Call action if we received an action submission\n      let actionOutput = await handleAction(\n        request,\n        location,\n        opts.submission,\n        matches,\n        { replace: opts.replace }\n      );\n\n      if (actionOutput.shortCircuited) {\n        return;\n      }\n\n      pendingActionData = actionOutput.pendingActionData;\n      pendingError = actionOutput.pendingActionError;\n      loadingNavigation = getLoadingNavigation(location, opts.submission);\n\n      // Create a GET request for the loaders\n      request = new Request(request.url, { signal: request.signal });\n    }\n\n    // Call loaders\n    let { shortCircuited, loaderData, errors } = await handleLoaders(\n      request,\n      location,\n      matches,\n      loadingNavigation,\n      opts && opts.submission,\n      opts && opts.fetcherSubmission,\n      opts && opts.replace,\n      pendingActionData,\n      pendingError\n    );\n\n    if (shortCircuited) {\n      return;\n    }\n\n    // Clean up now that the action/loaders have completed.  Don't clean up if\n    // we short circuited because pendingNavigationController will have already\n    // been assigned to a new controller for the next navigation\n    pendingNavigationController = null;\n\n    completeNavigation(location, {\n      matches,\n      ...(pendingActionData ? { actionData: pendingActionData } : {}),\n      loaderData,\n      errors,\n    });\n  }\n\n  // Call the action matched by the leaf route for this navigation and handle\n  // redirects/errors\n  async function handleAction(\n    request: Request,\n    location: Location,\n    submission: Submission,\n    matches: AgnosticDataRouteMatch[],\n    opts: { replace?: boolean } = {}\n  ): Promise<HandleActionResult> {\n    interruptActiveLoads();\n\n    // Put us in a submitting state\n    let navigation = getSubmittingNavigation(location, submission);\n    updateState({ navigation });\n\n    // Call our action and get the result\n    let result: DataResult;\n    let actionMatch = getTargetMatch(matches, location);\n\n    if (!actionMatch.route.action && !actionMatch.route.lazy) {\n      result = {\n        type: ResultType.error,\n        error: getInternalRouterError(405, {\n          method: request.method,\n          pathname: location.pathname,\n          routeId: actionMatch.route.id,\n        }),\n      };\n    } else {\n      result = await callLoaderOrAction(\n        \"action\",\n        request,\n        actionMatch,\n        matches,\n        manifest,\n        mapRouteProperties,\n        basename\n      );\n\n      if (request.signal.aborted) {\n        return { shortCircuited: true };\n      }\n    }\n\n    if (isRedirectResult(result)) {\n      let replace: boolean;\n      if (opts && opts.replace != null) {\n        replace = opts.replace;\n      } else {\n        // If the user didn't explicity indicate replace behavior, replace if\n        // we redirected to the exact same location we're currently at to avoid\n        // double back-buttons\n        replace =\n          result.location === state.location.pathname + state.location.search;\n      }\n      await startRedirectNavigation(state, result, { submission, replace });\n      return { shortCircuited: true };\n    }\n\n    if (isErrorResult(result)) {\n      // Store off the pending error - we use it to determine which loaders\n      // to call and will commit it when we complete the navigation\n      let boundaryMatch = findNearestBoundary(matches, actionMatch.route.id);\n\n      // By default, all submissions are REPLACE navigations, but if the\n      // action threw an error that'll be rendered in an errorElement, we fall\n      // back to PUSH so that the user can use the back button to get back to\n      // the pre-submission form location to try again\n      if ((opts && opts.replace) !== true) {\n        pendingAction = HistoryAction.Push;\n      }\n\n      return {\n        // Send back an empty object we can use to clear out any prior actionData\n        pendingActionData: {},\n        pendingActionError: { [boundaryMatch.route.id]: result.error },\n      };\n    }\n\n    if (isDeferredResult(result)) {\n      throw getInternalRouterError(400, { type: \"defer-action\" });\n    }\n\n    return {\n      pendingActionData: { [actionMatch.route.id]: result.data },\n    };\n  }\n\n  // Call all applicable loaders for the given matches, handling redirects,\n  // errors, etc.\n  async function handleLoaders(\n    request: Request,\n    location: Location,\n    matches: AgnosticDataRouteMatch[],\n    overrideNavigation?: Navigation,\n    submission?: Submission,\n    fetcherSubmission?: Submission,\n    replace?: boolean,\n    pendingActionData?: RouteData,\n    pendingError?: RouteData\n  ): Promise<HandleLoadersResult> {\n    // Figure out the right navigation we want to use for data loading\n    let loadingNavigation =\n      overrideNavigation || getLoadingNavigation(location, submission);\n\n    // If this was a redirect from an action we don't have a \"submission\" but\n    // we have it on the loading navigation so use that if available\n    let activeSubmission =\n      submission ||\n      fetcherSubmission ||\n      getSubmissionFromNavigation(loadingNavigation);\n\n    let routesToUse = inFlightDataRoutes || dataRoutes;\n    let [matchesToLoad, revalidatingFetchers] = getMatchesToLoad(\n      init.history,\n      state,\n      matches,\n      activeSubmission,\n      location,\n      isRevalidationRequired,\n      cancelledDeferredRoutes,\n      cancelledFetcherLoads,\n      fetchLoadMatches,\n      fetchRedirectIds,\n      routesToUse,\n      basename,\n      pendingActionData,\n      pendingError\n    );\n\n    // Cancel pending deferreds for no-longer-matched routes or routes we're\n    // about to reload.  Note that if this is an action reload we would have\n    // already cancelled all pending deferreds so this would be a no-op\n    cancelActiveDeferreds(\n      (routeId) =>\n        !(matches && matches.some((m) => m.route.id === routeId)) ||\n        (matchesToLoad && matchesToLoad.some((m) => m.route.id === routeId))\n    );\n\n    pendingNavigationLoadId = ++incrementingLoadId;\n\n    // Short circuit if we have no loaders to run\n    if (matchesToLoad.length === 0 && revalidatingFetchers.length === 0) {\n      let updatedFetchers = markFetchRedirectsDone();\n      completeNavigation(location, {\n        matches,\n        loaderData: {},\n        // Commit pending error if we're short circuiting\n        errors: pendingError || null,\n        ...(pendingActionData ? { actionData: pendingActionData } : {}),\n        ...(updatedFetchers ? { fetchers: new Map(state.fetchers) } : {}),\n      });\n      return { shortCircuited: true };\n    }\n\n    // If this is an uninterrupted revalidation, we remain in our current idle\n    // state.  If not, we need to switch to our loading state and load data,\n    // preserving any new action data or existing action data (in the case of\n    // a revalidation interrupting an actionReload)\n    if (!isUninterruptedRevalidation) {\n      revalidatingFetchers.forEach((rf) => {\n        let fetcher = state.fetchers.get(rf.key);\n        let revalidatingFetcher = getLoadingFetcher(\n          undefined,\n          fetcher ? fetcher.data : undefined\n        );\n        state.fetchers.set(rf.key, revalidatingFetcher);\n      });\n      let actionData = pendingActionData || state.actionData;\n      updateState({\n        navigation: loadingNavigation,\n        ...(actionData\n          ? Object.keys(actionData).length === 0\n            ? { actionData: null }\n            : { actionData }\n          : {}),\n        ...(revalidatingFetchers.length > 0\n          ? { fetchers: new Map(state.fetchers) }\n          : {}),\n      });\n    }\n\n    revalidatingFetchers.forEach((rf) => {\n      if (fetchControllers.has(rf.key)) {\n        abortFetcher(rf.key);\n      }\n      if (rf.controller) {\n        // Fetchers use an independent AbortController so that aborting a fetcher\n        // (via deleteFetcher) does not abort the triggering navigation that\n        // triggered the revalidation\n        fetchControllers.set(rf.key, rf.controller);\n      }\n    });\n\n    // Proxy navigation abort through to revalidation fetchers\n    let abortPendingFetchRevalidations = () =>\n      revalidatingFetchers.forEach((f) => abortFetcher(f.key));\n    if (pendingNavigationController) {\n      pendingNavigationController.signal.addEventListener(\n        \"abort\",\n        abortPendingFetchRevalidations\n      );\n    }\n\n    let { results, loaderResults, fetcherResults } =\n      await callLoadersAndMaybeResolveData(\n        state.matches,\n        matches,\n        matchesToLoad,\n        revalidatingFetchers,\n        request\n      );\n\n    if (request.signal.aborted) {\n      return { shortCircuited: true };\n    }\n\n    // Clean up _after_ loaders have completed.  Don't clean up if we short\n    // circuited because fetchControllers would have been aborted and\n    // reassigned to new controllers for the next navigation\n    if (pendingNavigationController) {\n      pendingNavigationController.signal.removeEventListener(\n        \"abort\",\n        abortPendingFetchRevalidations\n      );\n    }\n    revalidatingFetchers.forEach((rf) => fetchControllers.delete(rf.key));\n\n    // If any loaders returned a redirect Response, start a new REPLACE navigation\n    let redirect = findRedirect(results);\n    if (redirect) {\n      if (redirect.idx >= matchesToLoad.length) {\n        // If this redirect came from a fetcher make sure we mark it in\n        // fetchRedirectIds so it doesn't get revalidated on the next set of\n        // loader executions\n        let fetcherKey =\n          revalidatingFetchers[redirect.idx - matchesToLoad.length].key;\n        fetchRedirectIds.add(fetcherKey);\n      }\n      await startRedirectNavigation(state, redirect.result, { replace });\n      return { shortCircuited: true };\n    }\n\n    // Process and commit output from loaders\n    let { loaderData, errors } = processLoaderData(\n      state,\n      matches,\n      matchesToLoad,\n      loaderResults,\n      pendingError,\n      revalidatingFetchers,\n      fetcherResults,\n      activeDeferreds\n    );\n\n    // Wire up subscribers to update loaderData as promises settle\n    activeDeferreds.forEach((deferredData, routeId) => {\n      deferredData.subscribe((aborted) => {\n        // Note: No need to updateState here since the TrackedPromise on\n        // loaderData is stable across resolve/reject\n        // Remove this instance if we were aborted or if promises have settled\n        if (aborted || deferredData.done) {\n          activeDeferreds.delete(routeId);\n        }\n      });\n    });\n\n    let updatedFetchers = markFetchRedirectsDone();\n    let didAbortFetchLoads = abortStaleFetchLoads(pendingNavigationLoadId);\n    let shouldUpdateFetchers =\n      updatedFetchers || didAbortFetchLoads || revalidatingFetchers.length > 0;\n\n    return {\n      loaderData,\n      errors,\n      ...(shouldUpdateFetchers ? { fetchers: new Map(state.fetchers) } : {}),\n    };\n  }\n\n  function getFetcher<TData = any>(key: string): Fetcher<TData> {\n    return state.fetchers.get(key) || IDLE_FETCHER;\n  }\n\n  // Trigger a fetcher load/submit for the given fetcher key\n  function fetch(\n    key: string,\n    routeId: string,\n    href: string | null,\n    opts?: RouterFetchOptions\n  ) {\n    if (isServer) {\n      throw new Error(\n        \"router.fetch() was called during the server render, but it shouldn't be. \" +\n          \"You are likely calling a useFetcher() method in the body of your component. \" +\n          \"Try moving it to a useEffect or a callback.\"\n      );\n    }\n\n    if (fetchControllers.has(key)) abortFetcher(key);\n\n    let routesToUse = inFlightDataRoutes || dataRoutes;\n    let normalizedPath = normalizeTo(\n      state.location,\n      state.matches,\n      basename,\n      future.v7_prependBasename,\n      href,\n      routeId,\n      opts?.relative\n    );\n    let matches = matchRoutes(routesToUse, normalizedPath, basename);\n\n    if (!matches) {\n      setFetcherError(\n        key,\n        routeId,\n        getInternalRouterError(404, { pathname: normalizedPath })\n      );\n      return;\n    }\n\n    let { path, submission, error } = normalizeNavigateOptions(\n      future.v7_normalizeFormMethod,\n      true,\n      normalizedPath,\n      opts\n    );\n\n    if (error) {\n      setFetcherError(key, routeId, error);\n      return;\n    }\n\n    let match = getTargetMatch(matches, path);\n\n    pendingPreventScrollReset = (opts && opts.preventScrollReset) === true;\n\n    if (submission && isMutationMethod(submission.formMethod)) {\n      handleFetcherAction(key, routeId, path, match, matches, submission);\n      return;\n    }\n\n    // Store off the match so we can call it's shouldRevalidate on subsequent\n    // revalidations\n    fetchLoadMatches.set(key, { routeId, path });\n    handleFetcherLoader(key, routeId, path, match, matches, submission);\n  }\n\n  // Call the action for the matched fetcher.submit(), and then handle redirects,\n  // errors, and revalidation\n  async function handleFetcherAction(\n    key: string,\n    routeId: string,\n    path: string,\n    match: AgnosticDataRouteMatch,\n    requestMatches: AgnosticDataRouteMatch[],\n    submission: Submission\n  ) {\n    interruptActiveLoads();\n    fetchLoadMatches.delete(key);\n\n    if (!match.route.action && !match.route.lazy) {\n      let error = getInternalRouterError(405, {\n        method: submission.formMethod,\n        pathname: path,\n        routeId: routeId,\n      });\n      setFetcherError(key, routeId, error);\n      return;\n    }\n\n    // Put this fetcher into it's submitting state\n    let existingFetcher = state.fetchers.get(key);\n    let fetcher = getSubmittingFetcher(submission, existingFetcher);\n    state.fetchers.set(key, fetcher);\n    updateState({ fetchers: new Map(state.fetchers) });\n\n    // Call the action for the fetcher\n    let abortController = new AbortController();\n    let fetchRequest = createClientSideRequest(\n      init.history,\n      path,\n      abortController.signal,\n      submission\n    );\n    fetchControllers.set(key, abortController);\n\n    let originatingLoadId = incrementingLoadId;\n    let actionResult = await callLoaderOrAction(\n      \"action\",\n      fetchRequest,\n      match,\n      requestMatches,\n      manifest,\n      mapRouteProperties,\n      basename\n    );\n\n    if (fetchRequest.signal.aborted) {\n      // We can delete this so long as we weren't aborted by ou our own fetcher\n      // re-submit which would have put _new_ controller is in fetchControllers\n      if (fetchControllers.get(key) === abortController) {\n        fetchControllers.delete(key);\n      }\n      return;\n    }\n\n    if (isRedirectResult(actionResult)) {\n      fetchControllers.delete(key);\n      if (pendingNavigationLoadId > originatingLoadId) {\n        // A new navigation was kicked off after our action started, so that\n        // should take precedence over this redirect navigation.  We already\n        // set isRevalidationRequired so all loaders for the new route should\n        // fire unless opted out via shouldRevalidate\n        let doneFetcher = getDoneFetcher(undefined);\n        state.fetchers.set(key, doneFetcher);\n        updateState({ fetchers: new Map(state.fetchers) });\n        return;\n      } else {\n        fetchRedirectIds.add(key);\n        let loadingFetcher = getLoadingFetcher(submission);\n        state.fetchers.set(key, loadingFetcher);\n        updateState({ fetchers: new Map(state.fetchers) });\n\n        return startRedirectNavigation(state, actionResult, {\n          submission,\n          isFetchActionRedirect: true,\n        });\n      }\n    }\n\n    // Process any non-redirect errors thrown\n    if (isErrorResult(actionResult)) {\n      setFetcherError(key, routeId, actionResult.error);\n      return;\n    }\n\n    if (isDeferredResult(actionResult)) {\n      throw getInternalRouterError(400, { type: \"defer-action\" });\n    }\n\n    // Start the data load for current matches, or the next location if we're\n    // in the middle of a navigation\n    let nextLocation = state.navigation.location || state.location;\n    let revalidationRequest = createClientSideRequest(\n      init.history,\n      nextLocation,\n      abortController.signal\n    );\n    let routesToUse = inFlightDataRoutes || dataRoutes;\n    let matches =\n      state.navigation.state !== \"idle\"\n        ? matchRoutes(routesToUse, state.navigation.location, basename)\n        : state.matches;\n\n    invariant(matches, \"Didn't find any matches after fetcher action\");\n\n    let loadId = ++incrementingLoadId;\n    fetchReloadIds.set(key, loadId);\n\n    let loadFetcher = getLoadingFetcher(submission, actionResult.data);\n    state.fetchers.set(key, loadFetcher);\n\n    let [matchesToLoad, revalidatingFetchers] = getMatchesToLoad(\n      init.history,\n      state,\n      matches,\n      submission,\n      nextLocation,\n      isRevalidationRequired,\n      cancelledDeferredRoutes,\n      cancelledFetcherLoads,\n      fetchLoadMatches,\n      fetchRedirectIds,\n      routesToUse,\n      basename,\n      { [match.route.id]: actionResult.data },\n      undefined // No need to send through errors since we short circuit above\n    );\n\n    // Put all revalidating fetchers into the loading state, except for the\n    // current fetcher which we want to keep in it's current loading state which\n    // contains it's action submission info + action data\n    revalidatingFetchers\n      .filter((rf) => rf.key !== key)\n      .forEach((rf) => {\n        let staleKey = rf.key;\n        let existingFetcher = state.fetchers.get(staleKey);\n        let revalidatingFetcher = getLoadingFetcher(\n          undefined,\n          existingFetcher ? existingFetcher.data : undefined\n        );\n        state.fetchers.set(staleKey, revalidatingFetcher);\n        if (fetchControllers.has(staleKey)) {\n          abortFetcher(staleKey);\n        }\n        if (rf.controller) {\n          fetchControllers.set(staleKey, rf.controller);\n        }\n      });\n\n    updateState({ fetchers: new Map(state.fetchers) });\n\n    let abortPendingFetchRevalidations = () =>\n      revalidatingFetchers.forEach((rf) => abortFetcher(rf.key));\n\n    abortController.signal.addEventListener(\n      \"abort\",\n      abortPendingFetchRevalidations\n    );\n\n    let { results, loaderResults, fetcherResults } =\n      await callLoadersAndMaybeResolveData(\n        state.matches,\n        matches,\n        matchesToLoad,\n        revalidatingFetchers,\n        revalidationRequest\n      );\n\n    if (abortController.signal.aborted) {\n      return;\n    }\n\n    abortController.signal.removeEventListener(\n      \"abort\",\n      abortPendingFetchRevalidations\n    );\n\n    fetchReloadIds.delete(key);\n    fetchControllers.delete(key);\n    revalidatingFetchers.forEach((r) => fetchControllers.delete(r.key));\n\n    let redirect = findRedirect(results);\n    if (redirect) {\n      if (redirect.idx >= matchesToLoad.length) {\n        // If this redirect came from a fetcher make sure we mark it in\n        // fetchRedirectIds so it doesn't get revalidated on the next set of\n        // loader executions\n        let fetcherKey =\n          revalidatingFetchers[redirect.idx - matchesToLoad.length].key;\n        fetchRedirectIds.add(fetcherKey);\n      }\n      return startRedirectNavigation(state, redirect.result);\n    }\n\n    // Process and commit output from loaders\n    let { loaderData, errors } = processLoaderData(\n      state,\n      state.matches,\n      matchesToLoad,\n      loaderResults,\n      undefined,\n      revalidatingFetchers,\n      fetcherResults,\n      activeDeferreds\n    );\n\n    // Since we let revalidations complete even if the submitting fetcher was\n    // deleted, only put it back to idle if it hasn't been deleted\n    if (state.fetchers.has(key)) {\n      let doneFetcher = getDoneFetcher(actionResult.data);\n      state.fetchers.set(key, doneFetcher);\n    }\n\n    let didAbortFetchLoads = abortStaleFetchLoads(loadId);\n\n    // If we are currently in a navigation loading state and this fetcher is\n    // more recent than the navigation, we want the newer data so abort the\n    // navigation and complete it with the fetcher data\n    if (\n      state.navigation.state === \"loading\" &&\n      loadId > pendingNavigationLoadId\n    ) {\n      invariant(pendingAction, \"Expected pending action\");\n      pendingNavigationController && pendingNavigationController.abort();\n\n      completeNavigation(state.navigation.location, {\n        matches,\n        loaderData,\n        errors,\n        fetchers: new Map(state.fetchers),\n      });\n    } else {\n      // otherwise just update with the fetcher data, preserving any existing\n      // loaderData for loaders that did not need to reload.  We have to\n      // manually merge here since we aren't going through completeNavigation\n      updateState({\n        errors,\n        loaderData: mergeLoaderData(\n          state.loaderData,\n          loaderData,\n          matches,\n          errors\n        ),\n        ...(didAbortFetchLoads || revalidatingFetchers.length > 0\n          ? { fetchers: new Map(state.fetchers) }\n          : {}),\n      });\n      isRevalidationRequired = false;\n    }\n  }\n\n  // Call the matched loader for fetcher.load(), handling redirects, errors, etc.\n  async function handleFetcherLoader(\n    key: string,\n    routeId: string,\n    path: string,\n    match: AgnosticDataRouteMatch,\n    matches: AgnosticDataRouteMatch[],\n    submission?: Submission\n  ) {\n    let existingFetcher = state.fetchers.get(key);\n    // Put this fetcher into it's loading state\n    let loadingFetcher = getLoadingFetcher(\n      submission,\n      existingFetcher ? existingFetcher.data : undefined\n    );\n    state.fetchers.set(key, loadingFetcher);\n    updateState({ fetchers: new Map(state.fetchers) });\n\n    // Call the loader for this fetcher route match\n    let abortController = new AbortController();\n    let fetchRequest = createClientSideRequest(\n      init.history,\n      path,\n      abortController.signal\n    );\n    fetchControllers.set(key, abortController);\n\n    let originatingLoadId = incrementingLoadId;\n    let result: DataResult = await callLoaderOrAction(\n      \"loader\",\n      fetchRequest,\n      match,\n      matches,\n      manifest,\n      mapRouteProperties,\n      basename\n    );\n\n    // Deferred isn't supported for fetcher loads, await everything and treat it\n    // as a normal load.  resolveDeferredData will return undefined if this\n    // fetcher gets aborted, so we just leave result untouched and short circuit\n    // below if that happens\n    if (isDeferredResult(result)) {\n      result =\n        (await resolveDeferredData(result, fetchRequest.signal, true)) ||\n        result;\n    }\n\n    // We can delete this so long as we weren't aborted by our our own fetcher\n    // re-load which would have put _new_ controller is in fetchControllers\n    if (fetchControllers.get(key) === abortController) {\n      fetchControllers.delete(key);\n    }\n\n    if (fetchRequest.signal.aborted) {\n      return;\n    }\n\n    // If the loader threw a redirect Response, start a new REPLACE navigation\n    if (isRedirectResult(result)) {\n      if (pendingNavigationLoadId > originatingLoadId) {\n        // A new navigation was kicked off after our loader started, so that\n        // should take precedence over this redirect navigation\n        let doneFetcher = getDoneFetcher(undefined);\n        state.fetchers.set(key, doneFetcher);\n        updateState({ fetchers: new Map(state.fetchers) });\n        return;\n      } else {\n        fetchRedirectIds.add(key);\n        await startRedirectNavigation(state, result);\n        return;\n      }\n    }\n\n    // Process any non-redirect errors thrown\n    if (isErrorResult(result)) {\n      let boundaryMatch = findNearestBoundary(state.matches, routeId);\n      state.fetchers.delete(key);\n      // TODO: In remix, this would reset to IDLE_NAVIGATION if it was a catch -\n      // do we need to behave any differently with our non-redirect errors?\n      // What if it was a non-redirect Response?\n      updateState({\n        fetchers: new Map(state.fetchers),\n        errors: {\n          [boundaryMatch.route.id]: result.error,\n        },\n      });\n      return;\n    }\n\n    invariant(!isDeferredResult(result), \"Unhandled fetcher deferred data\");\n\n    // Put the fetcher back into an idle state\n    let doneFetcher = getDoneFetcher(result.data);\n    state.fetchers.set(key, doneFetcher);\n    updateState({ fetchers: new Map(state.fetchers) });\n  }\n\n  /**\n   * Utility function to handle redirects returned from an action or loader.\n   * Normally, a redirect \"replaces\" the navigation that triggered it.  So, for\n   * example:\n   *\n   *  - user is on /a\n   *  - user clicks a link to /b\n   *  - loader for /b redirects to /c\n   *\n   * In a non-JS app the browser would track the in-flight navigation to /b and\n   * then replace it with /c when it encountered the redirect response.  In\n   * the end it would only ever update the URL bar with /c.\n   *\n   * In client-side routing using pushState/replaceState, we aim to emulate\n   * this behavior and we also do not update history until the end of the\n   * navigation (including processed redirects).  This means that we never\n   * actually touch history until we've processed redirects, so we just use\n   * the history action from the original navigation (PUSH or REPLACE).\n   */\n  async function startRedirectNavigation(\n    state: RouterState,\n    redirect: RedirectResult,\n    {\n      submission,\n      replace,\n      isFetchActionRedirect,\n    }: {\n      submission?: Submission;\n      replace?: boolean;\n      isFetchActionRedirect?: boolean;\n    } = {}\n  ) {\n    if (redirect.revalidate) {\n      isRevalidationRequired = true;\n    }\n\n    let redirectLocation = createLocation(\n      state.location,\n      redirect.location,\n      // TODO: This can be removed once we get rid of useTransition in Remix v2\n      {\n        _isRedirect: true,\n        ...(isFetchActionRedirect ? { _isFetchActionRedirect: true } : {}),\n      }\n    );\n    invariant(\n      redirectLocation,\n      \"Expected a location on the redirect navigation\"\n    );\n    // Check if this an absolute external redirect that goes to a new origin\n    if (ABSOLUTE_URL_REGEX.test(redirect.location) && isBrowser) {\n      let url = init.history.createURL(redirect.location);\n      let isDifferentBasename = stripBasename(url.pathname, basename) == null;\n\n      if (routerWindow.location.origin !== url.origin || isDifferentBasename) {\n        if (replace) {\n          routerWindow.location.replace(redirect.location);\n        } else {\n          routerWindow.location.assign(redirect.location);\n        }\n        return;\n      }\n    }\n\n    // There's no need to abort on redirects, since we don't detect the\n    // redirect until the action/loaders have settled\n    pendingNavigationController = null;\n\n    let redirectHistoryAction =\n      replace === true ? HistoryAction.Replace : HistoryAction.Push;\n\n    // Use the incoming submission if provided, fallback on the active one in\n    // state.navigation\n    let activeSubmission =\n      submission || getSubmissionFromNavigation(state.navigation);\n\n    // If this was a 307/308 submission we want to preserve the HTTP method and\n    // re-submit the GET/POST/PUT/PATCH/DELETE as a submission navigation to the\n    // redirected location\n    if (\n      redirectPreserveMethodStatusCodes.has(redirect.status) &&\n      activeSubmission &&\n      isMutationMethod(activeSubmission.formMethod)\n    ) {\n      await startNavigation(redirectHistoryAction, redirectLocation, {\n        submission: {\n          ...activeSubmission,\n          formAction: redirect.location,\n        },\n        // Preserve this flag across redirects\n        preventScrollReset: pendingPreventScrollReset,\n      });\n    } else if (isFetchActionRedirect) {\n      // For a fetch action redirect, we kick off a new loading navigation\n      // without the fetcher submission, but we send it along for shouldRevalidate\n      await startNavigation(redirectHistoryAction, redirectLocation, {\n        overrideNavigation: getLoadingNavigation(redirectLocation),\n        fetcherSubmission: activeSubmission,\n        // Preserve this flag across redirects\n        preventScrollReset: pendingPreventScrollReset,\n      });\n    } else {\n      // If we have a submission, we will preserve it through the redirect navigation\n      let overrideNavigation = getLoadingNavigation(\n        redirectLocation,\n        activeSubmission\n      );\n      await startNavigation(redirectHistoryAction, redirectLocation, {\n        overrideNavigation,\n        // Preserve this flag across redirects\n        preventScrollReset: pendingPreventScrollReset,\n      });\n    }\n  }\n\n  async function callLoadersAndMaybeResolveData(\n    currentMatches: AgnosticDataRouteMatch[],\n    matches: AgnosticDataRouteMatch[],\n    matchesToLoad: AgnosticDataRouteMatch[],\n    fetchersToLoad: RevalidatingFetcher[],\n    request: Request\n  ) {\n    // Call all navigation loaders and revalidating fetcher loaders in parallel,\n    // then slice off the results into separate arrays so we can handle them\n    // accordingly\n    let results = await Promise.all([\n      ...matchesToLoad.map((match) =>\n        callLoaderOrAction(\n          \"loader\",\n          request,\n          match,\n          matches,\n          manifest,\n          mapRouteProperties,\n          basename\n        )\n      ),\n      ...fetchersToLoad.map((f) => {\n        if (f.matches && f.match && f.controller) {\n          return callLoaderOrAction(\n            \"loader\",\n            createClientSideRequest(init.history, f.path, f.controller.signal),\n            f.match,\n            f.matches,\n            manifest,\n            mapRouteProperties,\n            basename\n          );\n        } else {\n          let error: ErrorResult = {\n            type: ResultType.error,\n            error: getInternalRouterError(404, { pathname: f.path }),\n          };\n          return error;\n        }\n      }),\n    ]);\n    let loaderResults = results.slice(0, matchesToLoad.length);\n    let fetcherResults = results.slice(matchesToLoad.length);\n\n    await Promise.all([\n      resolveDeferredResults(\n        currentMatches,\n        matchesToLoad,\n        loaderResults,\n        loaderResults.map(() => request.signal),\n        false,\n        state.loaderData\n      ),\n      resolveDeferredResults(\n        currentMatches,\n        fetchersToLoad.map((f) => f.match),\n        fetcherResults,\n        fetchersToLoad.map((f) => (f.controller ? f.controller.signal : null)),\n        true\n      ),\n    ]);\n\n    return { results, loaderResults, fetcherResults };\n  }\n\n  function interruptActiveLoads() {\n    // Every interruption triggers a revalidation\n    isRevalidationRequired = true;\n\n    // Cancel pending route-level deferreds and mark cancelled routes for\n    // revalidation\n    cancelledDeferredRoutes.push(...cancelActiveDeferreds());\n\n    // Abort in-flight fetcher loads\n    fetchLoadMatches.forEach((_, key) => {\n      if (fetchControllers.has(key)) {\n        cancelledFetcherLoads.push(key);\n        abortFetcher(key);\n      }\n    });\n  }\n\n  function setFetcherError(key: string, routeId: string, error: any) {\n    let boundaryMatch = findNearestBoundary(state.matches, routeId);\n    deleteFetcher(key);\n    updateState({\n      errors: {\n        [boundaryMatch.route.id]: error,\n      },\n      fetchers: new Map(state.fetchers),\n    });\n  }\n\n  function deleteFetcher(key: string): void {\n    let fetcher = state.fetchers.get(key);\n    // Don't abort the controller if this is a deletion of a fetcher.submit()\n    // in it's loading phase since - we don't want to abort the corresponding\n    // revalidation and want them to complete and land\n    if (\n      fetchControllers.has(key) &&\n      !(fetcher && fetcher.state === \"loading\" && fetchReloadIds.has(key))\n    ) {\n      abortFetcher(key);\n    }\n    fetchLoadMatches.delete(key);\n    fetchReloadIds.delete(key);\n    fetchRedirectIds.delete(key);\n    state.fetchers.delete(key);\n  }\n\n  function abortFetcher(key: string) {\n    let controller = fetchControllers.get(key);\n    invariant(controller, `Expected fetch controller: ${key}`);\n    controller.abort();\n    fetchControllers.delete(key);\n  }\n\n  function markFetchersDone(keys: string[]) {\n    for (let key of keys) {\n      let fetcher = getFetcher(key);\n      let doneFetcher = getDoneFetcher(fetcher.data);\n      state.fetchers.set(key, doneFetcher);\n    }\n  }\n\n  function markFetchRedirectsDone(): boolean {\n    let doneKeys = [];\n    let updatedFetchers = false;\n    for (let key of fetchRedirectIds) {\n      let fetcher = state.fetchers.get(key);\n      invariant(fetcher, `Expected fetcher: ${key}`);\n      if (fetcher.state === \"loading\") {\n        fetchRedirectIds.delete(key);\n        doneKeys.push(key);\n        updatedFetchers = true;\n      }\n    }\n    markFetchersDone(doneKeys);\n    return updatedFetchers;\n  }\n\n  function abortStaleFetchLoads(landedId: number): boolean {\n    let yeetedKeys = [];\n    for (let [key, id] of fetchReloadIds) {\n      if (id < landedId) {\n        let fetcher = state.fetchers.get(key);\n        invariant(fetcher, `Expected fetcher: ${key}`);\n        if (fetcher.state === \"loading\") {\n          abortFetcher(key);\n          fetchReloadIds.delete(key);\n          yeetedKeys.push(key);\n        }\n      }\n    }\n    markFetchersDone(yeetedKeys);\n    return yeetedKeys.length > 0;\n  }\n\n  function getBlocker(key: string, fn: BlockerFunction) {\n    let blocker: Blocker = state.blockers.get(key) || IDLE_BLOCKER;\n\n    if (blockerFunctions.get(key) !== fn) {\n      blockerFunctions.set(key, fn);\n    }\n\n    return blocker;\n  }\n\n  function deleteBlocker(key: string) {\n    state.blockers.delete(key);\n    blockerFunctions.delete(key);\n  }\n\n  // Utility function to update blockers, ensuring valid state transitions\n  function updateBlocker(key: string, newBlocker: Blocker) {\n    let blocker = state.blockers.get(key) || IDLE_BLOCKER;\n\n    // Poor mans state machine :)\n    // https://mermaid.live/edit#pako:eNqVkc9OwzAMxl8l8nnjAYrEtDIOHEBIgwvKJTReGy3_lDpIqO27k6awMG0XcrLlnz87nwdonESogKXXBuE79rq75XZO3-yHds0RJVuv70YrPlUrCEe2HfrORS3rubqZfuhtpg5C9wk5tZ4VKcRUq88q9Z8RS0-48cE1iHJkL0ugbHuFLus9L6spZy8nX9MP2CNdomVaposqu3fGayT8T8-jJQwhepo_UtpgBQaDEUom04dZhAN1aJBDlUKJBxE1ceB2Smj0Mln-IBW5AFU2dwUiktt_2Qaq2dBfaKdEup85UV7Yd-dKjlnkabl2Pvr0DTkTreM\n    invariant(\n      (blocker.state === \"unblocked\" && newBlocker.state === \"blocked\") ||\n        (blocker.state === \"blocked\" && newBlocker.state === \"blocked\") ||\n        (blocker.state === \"blocked\" && newBlocker.state === \"proceeding\") ||\n        (blocker.state === \"blocked\" && newBlocker.state === \"unblocked\") ||\n        (blocker.state === \"proceeding\" && newBlocker.state === \"unblocked\"),\n      `Invalid blocker state transition: ${blocker.state} -> ${newBlocker.state}`\n    );\n\n    let blockers = new Map(state.blockers);\n    blockers.set(key, newBlocker);\n    updateState({ blockers });\n  }\n\n  function shouldBlockNavigation({\n    currentLocation,\n    nextLocation,\n    historyAction,\n  }: {\n    currentLocation: Location;\n    nextLocation: Location;\n    historyAction: HistoryAction;\n  }): string | undefined {\n    if (blockerFunctions.size === 0) {\n      return;\n    }\n\n    // We ony support a single active blocker at the moment since we don't have\n    // any compelling use cases for multi-blocker yet\n    if (blockerFunctions.size > 1) {\n      warning(false, \"A router only supports one blocker at a time\");\n    }\n\n    let entries = Array.from(blockerFunctions.entries());\n    let [blockerKey, blockerFunction] = entries[entries.length - 1];\n    let blocker = state.blockers.get(blockerKey);\n\n    if (blocker && blocker.state === \"proceeding\") {\n      // If the blocker is currently proceeding, we don't need to re-check\n      // it and can let this navigation continue\n      return;\n    }\n\n    // At this point, we know we're unblocked/blocked so we need to check the\n    // user-provided blocker function\n    if (blockerFunction({ currentLocation, nextLocation, historyAction })) {\n      return blockerKey;\n    }\n  }\n\n  function cancelActiveDeferreds(\n    predicate?: (routeId: string) => boolean\n  ): string[] {\n    let cancelledRouteIds: string[] = [];\n    activeDeferreds.forEach((dfd, routeId) => {\n      if (!predicate || predicate(routeId)) {\n        // Cancel the deferred - but do not remove from activeDeferreds here -\n        // we rely on the subscribers to do that so our tests can assert proper\n        // cleanup via _internalActiveDeferreds\n        dfd.cancel();\n        cancelledRouteIds.push(routeId);\n        activeDeferreds.delete(routeId);\n      }\n    });\n    return cancelledRouteIds;\n  }\n\n  // Opt in to capturing and reporting scroll positions during navigations,\n  // used by the <ScrollRestoration> component\n  function enableScrollRestoration(\n    positions: Record<string, number>,\n    getPosition: GetScrollPositionFunction,\n    getKey?: GetScrollRestorationKeyFunction\n  ) {\n    savedScrollPositions = positions;\n    getScrollPosition = getPosition;\n    getScrollRestorationKey = getKey || null;\n\n    // Perform initial hydration scroll restoration, since we miss the boat on\n    // the initial updateState() because we've not yet rendered <ScrollRestoration/>\n    // and therefore have no savedScrollPositions available\n    if (!initialScrollRestored && state.navigation === IDLE_NAVIGATION) {\n      initialScrollRestored = true;\n      let y = getSavedScrollPosition(state.location, state.matches);\n      if (y != null) {\n        updateState({ restoreScrollPosition: y });\n      }\n    }\n\n    return () => {\n      savedScrollPositions = null;\n      getScrollPosition = null;\n      getScrollRestorationKey = null;\n    };\n  }\n\n  function getScrollKey(location: Location, matches: AgnosticDataRouteMatch[]) {\n    if (getScrollRestorationKey) {\n      let key = getScrollRestorationKey(\n        location,\n        matches.map((m) => createUseMatchesMatch(m, state.loaderData))\n      );\n      return key || location.key;\n    }\n    return location.key;\n  }\n\n  function saveScrollPosition(\n    location: Location,\n    matches: AgnosticDataRouteMatch[]\n  ): void {\n    if (savedScrollPositions && getScrollPosition) {\n      let key = getScrollKey(location, matches);\n      savedScrollPositions[key] = getScrollPosition();\n    }\n  }\n\n  function getSavedScrollPosition(\n    location: Location,\n    matches: AgnosticDataRouteMatch[]\n  ): number | null {\n    if (savedScrollPositions) {\n      let key = getScrollKey(location, matches);\n      let y = savedScrollPositions[key];\n      if (typeof y === \"number\") {\n        return y;\n      }\n    }\n    return null;\n  }\n\n  function _internalSetRoutes(newRoutes: AgnosticDataRouteObject[]) {\n    manifest = {};\n    inFlightDataRoutes = convertRoutesToDataRoutes(\n      newRoutes,\n      mapRouteProperties,\n      undefined,\n      manifest\n    );\n  }\n\n  router = {\n    get basename() {\n      return basename;\n    },\n    get state() {\n      return state;\n    },\n    get routes() {\n      return dataRoutes;\n    },\n    initialize,\n    subscribe,\n    enableScrollRestoration,\n    navigate,\n    fetch,\n    revalidate,\n    // Passthrough to history-aware createHref used by useHref so we get proper\n    // hash-aware URLs in DOM paths\n    createHref: (to: To) => init.history.createHref(to),\n    encodeLocation: (to: To) => init.history.encodeLocation(to),\n    getFetcher,\n    deleteFetcher,\n    dispose,\n    getBlocker,\n    deleteBlocker,\n    _internalFetchControllers: fetchControllers,\n    _internalActiveDeferreds: activeDeferreds,\n    // TODO: Remove setRoutes, it's temporary to avoid dealing with\n    // updating the tree while validating the update algorithm.\n    _internalSetRoutes,\n  };\n\n  return router;\n}\n//#endregion\n\n////////////////////////////////////////////////////////////////////////////////\n//#region createStaticHandler\n////////////////////////////////////////////////////////////////////////////////\n\nexport const UNSAFE_DEFERRED_SYMBOL = Symbol(\"deferred\");\n\nexport interface CreateStaticHandlerOptions {\n  basename?: string;\n  /**\n   * @deprecated Use `mapRouteProperties` instead\n   */\n  detectErrorBoundary?: DetectErrorBoundaryFunction;\n  mapRouteProperties?: MapRoutePropertiesFunction;\n}\n\nexport function createStaticHandler(\n  routes: AgnosticRouteObject[],\n  opts?: CreateStaticHandlerOptions\n): StaticHandler {\n  invariant(\n    routes.length > 0,\n    \"You must provide a non-empty routes array to createStaticHandler\"\n  );\n\n  let manifest: RouteManifest = {};\n  let basename = (opts ? opts.basename : null) || \"/\";\n  let mapRouteProperties: MapRoutePropertiesFunction;\n  if (opts?.mapRouteProperties) {\n    mapRouteProperties = opts.mapRouteProperties;\n  } else if (opts?.detectErrorBoundary) {\n    // If they are still using the deprecated version, wrap it with the new API\n    let detectErrorBoundary = opts.detectErrorBoundary;\n    mapRouteProperties = (route) => ({\n      hasErrorBoundary: detectErrorBoundary(route),\n    });\n  } else {\n    mapRouteProperties = defaultMapRouteProperties;\n  }\n\n  let dataRoutes = convertRoutesToDataRoutes(\n    routes,\n    mapRouteProperties,\n    undefined,\n    manifest\n  );\n\n  /**\n   * The query() method is intended for document requests, in which we want to\n   * call an optional action and potentially multiple loaders for all nested\n   * routes.  It returns a StaticHandlerContext object, which is very similar\n   * to the router state (location, loaderData, actionData, errors, etc.) and\n   * also adds SSR-specific information such as the statusCode and headers\n   * from action/loaders Responses.\n   *\n   * It _should_ never throw and should report all errors through the\n   * returned context.errors object, properly associating errors to their error\n   * boundary.  Additionally, it tracks _deepestRenderedBoundaryId which can be\n   * used to emulate React error boundaries during SSr by performing a second\n   * pass only down to the boundaryId.\n   *\n   * The one exception where we do not return a StaticHandlerContext is when a\n   * redirect response is returned or thrown from any action/loader.  We\n   * propagate that out and return the raw Response so the HTTP server can\n   * return it directly.\n   */\n  async function query(\n    request: Request,\n    { requestContext }: { requestContext?: unknown } = {}\n  ): Promise<StaticHandlerContext | Response> {\n    let url = new URL(request.url);\n    let method = request.method;\n    let location = createLocation(\"\", createPath(url), null, \"default\");\n    let matches = matchRoutes(dataRoutes, location, basename);\n\n    // SSR supports HEAD requests while SPA doesn't\n    if (!isValidMethod(method) && method !== \"HEAD\") {\n      let error = getInternalRouterError(405, { method });\n      let { matches: methodNotAllowedMatches, route } =\n        getShortCircuitMatches(dataRoutes);\n      return {\n        basename,\n        location,\n        matches: methodNotAllowedMatches,\n        loaderData: {},\n        actionData: null,\n        errors: {\n          [route.id]: error,\n        },\n        statusCode: error.status,\n        loaderHeaders: {},\n        actionHeaders: {},\n        activeDeferreds: null,\n      };\n    } else if (!matches) {\n      let error = getInternalRouterError(404, { pathname: location.pathname });\n      let { matches: notFoundMatches, route } =\n        getShortCircuitMatches(dataRoutes);\n      return {\n        basename,\n        location,\n        matches: notFoundMatches,\n        loaderData: {},\n        actionData: null,\n        errors: {\n          [route.id]: error,\n        },\n        statusCode: error.status,\n        loaderHeaders: {},\n        actionHeaders: {},\n        activeDeferreds: null,\n      };\n    }\n\n    let result = await queryImpl(request, location, matches, requestContext);\n    if (isResponse(result)) {\n      return result;\n    }\n\n    // When returning StaticHandlerContext, we patch back in the location here\n    // since we need it for React Context.  But this helps keep our submit and\n    // loadRouteData operating on a Request instead of a Location\n    return { location, basename, ...result };\n  }\n\n  /**\n   * The queryRoute() method is intended for targeted route requests, either\n   * for fetch ?_data requests or resource route requests.  In this case, we\n   * are only ever calling a single action or loader, and we are returning the\n   * returned value directly.  In most cases, this will be a Response returned\n   * from the action/loader, but it may be a primitive or other value as well -\n   * and in such cases the calling context should handle that accordingly.\n   *\n   * We do respect the throw/return differentiation, so if an action/loader\n   * throws, then this method will throw the value.  This is important so we\n   * can do proper boundary identification in Remix where a thrown Response\n   * must go to the Catch Boundary but a returned Response is happy-path.\n   *\n   * One thing to note is that any Router-initiated Errors that make sense\n   * to associate with a status code will be thrown as an ErrorResponse\n   * instance which include the raw Error, such that the calling context can\n   * serialize the error as they see fit while including the proper response\n   * code.  Examples here are 404 and 405 errors that occur prior to reaching\n   * any user-defined loaders.\n   */\n  async function queryRoute(\n    request: Request,\n    {\n      routeId,\n      requestContext,\n    }: { requestContext?: unknown; routeId?: string } = {}\n  ): Promise<any> {\n    let url = new URL(request.url);\n    let method = request.method;\n    let location = createLocation(\"\", createPath(url), null, \"default\");\n    let matches = matchRoutes(dataRoutes, location, basename);\n\n    // SSR supports HEAD requests while SPA doesn't\n    if (!isValidMethod(method) && method !== \"HEAD\" && method !== \"OPTIONS\") {\n      throw getInternalRouterError(405, { method });\n    } else if (!matches) {\n      throw getInternalRouterError(404, { pathname: location.pathname });\n    }\n\n    let match = routeId\n      ? matches.find((m) => m.route.id === routeId)\n      : getTargetMatch(matches, location);\n\n    if (routeId && !match) {\n      throw getInternalRouterError(403, {\n        pathname: location.pathname,\n        routeId,\n      });\n    } else if (!match) {\n      // This should never hit I don't think?\n      throw getInternalRouterError(404, { pathname: location.pathname });\n    }\n\n    let result = await queryImpl(\n      request,\n      location,\n      matches,\n      requestContext,\n      match\n    );\n    if (isResponse(result)) {\n      return result;\n    }\n\n    let error = result.errors ? Object.values(result.errors)[0] : undefined;\n    if (error !== undefined) {\n      // If we got back result.errors, that means the loader/action threw\n      // _something_ that wasn't a Response, but it's not guaranteed/required\n      // to be an `instanceof Error` either, so we have to use throw here to\n      // preserve the \"error\" state outside of queryImpl.\n      throw error;\n    }\n\n    // Pick off the right state value to return\n    if (result.actionData) {\n      return Object.values(result.actionData)[0];\n    }\n\n    if (result.loaderData) {\n      let data = Object.values(result.loaderData)[0];\n      if (result.activeDeferreds?.[match.route.id]) {\n        data[UNSAFE_DEFERRED_SYMBOL] = result.activeDeferreds[match.route.id];\n      }\n      return data;\n    }\n\n    return undefined;\n  }\n\n  async function queryImpl(\n    request: Request,\n    location: Location,\n    matches: AgnosticDataRouteMatch[],\n    requestContext: unknown,\n    routeMatch?: AgnosticDataRouteMatch\n  ): Promise<Omit<StaticHandlerContext, \"location\" | \"basename\"> | Response> {\n    invariant(\n      request.signal,\n      \"query()/queryRoute() requests must contain an AbortController signal\"\n    );\n\n    try {\n      if (isMutationMethod(request.method.toLowerCase())) {\n        let result = await submit(\n          request,\n          matches,\n          routeMatch || getTargetMatch(matches, location),\n          requestContext,\n          routeMatch != null\n        );\n        return result;\n      }\n\n      let result = await loadRouteData(\n        request,\n        matches,\n        requestContext,\n        routeMatch\n      );\n      return isResponse(result)\n        ? result\n        : {\n            ...result,\n            actionData: null,\n            actionHeaders: {},\n          };\n    } catch (e) {\n      // If the user threw/returned a Response in callLoaderOrAction, we throw\n      // it to bail out and then return or throw here based on whether the user\n      // returned or threw\n      if (isQueryRouteResponse(e)) {\n        if (e.type === ResultType.error && !isRedirectResponse(e.response)) {\n          throw e.response;\n        }\n        return e.response;\n      }\n      // Redirects are always returned since they don't propagate to catch\n      // boundaries\n      if (isRedirectResponse(e)) {\n        return e;\n      }\n      throw e;\n    }\n  }\n\n  async function submit(\n    request: Request,\n    matches: AgnosticDataRouteMatch[],\n    actionMatch: AgnosticDataRouteMatch,\n    requestContext: unknown,\n    isRouteRequest: boolean\n  ): Promise<Omit<StaticHandlerContext, \"location\" | \"basename\"> | Response> {\n    let result: DataResult;\n\n    if (!actionMatch.route.action && !actionMatch.route.lazy) {\n      let error = getInternalRouterError(405, {\n        method: request.method,\n        pathname: new URL(request.url).pathname,\n        routeId: actionMatch.route.id,\n      });\n      if (isRouteRequest) {\n        throw error;\n      }\n      result = {\n        type: ResultType.error,\n        error,\n      };\n    } else {\n      result = await callLoaderOrAction(\n        \"action\",\n        request,\n        actionMatch,\n        matches,\n        manifest,\n        mapRouteProperties,\n        basename,\n        { isStaticRequest: true, isRouteRequest, requestContext }\n      );\n\n      if (request.signal.aborted) {\n        let method = isRouteRequest ? \"queryRoute\" : \"query\";\n        throw new Error(`${method}() call aborted`);\n      }\n    }\n\n    if (isRedirectResult(result)) {\n      // Uhhhh - this should never happen, we should always throw these from\n      // callLoaderOrAction, but the type narrowing here keeps TS happy and we\n      // can get back on the \"throw all redirect responses\" train here should\n      // this ever happen :/\n      throw new Response(null, {\n        status: result.status,\n        headers: {\n          Location: result.location,\n        },\n      });\n    }\n\n    if (isDeferredResult(result)) {\n      let error = getInternalRouterError(400, { type: \"defer-action\" });\n      if (isRouteRequest) {\n        throw error;\n      }\n      result = {\n        type: ResultType.error,\n        error,\n      };\n    }\n\n    if (isRouteRequest) {\n      // Note: This should only be non-Response values if we get here, since\n      // isRouteRequest should throw any Response received in callLoaderOrAction\n      if (isErrorResult(result)) {\n        throw result.error;\n      }\n\n      return {\n        matches: [actionMatch],\n        loaderData: {},\n        actionData: { [actionMatch.route.id]: result.data },\n        errors: null,\n        // Note: statusCode + headers are unused here since queryRoute will\n        // return the raw Response or value\n        statusCode: 200,\n        loaderHeaders: {},\n        actionHeaders: {},\n        activeDeferreds: null,\n      };\n    }\n\n    if (isErrorResult(result)) {\n      // Store off the pending error - we use it to determine which loaders\n      // to call and will commit it when we complete the navigation\n      let boundaryMatch = findNearestBoundary(matches, actionMatch.route.id);\n      let context = await loadRouteData(\n        request,\n        matches,\n        requestContext,\n        undefined,\n        {\n          [boundaryMatch.route.id]: result.error,\n        }\n      );\n\n      // action status codes take precedence over loader status codes\n      return {\n        ...context,\n        statusCode: isRouteErrorResponse(result.error)\n          ? result.error.status\n          : 500,\n        actionData: null,\n        actionHeaders: {\n          ...(result.headers ? { [actionMatch.route.id]: result.headers } : {}),\n        },\n      };\n    }\n\n    // Create a GET request for the loaders\n    let loaderRequest = new Request(request.url, {\n      headers: request.headers,\n      redirect: request.redirect,\n      signal: request.signal,\n    });\n    let context = await loadRouteData(loaderRequest, matches, requestContext);\n\n    return {\n      ...context,\n      // action status codes take precedence over loader status codes\n      ...(result.statusCode ? { statusCode: result.statusCode } : {}),\n      actionData: {\n        [actionMatch.route.id]: result.data,\n      },\n      actionHeaders: {\n        ...(result.headers ? { [actionMatch.route.id]: result.headers } : {}),\n      },\n    };\n  }\n\n  async function loadRouteData(\n    request: Request,\n    matches: AgnosticDataRouteMatch[],\n    requestContext: unknown,\n    routeMatch?: AgnosticDataRouteMatch,\n    pendingActionError?: RouteData\n  ): Promise<\n    | Omit<\n        StaticHandlerContext,\n        \"location\" | \"basename\" | \"actionData\" | \"actionHeaders\"\n      >\n    | Response\n  > {\n    let isRouteRequest = routeMatch != null;\n\n    // Short circuit if we have no loaders to run (queryRoute())\n    if (\n      isRouteRequest &&\n      !routeMatch?.route.loader &&\n      !routeMatch?.route.lazy\n    ) {\n      throw getInternalRouterError(400, {\n        method: request.method,\n        pathname: new URL(request.url).pathname,\n        routeId: routeMatch?.route.id,\n      });\n    }\n\n    let requestMatches = routeMatch\n      ? [routeMatch]\n      : getLoaderMatchesUntilBoundary(\n          matches,\n          Object.keys(pendingActionError || {})[0]\n        );\n    let matchesToLoad = requestMatches.filter(\n      (m) => m.route.loader || m.route.lazy\n    );\n\n    // Short circuit if we have no loaders to run (query())\n    if (matchesToLoad.length === 0) {\n      return {\n        matches,\n        // Add a null for all matched routes for proper revalidation on the client\n        loaderData: matches.reduce(\n          (acc, m) => Object.assign(acc, { [m.route.id]: null }),\n          {}\n        ),\n        errors: pendingActionError || null,\n        statusCode: 200,\n        loaderHeaders: {},\n        activeDeferreds: null,\n      };\n    }\n\n    let results = await Promise.all([\n      ...matchesToLoad.map((match) =>\n        callLoaderOrAction(\n          \"loader\",\n          request,\n          match,\n          matches,\n          manifest,\n          mapRouteProperties,\n          basename,\n          { isStaticRequest: true, isRouteRequest, requestContext }\n        )\n      ),\n    ]);\n\n    if (request.signal.aborted) {\n      let method = isRouteRequest ? \"queryRoute\" : \"query\";\n      throw new Error(`${method}() call aborted`);\n    }\n\n    // Process and commit output from loaders\n    let activeDeferreds = new Map<string, DeferredData>();\n    let context = processRouteLoaderData(\n      matches,\n      matchesToLoad,\n      results,\n      pendingActionError,\n      activeDeferreds\n    );\n\n    // Add a null for any non-loader matches for proper revalidation on the client\n    let executedLoaders = new Set<string>(\n      matchesToLoad.map((match) => match.route.id)\n    );\n    matches.forEach((match) => {\n      if (!executedLoaders.has(match.route.id)) {\n        context.loaderData[match.route.id] = null;\n      }\n    });\n\n    return {\n      ...context,\n      matches,\n      activeDeferreds:\n        activeDeferreds.size > 0\n          ? Object.fromEntries(activeDeferreds.entries())\n          : null,\n    };\n  }\n\n  return {\n    dataRoutes,\n    query,\n    queryRoute,\n  };\n}\n\n//#endregion\n\n////////////////////////////////////////////////////////////////////////////////\n//#region Helpers\n////////////////////////////////////////////////////////////////////////////////\n\n/**\n * Given an existing StaticHandlerContext and an error thrown at render time,\n * provide an updated StaticHandlerContext suitable for a second SSR render\n */\nexport function getStaticContextFromError(\n  routes: AgnosticDataRouteObject[],\n  context: StaticHandlerContext,\n  error: any\n) {\n  let newContext: StaticHandlerContext = {\n    ...context,\n    statusCode: 500,\n    errors: {\n      [context._deepestRenderedBoundaryId || routes[0].id]: error,\n    },\n  };\n  return newContext;\n}\n\nfunction isSubmissionNavigation(\n  opts: RouterNavigateOptions\n): opts is SubmissionNavigateOptions {\n  return (\n    opts != null &&\n    ((\"formData\" in opts && opts.formData != null) ||\n      (\"body\" in opts && opts.body !== undefined))\n  );\n}\n\nfunction normalizeTo(\n  location: Path,\n  matches: AgnosticDataRouteMatch[],\n  basename: string,\n  prependBasename: boolean,\n  to: To | null,\n  fromRouteId?: string,\n  relative?: RelativeRoutingType\n) {\n  let contextualMatches: AgnosticDataRouteMatch[];\n  let activeRouteMatch: AgnosticDataRouteMatch | undefined;\n  if (fromRouteId != null && relative !== \"path\") {\n    // Grab matches up to the calling route so our route-relative logic is\n    // relative to the correct source route.  When using relative:path,\n    // fromRouteId is ignored since that is always relative to the current\n    // location path\n    contextualMatches = [];\n    for (let match of matches) {\n      contextualMatches.push(match);\n      if (match.route.id === fromRouteId) {\n        activeRouteMatch = match;\n        break;\n      }\n    }\n  } else {\n    contextualMatches = matches;\n    activeRouteMatch = matches[matches.length - 1];\n  }\n\n  // Resolve the relative path\n  let path = resolveTo(\n    to ? to : \".\",\n    getPathContributingMatches(contextualMatches).map((m) => m.pathnameBase),\n    stripBasename(location.pathname, basename) || location.pathname,\n    relative === \"path\"\n  );\n\n  // When `to` is not specified we inherit search/hash from the current\n  // location, unlike when to=\".\" and we just inherit the path.\n  // See https://github.com/remix-run/remix/issues/927\n  if (to == null) {\n    path.search = location.search;\n    path.hash = location.hash;\n  }\n\n  // Add an ?index param for matched index routes if we don't already have one\n  if (\n    (to == null || to === \"\" || to === \".\") &&\n    activeRouteMatch &&\n    activeRouteMatch.route.index &&\n    !hasNakedIndexQuery(path.search)\n  ) {\n    path.search = path.search\n      ? path.search.replace(/^\\?/, \"?index&\")\n      : \"?index\";\n  }\n\n  // If we're operating within a basename, prepend it to the pathname.  If\n  // this is a root navigation, then just use the raw basename which allows\n  // the basename to have full control over the presence of a trailing slash\n  // on root actions\n  if (prependBasename && basename !== \"/\") {\n    path.pathname =\n      path.pathname === \"/\" ? basename : joinPaths([basename, path.pathname]);\n  }\n\n  return createPath(path);\n}\n\n// Normalize navigation options by converting formMethod=GET formData objects to\n// URLSearchParams so they behave identically to links with query params\nfunction normalizeNavigateOptions(\n  normalizeFormMethod: boolean,\n  isFetcher: boolean,\n  path: string,\n  opts?: RouterNavigateOptions\n): {\n  path: string;\n  submission?: Submission;\n  error?: ErrorResponse;\n} {\n  // Return location verbatim on non-submission navigations\n  if (!opts || !isSubmissionNavigation(opts)) {\n    return { path };\n  }\n\n  if (opts.formMethod && !isValidMethod(opts.formMethod)) {\n    return {\n      path,\n      error: getInternalRouterError(405, { method: opts.formMethod }),\n    };\n  }\n\n  let getInvalidBodyError = () => ({\n    path,\n    error: getInternalRouterError(400, { type: \"invalid-body\" }),\n  });\n\n  // Create a Submission on non-GET navigations\n  let rawFormMethod = opts.formMethod || \"get\";\n  let formMethod = normalizeFormMethod\n    ? (rawFormMethod.toUpperCase() as V7_FormMethod)\n    : (rawFormMethod.toLowerCase() as FormMethod);\n  let formAction = stripHashFromPath(path);\n\n  if (opts.body !== undefined) {\n    if (opts.formEncType === \"text/plain\") {\n      // text only support POST/PUT/PATCH/DELETE submissions\n      if (!isMutationMethod(formMethod)) {\n        return getInvalidBodyError();\n      }\n\n      let text =\n        typeof opts.body === \"string\"\n          ? opts.body\n          : opts.body instanceof FormData ||\n            opts.body instanceof URLSearchParams\n          ? // https://html.spec.whatwg.org/multipage/form-control-infrastructure.html#plain-text-form-data\n            Array.from(opts.body.entries()).reduce(\n              (acc, [name, value]) => `${acc}${name}=${value}\\n`,\n              \"\"\n            )\n          : String(opts.body);\n\n      return {\n        path,\n        submission: {\n          formMethod,\n          formAction,\n          formEncType: opts.formEncType,\n          formData: undefined,\n          json: undefined,\n          text,\n        },\n      };\n    } else if (opts.formEncType === \"application/json\") {\n      // json only supports POST/PUT/PATCH/DELETE submissions\n      if (!isMutationMethod(formMethod)) {\n        return getInvalidBodyError();\n      }\n\n      try {\n        let json =\n          typeof opts.body === \"string\" ? JSON.parse(opts.body) : opts.body;\n\n        return {\n          path,\n          submission: {\n            formMethod,\n            formAction,\n            formEncType: opts.formEncType,\n            formData: undefined,\n            json,\n            text: undefined,\n          },\n        };\n      } catch (e) {\n        return getInvalidBodyError();\n      }\n    }\n  }\n\n  invariant(\n    typeof FormData === \"function\",\n    \"FormData is not available in this environment\"\n  );\n\n  let searchParams: URLSearchParams;\n  let formData: FormData;\n\n  if (opts.formData) {\n    searchParams = convertFormDataToSearchParams(opts.formData);\n    formData = opts.formData;\n  } else if (opts.body instanceof FormData) {\n    searchParams = convertFormDataToSearchParams(opts.body);\n    formData = opts.body;\n  } else if (opts.body instanceof URLSearchParams) {\n    searchParams = opts.body;\n    formData = convertSearchParamsToFormData(searchParams);\n  } else if (opts.body == null) {\n    searchParams = new URLSearchParams();\n    formData = new FormData();\n  } else {\n    try {\n      searchParams = new URLSearchParams(opts.body);\n      formData = convertSearchParamsToFormData(searchParams);\n    } catch (e) {\n      return getInvalidBodyError();\n    }\n  }\n\n  let submission: Submission = {\n    formMethod,\n    formAction,\n    formEncType:\n      (opts && opts.formEncType) || \"application/x-www-form-urlencoded\",\n    formData,\n    json: undefined,\n    text: undefined,\n  };\n\n  if (isMutationMethod(submission.formMethod)) {\n    return { path, submission };\n  }\n\n  // Flatten submission onto URLSearchParams for GET submissions\n  let parsedPath = parsePath(path);\n  // On GET navigation submissions we can drop the ?index param from the\n  // resulting location since all loaders will run.  But fetcher GET submissions\n  // only run a single loader so we need to preserve any incoming ?index params\n  if (isFetcher && parsedPath.search && hasNakedIndexQuery(parsedPath.search)) {\n    searchParams.append(\"index\", \"\");\n  }\n  parsedPath.search = `?${searchParams}`;\n\n  return { path: createPath(parsedPath), submission };\n}\n\n// Filter out all routes below any caught error as they aren't going to\n// render so we don't need to load them\nfunction getLoaderMatchesUntilBoundary(\n  matches: AgnosticDataRouteMatch[],\n  boundaryId?: string\n) {\n  let boundaryMatches = matches;\n  if (boundaryId) {\n    let index = matches.findIndex((m) => m.route.id === boundaryId);\n    if (index >= 0) {\n      boundaryMatches = matches.slice(0, index);\n    }\n  }\n  return boundaryMatches;\n}\n\nfunction getMatchesToLoad(\n  history: History,\n  state: RouterState,\n  matches: AgnosticDataRouteMatch[],\n  submission: Submission | undefined,\n  location: Location,\n  isRevalidationRequired: boolean,\n  cancelledDeferredRoutes: string[],\n  cancelledFetcherLoads: string[],\n  fetchLoadMatches: Map<string, FetchLoadMatch>,\n  fetchRedirectIds: Set<string>,\n  routesToUse: AgnosticDataRouteObject[],\n  basename: string | undefined,\n  pendingActionData?: RouteData,\n  pendingError?: RouteData\n): [AgnosticDataRouteMatch[], RevalidatingFetcher[]] {\n  let actionResult = pendingError\n    ? Object.values(pendingError)[0]\n    : pendingActionData\n    ? Object.values(pendingActionData)[0]\n    : undefined;\n\n  let currentUrl = history.createURL(state.location);\n  let nextUrl = history.createURL(location);\n\n  // Pick navigation matches that are net-new or qualify for revalidation\n  let boundaryId = pendingError ? Object.keys(pendingError)[0] : undefined;\n  let boundaryMatches = getLoaderMatchesUntilBoundary(matches, boundaryId);\n\n  let navigationMatches = boundaryMatches.filter((match, index) => {\n    if (match.route.lazy) {\n      // We haven't loaded this route yet so we don't know if it's got a loader!\n      return true;\n    }\n    if (match.route.loader == null) {\n      return false;\n    }\n\n    // Always call the loader on new route instances and pending defer cancellations\n    if (\n      isNewLoader(state.loaderData, state.matches[index], match) ||\n      cancelledDeferredRoutes.some((id) => id === match.route.id)\n    ) {\n      return true;\n    }\n\n    // This is the default implementation for when we revalidate.  If the route\n    // provides it's own implementation, then we give them full control but\n    // provide this value so they can leverage it if needed after they check\n    // their own specific use cases\n    let currentRouteMatch = state.matches[index];\n    let nextRouteMatch = match;\n\n    return shouldRevalidateLoader(match, {\n      currentUrl,\n      currentParams: currentRouteMatch.params,\n      nextUrl,\n      nextParams: nextRouteMatch.params,\n      ...submission,\n      actionResult,\n      defaultShouldRevalidate:\n        // Forced revalidation due to submission, useRevalidator, or X-Remix-Revalidate\n        isRevalidationRequired ||\n        // Clicked the same link, resubmitted a GET form\n        currentUrl.pathname + currentUrl.search ===\n          nextUrl.pathname + nextUrl.search ||\n        // Search params affect all loaders\n        currentUrl.search !== nextUrl.search ||\n        isNewRouteInstance(currentRouteMatch, nextRouteMatch),\n    });\n  });\n\n  // Pick fetcher.loads that need to be revalidated\n  let revalidatingFetchers: RevalidatingFetcher[] = [];\n  fetchLoadMatches.forEach((f, key) => {\n    // Don't revalidate if fetcher won't be present in the subsequent render\n    if (!matches.some((m) => m.route.id === f.routeId)) {\n      return;\n    }\n\n    let fetcherMatches = matchRoutes(routesToUse, f.path, basename);\n\n    // If the fetcher path no longer matches, push it in with null matches so\n    // we can trigger a 404 in callLoadersAndMaybeResolveData.  Note this is\n    // currently only a use-case for Remix HMR where the route tree can change\n    // at runtime and remove a route previously loaded via a fetcher\n    if (!fetcherMatches) {\n      revalidatingFetchers.push({\n        key,\n        routeId: f.routeId,\n        path: f.path,\n        matches: null,\n        match: null,\n        controller: null,\n      });\n      return;\n    }\n\n    // Revalidating fetchers are decoupled from the route matches since they\n    // load from a static href.  They revalidate based on explicit revalidation\n    // (submission, useRevalidator, or X-Remix-Revalidate)\n    let fetcher = state.fetchers.get(key);\n    let fetcherMatch = getTargetMatch(fetcherMatches, f.path);\n\n    let shouldRevalidate = false;\n    if (fetchRedirectIds.has(key)) {\n      // Never trigger a revalidation of an actively redirecting fetcher\n      shouldRevalidate = false;\n    } else if (cancelledFetcherLoads.includes(key)) {\n      // Always revalidate if the fetcher was cancelled\n      shouldRevalidate = true;\n    } else if (\n      fetcher &&\n      fetcher.state !== \"idle\" &&\n      fetcher.data === undefined\n    ) {\n      // If the fetcher hasn't ever completed loading yet, then this isn't a\n      // revalidation, it would just be a brand new load if an explicit\n      // revalidation is required\n      shouldRevalidate = isRevalidationRequired;\n    } else {\n      // Otherwise fall back on any user-defined shouldRevalidate, defaulting\n      // to explicit revalidations only\n      shouldRevalidate = shouldRevalidateLoader(fetcherMatch, {\n        currentUrl,\n        currentParams: state.matches[state.matches.length - 1].params,\n        nextUrl,\n        nextParams: matches[matches.length - 1].params,\n        ...submission,\n        actionResult,\n        defaultShouldRevalidate: isRevalidationRequired,\n      });\n    }\n\n    if (shouldRevalidate) {\n      revalidatingFetchers.push({\n        key,\n        routeId: f.routeId,\n        path: f.path,\n        matches: fetcherMatches,\n        match: fetcherMatch,\n        controller: new AbortController(),\n      });\n    }\n  });\n\n  return [navigationMatches, revalidatingFetchers];\n}\n\nfunction isNewLoader(\n  currentLoaderData: RouteData,\n  currentMatch: AgnosticDataRouteMatch,\n  match: AgnosticDataRouteMatch\n) {\n  let isNew =\n    // [a] -> [a, b]\n    !currentMatch ||\n    // [a, b] -> [a, c]\n    match.route.id !== currentMatch.route.id;\n\n  // Handle the case that we don't have data for a re-used route, potentially\n  // from a prior error or from a cancelled pending deferred\n  let isMissingData = currentLoaderData[match.route.id] === undefined;\n\n  // Always load if this is a net-new route or we don't yet have data\n  return isNew || isMissingData;\n}\n\nfunction isNewRouteInstance(\n  currentMatch: AgnosticDataRouteMatch,\n  match: AgnosticDataRouteMatch\n) {\n  let currentPath = currentMatch.route.path;\n  return (\n    // param change for this match, /users/123 -> /users/456\n    currentMatch.pathname !== match.pathname ||\n    // splat param changed, which is not present in match.path\n    // e.g. /files/images/avatar.jpg -> files/finances.xls\n    (currentPath != null &&\n      currentPath.endsWith(\"*\") &&\n      currentMatch.params[\"*\"] !== match.params[\"*\"])\n  );\n}\n\nfunction shouldRevalidateLoader(\n  loaderMatch: AgnosticDataRouteMatch,\n  arg: Parameters<ShouldRevalidateFunction>[0]\n) {\n  if (loaderMatch.route.shouldRevalidate) {\n    let routeChoice = loaderMatch.route.shouldRevalidate(arg);\n    if (typeof routeChoice === \"boolean\") {\n      return routeChoice;\n    }\n  }\n\n  return arg.defaultShouldRevalidate;\n}\n\n/**\n * Execute route.lazy() methods to lazily load route modules (loader, action,\n * shouldRevalidate) and update the routeManifest in place which shares objects\n * with dataRoutes so those get updated as well.\n */\nasync function loadLazyRouteModule(\n  route: AgnosticDataRouteObject,\n  mapRouteProperties: MapRoutePropertiesFunction,\n  manifest: RouteManifest\n) {\n  if (!route.lazy) {\n    return;\n  }\n\n  let lazyRoute = await route.lazy();\n\n  // If the lazy route function was executed and removed by another parallel\n  // call then we can return - first lazy() to finish wins because the return\n  // value of lazy is expected to be static\n  if (!route.lazy) {\n    return;\n  }\n\n  let routeToUpdate = manifest[route.id];\n  invariant(routeToUpdate, \"No route found in manifest\");\n\n  // Update the route in place.  This should be safe because there's no way\n  // we could yet be sitting on this route as we can't get there without\n  // resolving lazy() first.\n  //\n  // This is different than the HMR \"update\" use-case where we may actively be\n  // on the route being updated.  The main concern boils down to \"does this\n  // mutation affect any ongoing navigations or any current state.matches\n  // values?\".  If not, it should be safe to update in place.\n  let routeUpdates: Record<string, any> = {};\n  for (let lazyRouteProperty in lazyRoute) {\n    let staticRouteValue =\n      routeToUpdate[lazyRouteProperty as keyof typeof routeToUpdate];\n\n    let isPropertyStaticallyDefined =\n      staticRouteValue !== undefined &&\n      // This property isn't static since it should always be updated based\n      // on the route updates\n      lazyRouteProperty !== \"hasErrorBoundary\";\n\n    warning(\n      !isPropertyStaticallyDefined,\n      `Route \"${routeToUpdate.id}\" has a static property \"${lazyRouteProperty}\" ` +\n        `defined but its lazy function is also returning a value for this property. ` +\n        `The lazy route property \"${lazyRouteProperty}\" will be ignored.`\n    );\n\n    if (\n      !isPropertyStaticallyDefined &&\n      !immutableRouteKeys.has(lazyRouteProperty as ImmutableRouteKey)\n    ) {\n      routeUpdates[lazyRouteProperty] =\n        lazyRoute[lazyRouteProperty as keyof typeof lazyRoute];\n    }\n  }\n\n  // Mutate the route with the provided updates.  Do this first so we pass\n  // the updated version to mapRouteProperties\n  Object.assign(routeToUpdate, routeUpdates);\n\n  // Mutate the `hasErrorBoundary` property on the route based on the route\n  // updates and remove the `lazy` function so we don't resolve the lazy\n  // route again.\n  Object.assign(routeToUpdate, {\n    // To keep things framework agnostic, we use the provided\n    // `mapRouteProperties` (or wrapped `detectErrorBoundary`) function to\n    // set the framework-aware properties (`element`/`hasErrorBoundary`) since\n    // the logic will differ between frameworks.\n    ...mapRouteProperties(routeToUpdate),\n    lazy: undefined,\n  });\n}\n\nasync function callLoaderOrAction(\n  type: \"loader\" | \"action\",\n  request: Request,\n  match: AgnosticDataRouteMatch,\n  matches: AgnosticDataRouteMatch[],\n  manifest: RouteManifest,\n  mapRouteProperties: MapRoutePropertiesFunction,\n  basename: string,\n  opts: {\n    isStaticRequest?: boolean;\n    isRouteRequest?: boolean;\n    requestContext?: unknown;\n  } = {}\n): Promise<DataResult> {\n  let resultType;\n  let result;\n  let onReject: (() => void) | undefined;\n\n  let runHandler = (handler: ActionFunction | LoaderFunction) => {\n    // Setup a promise we can race against so that abort signals short circuit\n    let reject: () => void;\n    let abortPromise = new Promise((_, r) => (reject = r));\n    onReject = () => reject();\n    request.signal.addEventListener(\"abort\", onReject);\n    return Promise.race([\n      handler({\n        request,\n        params: match.params,\n        context: opts.requestContext,\n      }),\n      abortPromise,\n    ]);\n  };\n\n  try {\n    let handler = match.route[type];\n\n    if (match.route.lazy) {\n      if (handler) {\n        // Run statically defined handler in parallel with lazy()\n        let values = await Promise.all([\n          runHandler(handler),\n          loadLazyRouteModule(match.route, mapRouteProperties, manifest),\n        ]);\n        result = values[0];\n      } else {\n        // Load lazy route module, then run any returned handler\n        await loadLazyRouteModule(match.route, mapRouteProperties, manifest);\n\n        handler = match.route[type];\n        if (handler) {\n          // Handler still run even if we got interrupted to maintain consistency\n          // with un-abortable behavior of handler execution on non-lazy or\n          // previously-lazy-loaded routes\n          result = await runHandler(handler);\n        } else if (type === \"action\") {\n          let url = new URL(request.url);\n          let pathname = url.pathname + url.search;\n          throw getInternalRouterError(405, {\n            method: request.method,\n            pathname,\n            routeId: match.route.id,\n          });\n        } else {\n          // lazy() route has no loader to run.  Short circuit here so we don't\n          // hit the invariant below that errors on returning undefined.\n          return { type: ResultType.data, data: undefined };\n        }\n      }\n    } else if (!handler) {\n      let url = new URL(request.url);\n      let pathname = url.pathname + url.search;\n      throw getInternalRouterError(404, {\n        pathname,\n      });\n    } else {\n      result = await runHandler(handler);\n    }\n\n    invariant(\n      result !== undefined,\n      `You defined ${type === \"action\" ? \"an action\" : \"a loader\"} for route ` +\n        `\"${match.route.id}\" but didn't return anything from your \\`${type}\\` ` +\n        `function. Please return a value or \\`null\\`.`\n    );\n  } catch (e) {\n    resultType = ResultType.error;\n    result = e;\n  } finally {\n    if (onReject) {\n      request.signal.removeEventListener(\"abort\", onReject);\n    }\n  }\n\n  if (isResponse(result)) {\n    let status = result.status;\n\n    // Process redirects\n    if (redirectStatusCodes.has(status)) {\n      let location = result.headers.get(\"Location\");\n      invariant(\n        location,\n        \"Redirects returned/thrown from loaders/actions must have a Location header\"\n      );\n\n      // Support relative routing in internal redirects\n      if (!ABSOLUTE_URL_REGEX.test(location)) {\n        location = normalizeTo(\n          new URL(request.url),\n          matches.slice(0, matches.indexOf(match) + 1),\n          basename,\n          true,\n          location\n        );\n      } else if (!opts.isStaticRequest) {\n        // Strip off the protocol+origin for same-origin + same-basename absolute\n        // redirects. If this is a static request, we can let it go back to the\n        // browser as-is\n        let currentUrl = new URL(request.url);\n        let url = location.startsWith(\"//\")\n          ? new URL(currentUrl.protocol + location)\n          : new URL(location);\n        let isSameBasename = stripBasename(url.pathname, basename) != null;\n        if (url.origin === currentUrl.origin && isSameBasename) {\n          location = url.pathname + url.search + url.hash;\n        }\n      }\n\n      // Don't process redirects in the router during static requests requests.\n      // Instead, throw the Response and let the server handle it with an HTTP\n      // redirect.  We also update the Location header in place in this flow so\n      // basename and relative routing is taken into account\n      if (opts.isStaticRequest) {\n        result.headers.set(\"Location\", location);\n        throw result;\n      }\n\n      return {\n        type: ResultType.redirect,\n        status,\n        location,\n        revalidate: result.headers.get(\"X-Remix-Revalidate\") !== null,\n      };\n    }\n\n    // For SSR single-route requests, we want to hand Responses back directly\n    // without unwrapping.  We do this with the QueryRouteResponse wrapper\n    // interface so we can know whether it was returned or thrown\n    if (opts.isRouteRequest) {\n      // eslint-disable-next-line no-throw-literal\n      throw {\n        type: resultType || ResultType.data,\n        response: result,\n      };\n    }\n\n    let data: any;\n    let contentType = result.headers.get(\"Content-Type\");\n    // Check between word boundaries instead of startsWith() due to the last\n    // paragraph of https://httpwg.org/specs/rfc9110.html#field.content-type\n    if (contentType && /\\bapplication\\/json\\b/.test(contentType)) {\n      data = await result.json();\n    } else {\n      data = await result.text();\n    }\n\n    if (resultType === ResultType.error) {\n      return {\n        type: resultType,\n        error: new ErrorResponse(status, result.statusText, data),\n        headers: result.headers,\n      };\n    }\n\n    return {\n      type: ResultType.data,\n      data,\n      statusCode: result.status,\n      headers: result.headers,\n    };\n  }\n\n  if (resultType === ResultType.error) {\n    return { type: resultType, error: result };\n  }\n\n  if (isDeferredData(result)) {\n    return {\n      type: ResultType.deferred,\n      deferredData: result,\n      statusCode: result.init?.status,\n      headers: result.init?.headers && new Headers(result.init.headers),\n    };\n  }\n\n  return { type: ResultType.data, data: result };\n}\n\n// Utility method for creating the Request instances for loaders/actions during\n// client-side navigations and fetches.  During SSR we will always have a\n// Request instance from the static handler (query/queryRoute)\nfunction createClientSideRequest(\n  history: History,\n  location: string | Location,\n  signal: AbortSignal,\n  submission?: Submission\n): Request {\n  let url = history.createURL(stripHashFromPath(location)).toString();\n  let init: RequestInit = { signal };\n\n  if (submission && isMutationMethod(submission.formMethod)) {\n    let { formMethod, formEncType } = submission;\n    // Didn't think we needed this but it turns out unlike other methods, patch\n    // won't be properly normalized to uppercase and results in a 405 error.\n    // See: https://fetch.spec.whatwg.org/#concept-method\n    init.method = formMethod.toUpperCase();\n\n    if (formEncType === \"application/json\") {\n      init.headers = new Headers({ \"Content-Type\": formEncType });\n      init.body = JSON.stringify(submission.json);\n    } else if (formEncType === \"text/plain\") {\n      // Content-Type is inferred (https://fetch.spec.whatwg.org/#dom-request)\n      init.body = submission.text;\n    } else if (\n      formEncType === \"application/x-www-form-urlencoded\" &&\n      submission.formData\n    ) {\n      // Content-Type is inferred (https://fetch.spec.whatwg.org/#dom-request)\n      init.body = convertFormDataToSearchParams(submission.formData);\n    } else {\n      // Content-Type is inferred (https://fetch.spec.whatwg.org/#dom-request)\n      init.body = submission.formData;\n    }\n  }\n\n  return new Request(url, init);\n}\n\nfunction convertFormDataToSearchParams(formData: FormData): URLSearchParams {\n  let searchParams = new URLSearchParams();\n\n  for (let [key, value] of formData.entries()) {\n    // https://html.spec.whatwg.org/multipage/form-control-infrastructure.html#converting-an-entry-list-to-a-list-of-name-value-pairs\n    searchParams.append(key, typeof value === \"string\" ? value : value.name);\n  }\n\n  return searchParams;\n}\n\nfunction convertSearchParamsToFormData(\n  searchParams: URLSearchParams\n): FormData {\n  let formData = new FormData();\n  for (let [key, value] of searchParams.entries()) {\n    formData.append(key, value);\n  }\n  return formData;\n}\n\nfunction processRouteLoaderData(\n  matches: AgnosticDataRouteMatch[],\n  matchesToLoad: AgnosticDataRouteMatch[],\n  results: DataResult[],\n  pendingError: RouteData | undefined,\n  activeDeferreds: Map<string, DeferredData>\n): {\n  loaderData: RouterState[\"loaderData\"];\n  errors: RouterState[\"errors\"] | null;\n  statusCode: number;\n  loaderHeaders: Record<string, Headers>;\n} {\n  // Fill in loaderData/errors from our loaders\n  let loaderData: RouterState[\"loaderData\"] = {};\n  let errors: RouterState[\"errors\"] | null = null;\n  let statusCode: number | undefined;\n  let foundError = false;\n  let loaderHeaders: Record<string, Headers> = {};\n\n  // Process loader results into state.loaderData/state.errors\n  results.forEach((result, index) => {\n    let id = matchesToLoad[index].route.id;\n    invariant(\n      !isRedirectResult(result),\n      \"Cannot handle redirect results in processLoaderData\"\n    );\n    if (isErrorResult(result)) {\n      // Look upwards from the matched route for the closest ancestor\n      // error boundary, defaulting to the root match\n      let boundaryMatch = findNearestBoundary(matches, id);\n      let error = result.error;\n      // If we have a pending action error, we report it at the highest-route\n      // that throws a loader error, and then clear it out to indicate that\n      // it was consumed\n      if (pendingError) {\n        error = Object.values(pendingError)[0];\n        pendingError = undefined;\n      }\n\n      errors = errors || {};\n\n      // Prefer higher error values if lower errors bubble to the same boundary\n      if (errors[boundaryMatch.route.id] == null) {\n        errors[boundaryMatch.route.id] = error;\n      }\n\n      // Clear our any prior loaderData for the throwing route\n      loaderData[id] = undefined;\n\n      // Once we find our first (highest) error, we set the status code and\n      // prevent deeper status codes from overriding\n      if (!foundError) {\n        foundError = true;\n        statusCode = isRouteErrorResponse(result.error)\n          ? result.error.status\n          : 500;\n      }\n      if (result.headers) {\n        loaderHeaders[id] = result.headers;\n      }\n    } else {\n      if (isDeferredResult(result)) {\n        activeDeferreds.set(id, result.deferredData);\n        loaderData[id] = result.deferredData.data;\n      } else {\n        loaderData[id] = result.data;\n      }\n\n      // Error status codes always override success status codes, but if all\n      // loaders are successful we take the deepest status code.\n      if (\n        result.statusCode != null &&\n        result.statusCode !== 200 &&\n        !foundError\n      ) {\n        statusCode = result.statusCode;\n      }\n      if (result.headers) {\n        loaderHeaders[id] = result.headers;\n      }\n    }\n  });\n\n  // If we didn't consume the pending action error (i.e., all loaders\n  // resolved), then consume it here.  Also clear out any loaderData for the\n  // throwing route\n  if (pendingError) {\n    errors = pendingError;\n    loaderData[Object.keys(pendingError)[0]] = undefined;\n  }\n\n  return {\n    loaderData,\n    errors,\n    statusCode: statusCode || 200,\n    loaderHeaders,\n  };\n}\n\nfunction processLoaderData(\n  state: RouterState,\n  matches: AgnosticDataRouteMatch[],\n  matchesToLoad: AgnosticDataRouteMatch[],\n  results: DataResult[],\n  pendingError: RouteData | undefined,\n  revalidatingFetchers: RevalidatingFetcher[],\n  fetcherResults: DataResult[],\n  activeDeferreds: Map<string, DeferredData>\n): {\n  loaderData: RouterState[\"loaderData\"];\n  errors?: RouterState[\"errors\"];\n} {\n  let { loaderData, errors } = processRouteLoaderData(\n    matches,\n    matchesToLoad,\n    results,\n    pendingError,\n    activeDeferreds\n  );\n\n  // Process results from our revalidating fetchers\n  for (let index = 0; index < revalidatingFetchers.length; index++) {\n    let { key, match, controller } = revalidatingFetchers[index];\n    invariant(\n      fetcherResults !== undefined && fetcherResults[index] !== undefined,\n      \"Did not find corresponding fetcher result\"\n    );\n    let result = fetcherResults[index];\n\n    // Process fetcher non-redirect errors\n    if (controller && controller.signal.aborted) {\n      // Nothing to do for aborted fetchers\n      continue;\n    } else if (isErrorResult(result)) {\n      let boundaryMatch = findNearestBoundary(state.matches, match?.route.id);\n      if (!(errors && errors[boundaryMatch.route.id])) {\n        errors = {\n          ...errors,\n          [boundaryMatch.route.id]: result.error,\n        };\n      }\n      state.fetchers.delete(key);\n    } else if (isRedirectResult(result)) {\n      // Should never get here, redirects should get processed above, but we\n      // keep this to type narrow to a success result in the else\n      invariant(false, \"Unhandled fetcher revalidation redirect\");\n    } else if (isDeferredResult(result)) {\n      // Should never get here, deferred data should be awaited for fetchers\n      // in resolveDeferredResults\n      invariant(false, \"Unhandled fetcher deferred data\");\n    } else {\n      let doneFetcher = getDoneFetcher(result.data);\n      state.fetchers.set(key, doneFetcher);\n    }\n  }\n\n  return { loaderData, errors };\n}\n\nfunction mergeLoaderData(\n  loaderData: RouteData,\n  newLoaderData: RouteData,\n  matches: AgnosticDataRouteMatch[],\n  errors: RouteData | null | undefined\n): RouteData {\n  let mergedLoaderData = { ...newLoaderData };\n  for (let match of matches) {\n    let id = match.route.id;\n    if (newLoaderData.hasOwnProperty(id)) {\n      if (newLoaderData[id] !== undefined) {\n        mergedLoaderData[id] = newLoaderData[id];\n      } else {\n        // No-op - this is so we ignore existing data if we have a key in the\n        // incoming object with an undefined value, which is how we unset a prior\n        // loaderData if we encounter a loader error\n      }\n    } else if (loaderData[id] !== undefined && match.route.loader) {\n      // Preserve existing keys not included in newLoaderData and where a loader\n      // wasn't removed by HMR\n      mergedLoaderData[id] = loaderData[id];\n    }\n\n    if (errors && errors.hasOwnProperty(id)) {\n      // Don't keep any loader data below the boundary\n      break;\n    }\n  }\n  return mergedLoaderData;\n}\n\n// Find the nearest error boundary, looking upwards from the leaf route (or the\n// route specified by routeId) for the closest ancestor error boundary,\n// defaulting to the root match\nfunction findNearestBoundary(\n  matches: AgnosticDataRouteMatch[],\n  routeId?: string\n): AgnosticDataRouteMatch {\n  let eligibleMatches = routeId\n    ? matches.slice(0, matches.findIndex((m) => m.route.id === routeId) + 1)\n    : [...matches];\n  return (\n    eligibleMatches.reverse().find((m) => m.route.hasErrorBoundary === true) ||\n    matches[0]\n  );\n}\n\nfunction getShortCircuitMatches(routes: AgnosticDataRouteObject[]): {\n  matches: AgnosticDataRouteMatch[];\n  route: AgnosticDataRouteObject;\n} {\n  // Prefer a root layout route if present, otherwise shim in a route object\n  let route = routes.find((r) => r.index || !r.path || r.path === \"/\") || {\n    id: `__shim-error-route__`,\n  };\n\n  return {\n    matches: [\n      {\n        params: {},\n        pathname: \"\",\n        pathnameBase: \"\",\n        route,\n      },\n    ],\n    route,\n  };\n}\n\nfunction getInternalRouterError(\n  status: number,\n  {\n    pathname,\n    routeId,\n    method,\n    type,\n  }: {\n    pathname?: string;\n    routeId?: string;\n    method?: string;\n    type?: \"defer-action\" | \"invalid-body\";\n  } = {}\n) {\n  let statusText = \"Unknown Server Error\";\n  let errorMessage = \"Unknown @remix-run/router error\";\n\n  if (status === 400) {\n    statusText = \"Bad Request\";\n    if (method && pathname && routeId) {\n      errorMessage =\n        `You made a ${method} request to \"${pathname}\" but ` +\n        `did not provide a \\`loader\\` for route \"${routeId}\", ` +\n        `so there is no way to handle the request.`;\n    } else if (type === \"defer-action\") {\n      errorMessage = \"defer() is not supported in actions\";\n    } else if (type === \"invalid-body\") {\n      errorMessage = \"Unable to encode submission body\";\n    }\n  } else if (status === 403) {\n    statusText = \"Forbidden\";\n    errorMessage = `Route \"${routeId}\" does not match URL \"${pathname}\"`;\n  } else if (status === 404) {\n    statusText = \"Not Found\";\n    errorMessage = `No route matches URL \"${pathname}\"`;\n  } else if (status === 405) {\n    statusText = \"Method Not Allowed\";\n    if (method && pathname && routeId) {\n      errorMessage =\n        `You made a ${method.toUpperCase()} request to \"${pathname}\" but ` +\n        `did not provide an \\`action\\` for route \"${routeId}\", ` +\n        `so there is no way to handle the request.`;\n    } else if (method) {\n      errorMessage = `Invalid request method \"${method.toUpperCase()}\"`;\n    }\n  }\n\n  return new ErrorResponse(\n    status || 500,\n    statusText,\n    new Error(errorMessage),\n    true\n  );\n}\n\n// Find any returned redirect errors, starting from the lowest match\nfunction findRedirect(\n  results: DataResult[]\n): { result: RedirectResult; idx: number } | undefined {\n  for (let i = results.length - 1; i >= 0; i--) {\n    let result = results[i];\n    if (isRedirectResult(result)) {\n      return { result, idx: i };\n    }\n  }\n}\n\nfunction stripHashFromPath(path: To) {\n  let parsedPath = typeof path === \"string\" ? parsePath(path) : path;\n  return createPath({ ...parsedPath, hash: \"\" });\n}\n\nfunction isHashChangeOnly(a: Location, b: Location): boolean {\n  if (a.pathname !== b.pathname || a.search !== b.search) {\n    return false;\n  }\n\n  if (a.hash === \"\") {\n    // /page -> /page#hash\n    return b.hash !== \"\";\n  } else if (a.hash === b.hash) {\n    // /page#hash -> /page#hash\n    return true;\n  } else if (b.hash !== \"\") {\n    // /page#hash -> /page#other\n    return true;\n  }\n\n  // If the hash is removed the browser will re-perform a request to the server\n  // /page#hash -> /page\n  return false;\n}\n\nfunction isDeferredResult(result: DataResult): result is DeferredResult {\n  return result.type === ResultType.deferred;\n}\n\nfunction isErrorResult(result: DataResult): result is ErrorResult {\n  return result.type === ResultType.error;\n}\n\nfunction isRedirectResult(result?: DataResult): result is RedirectResult {\n  return (result && result.type) === ResultType.redirect;\n}\n\nexport function isDeferredData(value: any): value is DeferredData {\n  let deferred: DeferredData = value;\n  return (\n    deferred &&\n    typeof deferred === \"object\" &&\n    typeof deferred.data === \"object\" &&\n    typeof deferred.subscribe === \"function\" &&\n    typeof deferred.cancel === \"function\" &&\n    typeof deferred.resolveData === \"function\"\n  );\n}\n\nfunction isResponse(value: any): value is Response {\n  return (\n    value != null &&\n    typeof value.status === \"number\" &&\n    typeof value.statusText === \"string\" &&\n    typeof value.headers === \"object\" &&\n    typeof value.body !== \"undefined\"\n  );\n}\n\nfunction isRedirectResponse(result: any): result is Response {\n  if (!isResponse(result)) {\n    return false;\n  }\n\n  let status = result.status;\n  let location = result.headers.get(\"Location\");\n  return status >= 300 && status <= 399 && location != null;\n}\n\nfunction isQueryRouteResponse(obj: any): obj is QueryRouteResponse {\n  return (\n    obj &&\n    isResponse(obj.response) &&\n    (obj.type === ResultType.data || ResultType.error)\n  );\n}\n\nfunction isValidMethod(method: string): method is FormMethod | V7_FormMethod {\n  return validRequestMethods.has(method.toLowerCase() as FormMethod);\n}\n\nfunction isMutationMethod(\n  method: string\n): method is MutationFormMethod | V7_MutationFormMethod {\n  return validMutationMethods.has(method.toLowerCase() as MutationFormMethod);\n}\n\nasync function resolveDeferredResults(\n  currentMatches: AgnosticDataRouteMatch[],\n  matchesToLoad: (AgnosticDataRouteMatch | null)[],\n  results: DataResult[],\n  signals: (AbortSignal | null)[],\n  isFetcher: boolean,\n  currentLoaderData?: RouteData\n) {\n  for (let index = 0; index < results.length; index++) {\n    let result = results[index];\n    let match = matchesToLoad[index];\n    // If we don't have a match, then we can have a deferred result to do\n    // anything with.  This is for revalidating fetchers where the route was\n    // removed during HMR\n    if (!match) {\n      continue;\n    }\n\n    let currentMatch = currentMatches.find(\n      (m) => m.route.id === match!.route.id\n    );\n    let isRevalidatingLoader =\n      currentMatch != null &&\n      !isNewRouteInstance(currentMatch, match) &&\n      (currentLoaderData && currentLoaderData[match.route.id]) !== undefined;\n\n    if (isDeferredResult(result) && (isFetcher || isRevalidatingLoader)) {\n      // Note: we do not have to touch activeDeferreds here since we race them\n      // against the signal in resolveDeferredData and they'll get aborted\n      // there if needed\n      let signal = signals[index];\n      invariant(\n        signal,\n        \"Expected an AbortSignal for revalidating fetcher deferred result\"\n      );\n      await resolveDeferredData(result, signal, isFetcher).then((result) => {\n        if (result) {\n          results[index] = result || results[index];\n        }\n      });\n    }\n  }\n}\n\nasync function resolveDeferredData(\n  result: DeferredResult,\n  signal: AbortSignal,\n  unwrap = false\n): Promise<SuccessResult | ErrorResult | undefined> {\n  let aborted = await result.deferredData.resolveData(signal);\n  if (aborted) {\n    return;\n  }\n\n  if (unwrap) {\n    try {\n      return {\n        type: ResultType.data,\n        data: result.deferredData.unwrappedData,\n      };\n    } catch (e) {\n      // Handle any TrackedPromise._error values encountered while unwrapping\n      return {\n        type: ResultType.error,\n        error: e,\n      };\n    }\n  }\n\n  return {\n    type: ResultType.data,\n    data: result.deferredData.data,\n  };\n}\n\nfunction hasNakedIndexQuery(search: string): boolean {\n  return new URLSearchParams(search).getAll(\"index\").some((v) => v === \"\");\n}\n\n// Note: This should match the format exported by useMatches, so if you change\n// this please also change that :)  Eventually we'll DRY this up\nfunction createUseMatchesMatch(\n  match: AgnosticDataRouteMatch,\n  loaderData: RouteData\n): UseMatchesMatch {\n  let { route, pathname, params } = match;\n  return {\n    id: route.id,\n    pathname,\n    params,\n    data: loaderData[route.id] as unknown,\n    handle: route.handle as unknown,\n  };\n}\n\nfunction getTargetMatch(\n  matches: AgnosticDataRouteMatch[],\n  location: Location | string\n) {\n  let search =\n    typeof location === \"string\" ? parsePath(location).search : location.search;\n  if (\n    matches[matches.length - 1].route.index &&\n    hasNakedIndexQuery(search || \"\")\n  ) {\n    // Return the leaf index route when index is present\n    return matches[matches.length - 1];\n  }\n  // Otherwise grab the deepest \"path contributing\" match (ignoring index and\n  // pathless layout routes)\n  let pathMatches = getPathContributingMatches(matches);\n  return pathMatches[pathMatches.length - 1];\n}\n\nfunction getSubmissionFromNavigation(\n  navigation: Navigation\n): Submission | undefined {\n  let { formMethod, formAction, formEncType, text, formData, json } =\n    navigation;\n  if (!formMethod || !formAction || !formEncType) {\n    return;\n  }\n\n  if (text != null) {\n    return {\n      formMethod,\n      formAction,\n      formEncType,\n      formData: undefined,\n      json: undefined,\n      text,\n    };\n  } else if (formData != null) {\n    return {\n      formMethod,\n      formAction,\n      formEncType,\n      formData,\n      json: undefined,\n      text: undefined,\n    };\n  } else if (json !== undefined) {\n    return {\n      formMethod,\n      formAction,\n      formEncType,\n      formData: undefined,\n      json,\n      text: undefined,\n    };\n  }\n}\n\nfunction getLoadingNavigation(\n  location: Location,\n  submission?: Submission\n): NavigationStates[\"Loading\"] {\n  if (submission) {\n    let navigation: NavigationStates[\"Loading\"] = {\n      state: \"loading\",\n      location,\n      formMethod: submission.formMethod,\n      formAction: submission.formAction,\n      formEncType: submission.formEncType,\n      formData: submission.formData,\n      json: submission.json,\n      text: submission.text,\n    };\n    return navigation;\n  } else {\n    let navigation: NavigationStates[\"Loading\"] = {\n      state: \"loading\",\n      location,\n      formMethod: undefined,\n      formAction: undefined,\n      formEncType: undefined,\n      formData: undefined,\n      json: undefined,\n      text: undefined,\n    };\n    return navigation;\n  }\n}\n\nfunction getSubmittingNavigation(\n  location: Location,\n  submission: Submission\n): NavigationStates[\"Submitting\"] {\n  let navigation: NavigationStates[\"Submitting\"] = {\n    state: \"submitting\",\n    location,\n    formMethod: submission.formMethod,\n    formAction: submission.formAction,\n    formEncType: submission.formEncType,\n    formData: submission.formData,\n    json: submission.json,\n    text: submission.text,\n  };\n  return navigation;\n}\n\nfunction getLoadingFetcher(\n  submission?: Submission,\n  data?: Fetcher[\"data\"]\n): FetcherStates[\"Loading\"] {\n  if (submission) {\n    let fetcher: FetcherStates[\"Loading\"] = {\n      state: \"loading\",\n      formMethod: submission.formMethod,\n      formAction: submission.formAction,\n      formEncType: submission.formEncType,\n      formData: submission.formData,\n      json: submission.json,\n      text: submission.text,\n      data,\n      \" _hasFetcherDoneAnything \": true,\n    };\n    return fetcher;\n  } else {\n    let fetcher: FetcherStates[\"Loading\"] = {\n      state: \"loading\",\n      formMethod: undefined,\n      formAction: undefined,\n      formEncType: undefined,\n      formData: undefined,\n      json: undefined,\n      text: undefined,\n      data,\n      \" _hasFetcherDoneAnything \": true,\n    };\n    return fetcher;\n  }\n}\n\nfunction getSubmittingFetcher(\n  submission: Submission,\n  existingFetcher?: Fetcher\n): FetcherStates[\"Submitting\"] {\n  let fetcher: FetcherStates[\"Submitting\"] = {\n    state: \"submitting\",\n    formMethod: submission.formMethod,\n    formAction: submission.formAction,\n    formEncType: submission.formEncType,\n    formData: submission.formData,\n    json: submission.json,\n    text: submission.text,\n    data: existingFetcher ? existingFetcher.data : undefined,\n    \" _hasFetcherDoneAnything \": true,\n  };\n  return fetcher;\n}\n\nfunction getDoneFetcher(data: Fetcher[\"data\"]): FetcherStates[\"Idle\"] {\n  let fetcher: FetcherStates[\"Idle\"] = {\n    state: \"idle\",\n    formMethod: undefined,\n    formAction: undefined,\n    formEncType: undefined,\n    formData: undefined,\n    json: undefined,\n    text: undefined,\n    data,\n    \" _hasFetcherDoneAnything \": true,\n  };\n  return fetcher;\n}\n//#endregion\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AAEA;;AAEG;IACSA,MAAA;AAAZ,WAAYA,MAAM;EAChB;;;;;;AAMG;EACHA,MAAA,eAAW;EAEX;;;;AAIG;EACHA,MAAA,iBAAa;EAEb;;;AAGG;EACHA,MAAA,uBAAmB;AACrB,CAAC,EAtBWA,MAAM,KAANA,MAAM,GAsBjB;AAkKD,MAAMC,iBAAiB,GAAG,UAAU;AA+BpC;;;AAGG;AACa,SAAAC,mBAAmBA,CACjCC,OAAA,EAAkC;EAAA,IAAlCA,OAAA;IAAAA,OAAA,GAAgC,EAAE;EAAA;EAElC,IAAI;IAAEC,cAAc,GAAG,CAAC,GAAG,CAAC;IAAEC,YAAY;IAAEC,QAAQ,GAAG;EAAO,IAAGH,OAAO;EACxE,IAAII,OAAmB,CAAC;EACxBA,OAAO,GAAGH,cAAc,CAACI,GAAG,CAAC,CAACC,KAAK,EAAEC,KAAK,KACxCC,oBAAoB,CAClBF,KAAK,EACL,OAAOA,KAAK,KAAK,QAAQ,GAAG,IAAI,GAAGA,KAAK,CAACG,KAAK,EAC9CF,KAAK,KAAK,CAAC,GAAG,SAAS,GAAGG,SAAS,CACpC,CACF;EACD,IAAIH,KAAK,GAAGI,UAAU,CACpBT,YAAY,IAAI,IAAI,GAAGE,OAAO,CAACQ,MAAM,GAAG,CAAC,GAAGV,YAAY,CACzD;EACD,IAAIW,MAAM,GAAGhB,MAAM,CAACiB,GAAG;EACvB,IAAIC,QAAQ,GAAoB,IAAI;EAEpC,SAASJ,UAAUA,CAACK,CAAS;IAC3B,OAAOC,IAAI,CAACC,GAAG,CAACD,IAAI,CAACE,GAAG,CAACH,CAAC,EAAE,CAAC,CAAC,EAAEZ,OAAO,CAACQ,MAAM,GAAG,CAAC,CAAC;EACrD;EACA,SAASQ,kBAAkBA,CAAA;IACzB,OAAOhB,OAAO,CAACG,KAAK,CAAC;EACvB;EACA,SAASC,oBAAoBA,CAC3Ba,EAAM,EACNZ,KAAa,EACba,GAAY;IAAA,IADZb,KAAa;MAAbA,KAAa,OAAI;IAAA;IAGjB,IAAIc,QAAQ,GAAGC,cAAc,CAC3BpB,OAAO,GAAGgB,kBAAkB,EAAE,CAACK,QAAQ,GAAG,GAAG,EAC7CJ,EAAE,EACFZ,KAAK,EACLa,GAAG,CACJ;IACDI,OAAO,CACLH,QAAQ,CAACE,QAAQ,CAACE,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,+DACwBC,IAAI,CAACC,SAAS,CACvER,EAAE,CACD,CACJ;IACD,OAAOE,QAAQ;EACjB;EAEA,SAASO,UAAUA,CAACT,EAAM;IACxB,OAAO,OAAOA,EAAE,KAAK,QAAQ,GAAGA,EAAE,GAAGU,UAAU,CAACV,EAAE,CAAC;EACrD;EAEA,IAAIW,OAAO,GAAkB;IAC3B,IAAIzB,KAAKA,CAAA;MACP,OAAOA,KAAK;KACb;IACD,IAAIM,MAAMA,CAAA;MACR,OAAOA,MAAM;KACd;IACD,IAAIU,QAAQA,CAAA;MACV,OAAOH,kBAAkB,EAAE;KAC5B;IACDU,UAAU;IACVG,SAASA,CAACZ,EAAE;MACV,OAAO,IAAIa,GAAG,CAACJ,UAAU,CAACT,EAAE,CAAC,EAAE,kBAAkB,CAAC;KACnD;IACDc,cAAcA,CAACd,EAAM;MACnB,IAAIe,IAAI,GAAG,OAAOf,EAAE,KAAK,QAAQ,GAAGgB,SAAS,CAAChB,EAAE,CAAC,GAAGA,EAAE;MACtD,OAAO;QACLI,QAAQ,EAAEW,IAAI,CAACX,QAAQ,IAAI,EAAE;QAC7Ba,MAAM,EAAEF,IAAI,CAACE,MAAM,IAAI,EAAE;QACzBC,IAAI,EAAEH,IAAI,CAACG,IAAI,IAAI;OACpB;KACF;IACDC,IAAIA,CAACnB,EAAE,EAAEZ,KAAK;MACZI,MAAM,GAAGhB,MAAM,CAAC4C,IAAI;MACpB,IAAIC,YAAY,GAAGlC,oBAAoB,CAACa,EAAE,EAAEZ,KAAK,CAAC;MAClDF,KAAK,IAAI,CAAC;MACVH,OAAO,CAACuC,MAAM,CAACpC,KAAK,EAAEH,OAAO,CAACQ,MAAM,EAAE8B,YAAY,CAAC;MACnD,IAAIvC,QAAQ,IAAIY,QAAQ,EAAE;QACxBA,QAAQ,CAAC;UAAEF,MAAM;UAAEU,QAAQ,EAAEmB,YAAY;UAAEE,KAAK,EAAE;QAAC,CAAE,CAAC;MACvD;KACF;IACDC,OAAOA,CAACxB,EAAE,EAAEZ,KAAK;MACfI,MAAM,GAAGhB,MAAM,CAACiD,OAAO;MACvB,IAAIJ,YAAY,GAAGlC,oBAAoB,CAACa,EAAE,EAAEZ,KAAK,CAAC;MAClDL,OAAO,CAACG,KAAK,CAAC,GAAGmC,YAAY;MAC7B,IAAIvC,QAAQ,IAAIY,QAAQ,EAAE;QACxBA,QAAQ,CAAC;UAAEF,MAAM;UAAEU,QAAQ,EAAEmB,YAAY;UAAEE,KAAK,EAAE;QAAC,CAAE,CAAC;MACvD;KACF;IACDG,EAAEA,CAACH,KAAK;MACN/B,MAAM,GAAGhB,MAAM,CAACiB,GAAG;MACnB,IAAIkC,SAAS,GAAGrC,UAAU,CAACJ,KAAK,GAAGqC,KAAK,CAAC;MACzC,IAAIF,YAAY,GAAGtC,OAAO,CAAC4C,SAAS,CAAC;MACrCzC,KAAK,GAAGyC,SAAS;MACjB,IAAIjC,QAAQ,EAAE;QACZA,QAAQ,CAAC;UAAEF,MAAM;UAAEU,QAAQ,EAAEmB,YAAY;UAAEE;QAAO,EAAC;MACpD;KACF;IACDK,MAAMA,CAACC,EAAY;MACjBnC,QAAQ,GAAGmC,EAAE;MACb,OAAO,MAAK;QACVnC,QAAQ,GAAG,IAAI;OAChB;IACH;GACD;EAED,OAAOiB,OAAO;AAChB;AAkBA;;;;;;AAMG;AACa,SAAAmB,oBAAoBA,CAClCnD,OAAA,EAAmC;EAAA,IAAnCA,OAAA;IAAAA,OAAA,GAAiC,EAAE;EAAA;EAEnC,SAASoD,qBAAqBA,CAC5BC,MAAc,EACdC,aAAgC;IAEhC,IAAI;MAAE7B,QAAQ;MAAEa,MAAM;MAAEC;KAAM,GAAGc,MAAM,CAAC9B,QAAQ;IAChD,OAAOC,cAAc,CACnB,EAAE,EACF;MAAEC,QAAQ;MAAEa,MAAM;MAAEC;KAAM;IAC1B;IACCe,aAAa,CAAC7C,KAAK,IAAI6C,aAAa,CAAC7C,KAAK,CAAC8C,GAAG,IAAK,IAAI,EACvDD,aAAa,CAAC7C,KAAK,IAAI6C,aAAa,CAAC7C,KAAK,CAACa,GAAG,IAAK,SAAS,CAC9D;EACH;EAEA,SAASkC,iBAAiBA,CAACH,MAAc,EAAEhC,EAAM;IAC/C,OAAO,OAAOA,EAAE,KAAK,QAAQ,GAAGA,EAAE,GAAGU,UAAU,CAACV,EAAE,CAAC;EACrD;EAEA,OAAOoC,kBAAkB,CACvBL,qBAAqB,EACrBI,iBAAiB,EACjB,IAAI,EACJxD,OAAO,CACR;AACH;AAsBA;;;;;;;AAOG;AACa,SAAA0D,iBAAiBA,CAC/B1D,OAAA,EAAgC;EAAA,IAAhCA,OAAA;IAAAA,OAAA,GAA8B,EAAE;EAAA;EAEhC,SAAS2D,kBAAkBA,CACzBN,MAAc,EACdC,aAAgC;IAEhC,IAAI;MACF7B,QAAQ,GAAG,GAAG;MACda,MAAM,GAAG,EAAE;MACXC,IAAI,GAAG;IAAE,CACV,GAAGF,SAAS,CAACgB,MAAM,CAAC9B,QAAQ,CAACgB,IAAI,CAACqB,MAAM,CAAC,CAAC,CAAC,CAAC;IAC7C,OAAOpC,cAAc,CACnB,EAAE,EACF;MAAEC,QAAQ;MAAEa,MAAM;MAAEC;KAAM;IAC1B;IACCe,aAAa,CAAC7C,KAAK,IAAI6C,aAAa,CAAC7C,KAAK,CAAC8C,GAAG,IAAK,IAAI,EACvDD,aAAa,CAAC7C,KAAK,IAAI6C,aAAa,CAAC7C,KAAK,CAACa,GAAG,IAAK,SAAS,CAC9D;EACH;EAEA,SAASuC,cAAcA,CAACR,MAAc,EAAEhC,EAAM;IAC5C,IAAIyC,IAAI,GAAGT,MAAM,CAACU,QAAQ,CAACC,aAAa,CAAC,MAAM,CAAC;IAChD,IAAIC,IAAI,GAAG,EAAE;IAEb,IAAIH,IAAI,IAAIA,IAAI,CAACI,YAAY,CAAC,MAAM,CAAC,EAAE;MACrC,IAAIC,GAAG,GAAGd,MAAM,CAAC9B,QAAQ,CAAC0C,IAAI;MAC9B,IAAIG,SAAS,GAAGD,GAAG,CAACE,OAAO,CAAC,GAAG,CAAC;MAChCJ,IAAI,GAAGG,SAAS,KAAK,CAAC,CAAC,GAAGD,GAAG,GAAGA,GAAG,CAACG,KAAK,CAAC,CAAC,EAAEF,SAAS,CAAC;IACxD;IAED,OAAOH,IAAI,GAAG,GAAG,IAAI,OAAO5C,EAAE,KAAK,QAAQ,GAAGA,EAAE,GAAGU,UAAU,CAACV,EAAE,CAAC,CAAC;EACpE;EAEA,SAASkD,oBAAoBA,CAAChD,QAAkB,EAAEF,EAAM;IACtDK,OAAO,CACLH,QAAQ,CAACE,QAAQ,CAACE,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,iEAC0BC,IAAI,CAACC,SAAS,CACzER,EAAE,CACH,MAAG,CACL;EACH;EAEA,OAAOoC,kBAAkB,CACvBE,kBAAkB,EAClBE,cAAc,EACdU,oBAAoB,EACpBvE,OAAO,CACR;AACH;AAegB,SAAAwE,SAASA,CAACC,KAAU,EAAEC,OAAgB;EACpD,IAAID,KAAK,KAAK,KAAK,IAAIA,KAAK,KAAK,IAAI,IAAI,OAAOA,KAAK,KAAK,WAAW,EAAE;IACrE,MAAM,IAAIE,KAAK,CAACD,OAAO,CAAC;EACzB;AACH;AAEgB,SAAAhD,OAAOA,CAACkD,IAAS,EAAEF,OAAe;EAChD,IAAI,CAACE,IAAI,EAAE;IACT;IACA,IAAI,OAAOC,OAAO,KAAK,WAAW,EAAEA,OAAO,CAACC,IAAI,CAACJ,OAAO,CAAC;IAEzD,IAAI;MACF;MACA;MACA;MACA;MACA;MACA,MAAM,IAAIC,KAAK,CAACD,OAAO,CAAC;MACxB;IACD,EAAC,OAAOK,CAAC,EAAE;EACb;AACH;AAEA,SAASC,SAASA,CAAA;EAChB,OAAO/D,IAAI,CAACgE,MAAM,EAAE,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACtB,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC;AAChD;AAEA;;AAEG;AACH,SAASuB,eAAeA,CAAC5D,QAAkB,EAAEhB,KAAa;EACxD,OAAO;IACLgD,GAAG,EAAEhC,QAAQ,CAACd,KAAK;IACnBa,GAAG,EAAEC,QAAQ,CAACD,GAAG;IACjB8D,GAAG,EAAE7E;GACN;AACH;AAEA;;AAEG;AACG,SAAUiB,cAAcA,CAC5B6D,OAA0B,EAC1BhE,EAAM,EACNZ,KAAA,EACAa,GAAY;EAAA,IADZb,KAAA;IAAAA,KAAA,GAAa,IAAI;EAAA;EAGjB,IAAIc,QAAQ,GAAA+D,QAAA;IACV7D,QAAQ,EAAE,OAAO4D,OAAO,KAAK,QAAQ,GAAGA,OAAO,GAAGA,OAAO,CAAC5D,QAAQ;IAClEa,MAAM,EAAE,EAAE;IACVC,IAAI,EAAE;GACF,SAAOlB,EAAE,KAAK,QAAQ,GAAGgB,SAAS,CAAChB,EAAE,CAAC,GAAGA,EAAE;IAC/CZ,KAAK;IACL;IACA;IACA;IACA;IACAa,GAAG,EAAGD,EAAE,IAAKA,EAAe,CAACC,GAAG,IAAKA,GAAG,IAAI0D,SAAS;GACtD;EACD,OAAOzD,QAAQ;AACjB;AAEA;;AAEG;AACa,SAAAQ,UAAUA,CAAAwD,IAAA,EAIV;EAAA,IAJW;IACzB9D,QAAQ,GAAG,GAAG;IACda,MAAM,GAAG,EAAE;IACXC,IAAI,GAAG;EACO,IAAAgD,IAAA;EACd,IAAIjD,MAAM,IAAIA,MAAM,KAAK,GAAG,EAC1Bb,QAAQ,IAAIa,MAAM,CAACX,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,GAAGW,MAAM,GAAG,GAAG,GAAGA,MAAM;EAC9D,IAAIC,IAAI,IAAIA,IAAI,KAAK,GAAG,EACtBd,QAAQ,IAAIc,IAAI,CAACZ,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,GAAGY,IAAI,GAAG,GAAG,GAAGA,IAAI;EACxD,OAAOd,QAAQ;AACjB;AAEA;;AAEG;AACG,SAAUY,SAASA,CAACD,IAAY;EACpC,IAAIoD,UAAU,GAAkB,EAAE;EAElC,IAAIpD,IAAI,EAAE;IACR,IAAIgC,SAAS,GAAGhC,IAAI,CAACiC,OAAO,CAAC,GAAG,CAAC;IACjC,IAAID,SAAS,IAAI,CAAC,EAAE;MAClBoB,UAAU,CAACjD,IAAI,GAAGH,IAAI,CAACwB,MAAM,CAACQ,SAAS,CAAC;MACxChC,IAAI,GAAGA,IAAI,CAACwB,MAAM,CAAC,CAAC,EAAEQ,SAAS,CAAC;IACjC;IAED,IAAIqB,WAAW,GAAGrD,IAAI,CAACiC,OAAO,CAAC,GAAG,CAAC;IACnC,IAAIoB,WAAW,IAAI,CAAC,EAAE;MACpBD,UAAU,CAAClD,MAAM,GAAGF,IAAI,CAACwB,MAAM,CAAC6B,WAAW,CAAC;MAC5CrD,IAAI,GAAGA,IAAI,CAACwB,MAAM,CAAC,CAAC,EAAE6B,WAAW,CAAC;IACnC;IAED,IAAIrD,IAAI,EAAE;MACRoD,UAAU,CAAC/D,QAAQ,GAAGW,IAAI;IAC3B;EACF;EAED,OAAOoD,UAAU;AACnB;AASA,SAAS/B,kBAAkBA,CACzBiC,WAA2E,EAC3E5D,UAA8C,EAC9C6D,gBAA+D,EAC/D3F,OAAA,EAA+B;EAAA,IAA/BA,OAAA;IAAAA,OAAA,GAA6B,EAAE;EAAA;EAE/B,IAAI;IAAEqD,MAAM,GAAGU,QAAQ,CAAC6B,WAAY;IAAEzF,QAAQ,GAAG;EAAO,IAAGH,OAAO;EAClE,IAAIsD,aAAa,GAAGD,MAAM,CAACrB,OAAO;EAClC,IAAInB,MAAM,GAAGhB,MAAM,CAACiB,GAAG;EACvB,IAAIC,QAAQ,GAAoB,IAAI;EAEpC,IAAIR,KAAK,GAAGsF,QAAQ,EAAG;EACvB;EACA;EACA;EACA,IAAItF,KAAK,IAAI,IAAI,EAAE;IACjBA,KAAK,GAAG,CAAC;IACT+C,aAAa,CAACwC,YAAY,CAAAR,QAAA,CAAM,IAAAhC,aAAa,CAAC7C,KAAK;MAAE2E,GAAG,EAAE7E;IAAK,IAAI,EAAE,CAAC;EACvE;EAED,SAASsF,QAAQA,CAAA;IACf,IAAIpF,KAAK,GAAG6C,aAAa,CAAC7C,KAAK,IAAI;MAAE2E,GAAG,EAAE;KAAM;IAChD,OAAO3E,KAAK,CAAC2E,GAAG;EAClB;EAEA,SAASW,SAASA,CAAA;IAChBlF,MAAM,GAAGhB,MAAM,CAACiB,GAAG;IACnB,IAAIkC,SAAS,GAAG6C,QAAQ,EAAE;IAC1B,IAAIjD,KAAK,GAAGI,SAAS,IAAI,IAAI,GAAG,IAAI,GAAGA,SAAS,GAAGzC,KAAK;IACxDA,KAAK,GAAGyC,SAAS;IACjB,IAAIjC,QAAQ,EAAE;MACZA,QAAQ,CAAC;QAAEF,MAAM;QAAEU,QAAQ,EAAES,OAAO,CAACT,QAAQ;QAAEqB;MAAK,CAAE,CAAC;IACxD;EACH;EAEA,SAASJ,IAAIA,CAACnB,EAAM,EAAEZ,KAAW;IAC/BI,MAAM,GAAGhB,MAAM,CAAC4C,IAAI;IACpB,IAAIlB,QAAQ,GAAGC,cAAc,CAACQ,OAAO,CAACT,QAAQ,EAAEF,EAAE,EAAEZ,KAAK,CAAC;IAC1D,IAAIkF,gBAAgB,EAAEA,gBAAgB,CAACpE,QAAQ,EAAEF,EAAE,CAAC;IAEpDd,KAAK,GAAGsF,QAAQ,EAAE,GAAG,CAAC;IACtB,IAAIG,YAAY,GAAGb,eAAe,CAAC5D,QAAQ,EAAEhB,KAAK,CAAC;IACnD,IAAI4D,GAAG,GAAGnC,OAAO,CAACF,UAAU,CAACP,QAAQ,CAAC;IAEtC;IACA,IAAI;MACF+B,aAAa,CAAC2C,SAAS,CAACD,YAAY,EAAE,EAAE,EAAE7B,GAAG,CAAC;KAC/C,CAAC,OAAO+B,KAAK,EAAE;MACd;MACA;MACA;MACA;MACA,IAAIA,KAAK,YAAYC,YAAY,IAAID,KAAK,CAACE,IAAI,KAAK,gBAAgB,EAAE;QACpE,MAAMF,KAAK;MACZ;MACD;MACA;MACA7C,MAAM,CAAC9B,QAAQ,CAAC8E,MAAM,CAAClC,GAAG,CAAC;IAC5B;IAED,IAAIhE,QAAQ,IAAIY,QAAQ,EAAE;MACxBA,QAAQ,CAAC;QAAEF,MAAM;QAAEU,QAAQ,EAAES,OAAO,CAACT,QAAQ;QAAEqB,KAAK,EAAE;MAAC,CAAE,CAAC;IAC3D;EACH;EAEA,SAASC,OAAOA,CAACxB,EAAM,EAAEZ,KAAW;IAClCI,MAAM,GAAGhB,MAAM,CAACiD,OAAO;IACvB,IAAIvB,QAAQ,GAAGC,cAAc,CAACQ,OAAO,CAACT,QAAQ,EAAEF,EAAE,EAAEZ,KAAK,CAAC;IAC1D,IAAIkF,gBAAgB,EAAEA,gBAAgB,CAACpE,QAAQ,EAAEF,EAAE,CAAC;IAEpDd,KAAK,GAAGsF,QAAQ,EAAE;IAClB,IAAIG,YAAY,GAAGb,eAAe,CAAC5D,QAAQ,EAAEhB,KAAK,CAAC;IACnD,IAAI4D,GAAG,GAAGnC,OAAO,CAACF,UAAU,CAACP,QAAQ,CAAC;IACtC+B,aAAa,CAACwC,YAAY,CAACE,YAAY,EAAE,EAAE,EAAE7B,GAAG,CAAC;IAEjD,IAAIhE,QAAQ,IAAIY,QAAQ,EAAE;MACxBA,QAAQ,CAAC;QAAEF,MAAM;QAAEU,QAAQ,EAAES,OAAO,CAACT,QAAQ;QAAEqB,KAAK,EAAE;MAAC,CAAE,CAAC;IAC3D;EACH;EAEA,SAASX,SAASA,CAACZ,EAAM;IACvB;IACA;IACA;IACA,IAAIyC,IAAI,GACNT,MAAM,CAAC9B,QAAQ,CAAC+E,MAAM,KAAK,MAAM,GAC7BjD,MAAM,CAAC9B,QAAQ,CAAC+E,MAAM,GACtBjD,MAAM,CAAC9B,QAAQ,CAAC0C,IAAI;IAE1B,IAAIA,IAAI,GAAG,OAAO5C,EAAE,KAAK,QAAQ,GAAGA,EAAE,GAAGU,UAAU,CAACV,EAAE,CAAC;IACvDmD,SAAS,CACPV,IAAI,EACkE,wEAAAG,IAAM,CAC7E;IACD,OAAO,IAAI/B,GAAG,CAAC+B,IAAI,EAAEH,IAAI,CAAC;EAC5B;EAEA,IAAI9B,OAAO,GAAY;IACrB,IAAInB,MAAMA,CAAA;MACR,OAAOA,MAAM;KACd;IACD,IAAIU,QAAQA,CAAA;MACV,OAAOmE,WAAW,CAACrC,MAAM,EAAEC,aAAa,CAAC;KAC1C;IACDL,MAAMA,CAACC,EAAY;MACjB,IAAInC,QAAQ,EAAE;QACZ,MAAM,IAAI4D,KAAK,CAAC,4CAA4C,CAAC;MAC9D;MACDtB,MAAM,CAACkD,gBAAgB,CAACzG,iBAAiB,EAAEiG,SAAS,CAAC;MACrDhF,QAAQ,GAAGmC,EAAE;MAEb,OAAO,MAAK;QACVG,MAAM,CAACmD,mBAAmB,CAAC1G,iBAAiB,EAAEiG,SAAS,CAAC;QACxDhF,QAAQ,GAAG,IAAI;OAChB;KACF;IACDe,UAAUA,CAACT,EAAE;MACX,OAAOS,UAAU,CAACuB,MAAM,EAAEhC,EAAE,CAAC;KAC9B;IACDY,SAAS;IACTE,cAAcA,CAACd,EAAE;MACf;MACA,IAAI8C,GAAG,GAAGlC,SAAS,CAACZ,EAAE,CAAC;MACvB,OAAO;QACLI,QAAQ,EAAE0C,GAAG,CAAC1C,QAAQ;QACtBa,MAAM,EAAE6B,GAAG,CAAC7B,MAAM;QAClBC,IAAI,EAAE4B,GAAG,CAAC5B;OACX;KACF;IACDC,IAAI;IACJK,OAAO;IACPE,EAAEA,CAAC/B,CAAC;MACF,OAAOsC,aAAa,CAACP,EAAE,CAAC/B,CAAC,CAAC;IAC5B;GACD;EAED,OAAOgB,OAAO;AAChB;AAEA;;AC7sBA,IAAYyE,UAKX;AALD,WAAYA,UAAU;EACpBA,UAAA,iBAAa;EACbA,UAAA,yBAAqB;EACrBA,UAAA,yBAAqB;EACrBA,UAAA,mBAAe;AACjB,CAAC,EALWA,UAAU,KAAVA,UAAU,GAKrB;AAyNM,MAAMC,kBAAkB,GAAG,IAAIC,GAAG,CAAoB,CAC3D,MAAM,EACN,eAAe,EACf,MAAM,EACN,IAAI,EACJ,OAAO,EACP,UAAU,CACX,CAAC;AAoJF,SAASC,YAAYA,CACnBC,KAA0B;EAE1B,OAAOA,KAAK,CAACtG,KAAK,KAAK,IAAI;AAC7B;AAEA;AACA;AACM,SAAUuG,yBAAyBA,CACvCC,MAA6B,EAC7BC,kBAA8C,EAC9CC,UAAuB,EACvBC,QAAA,EAA4B;EAAA,IAD5BD,UAAuB;IAAvBA,UAAuB,KAAE;EAAA;EAAA,IACzBC,QAAA;IAAAA,QAAA,GAA0B,EAAE;EAAA;EAE5B,OAAOH,MAAM,CAAC1G,GAAG,CAAC,CAACwG,KAAK,EAAEtG,KAAK,KAAI;IACjC,IAAI4G,QAAQ,GAAG,CAAC,GAAGF,UAAU,EAAE1G,KAAK,CAAC;IACrC,IAAI6G,EAAE,GAAG,OAAOP,KAAK,CAACO,EAAE,KAAK,QAAQ,GAAGP,KAAK,CAACO,EAAE,GAAGD,QAAQ,CAACE,IAAI,CAAC,GAAG,CAAC;IACrE7C,SAAS,CACPqC,KAAK,CAACtG,KAAK,KAAK,IAAI,IAAI,CAACsG,KAAK,CAACS,QAAQ,6CACI,CAC5C;IACD9C,SAAS,CACP,CAAC0C,QAAQ,CAACE,EAAE,CAAC,EACb,qCAAqC,GAAAA,EAAE,GACrC,wEAAwD,CAC3D;IAED,IAAIR,YAAY,CAACC,KAAK,CAAC,EAAE;MACvB,IAAIU,UAAU,GAAAjC,QAAA,KACTuB,KAAK,EACLG,kBAAkB,CAACH,KAAK,CAAC;QAC5BO;OACD;MACDF,QAAQ,CAACE,EAAE,CAAC,GAAGG,UAAU;MACzB,OAAOA,UAAU;IAClB,OAAM;MACL,IAAIC,iBAAiB,GAAAlC,QAAA,KAChBuB,KAAK,EACLG,kBAAkB,CAACH,KAAK,CAAC;QAC5BO,EAAE;QACFE,QAAQ,EAAE5G;OACX;MACDwG,QAAQ,CAACE,EAAE,CAAC,GAAGI,iBAAiB;MAEhC,IAAIX,KAAK,CAACS,QAAQ,EAAE;QAClBE,iBAAiB,CAACF,QAAQ,GAAGR,yBAAyB,CACpDD,KAAK,CAACS,QAAQ,EACdN,kBAAkB,EAClBG,QAAQ,EACRD,QAAQ,CACT;MACF;MAED,OAAOM,iBAAiB;IACzB;EACH,CAAC,CAAC;AACJ;AAEA;;;;AAIG;AACG,SAAUC,WAAWA,CAGzBV,MAAyB,EACzBW,WAAuC,EACvCC,QAAQ,EAAM;EAAA,IAAdA,QAAQ;IAARA,QAAQ,GAAG,GAAG;EAAA;EAEd,IAAIpG,QAAQ,GACV,OAAOmG,WAAW,KAAK,QAAQ,GAAGrF,SAAS,CAACqF,WAAW,CAAC,GAAGA,WAAW;EAExE,IAAIjG,QAAQ,GAAGmG,aAAa,CAACrG,QAAQ,CAACE,QAAQ,IAAI,GAAG,EAAEkG,QAAQ,CAAC;EAEhE,IAAIlG,QAAQ,IAAI,IAAI,EAAE;IACpB,OAAO,IAAI;EACZ;EAED,IAAIoG,QAAQ,GAAGC,aAAa,CAACf,MAAM,CAAC;EACpCgB,iBAAiB,CAACF,QAAQ,CAAC;EAE3B,IAAIG,OAAO,GAAG,IAAI;EAClB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAED,OAAO,IAAI,IAAI,IAAIC,CAAC,GAAGJ,QAAQ,CAACjH,MAAM,EAAE,EAAEqH,CAAC,EAAE;IAC3DD,OAAO,GAAGE,gBAAgB,CACxBL,QAAQ,CAACI,CAAC,CAAC;IACX;IACA;IACA;IACA;IACA;IACA;IACAE,eAAe,CAAC1G,QAAQ,CAAC,CAC1B;EACF;EAED,OAAOuG,OAAO;AAChB;AAmBA,SAASF,aAAaA,CAGpBf,MAAyB,EACzBc,QAA2C,EAC3CO,WAAA,EACAnB,UAAU,EAAK;EAAA,IAFfY,QAA2C;IAA3CA,QAA2C,KAAE;EAAA;EAAA,IAC7CO,WAAA;IAAAA,WAAA,GAA4C,EAAE;EAAA;EAAA,IAC9CnB,UAAU;IAAVA,UAAU,GAAG,EAAE;EAAA;EAEf,IAAIoB,YAAY,GAAGA,CACjBxB,KAAsB,EACtBtG,KAAa,EACb+H,YAAqB,KACnB;IACF,IAAIC,IAAI,GAA+B;MACrCD,YAAY,EACVA,YAAY,KAAK5H,SAAS,GAAGmG,KAAK,CAACzE,IAAI,IAAI,EAAE,GAAGkG,YAAY;MAC9DE,aAAa,EAAE3B,KAAK,CAAC2B,aAAa,KAAK,IAAI;MAC3CC,aAAa,EAAElI,KAAK;MACpBsG;KACD;IAED,IAAI0B,IAAI,CAACD,YAAY,CAACI,UAAU,CAAC,GAAG,CAAC,EAAE;MACrClE,SAAS,CACP+D,IAAI,CAACD,YAAY,CAACI,UAAU,CAACzB,UAAU,CAAC,EACxC,2BAAwBsB,IAAI,CAACD,YAAY,qCACnCrB,UAAU,oDAA+C,gEACA,CAChE;MAEDsB,IAAI,CAACD,YAAY,GAAGC,IAAI,CAACD,YAAY,CAAChE,KAAK,CAAC2C,UAAU,CAACrG,MAAM,CAAC;IAC/D;IAED,IAAIwB,IAAI,GAAGuG,SAAS,CAAC,CAAC1B,UAAU,EAAEsB,IAAI,CAACD,YAAY,CAAC,CAAC;IACrD,IAAIM,UAAU,GAAGR,WAAW,CAACS,MAAM,CAACN,IAAI,CAAC;IAEzC;IACA;IACA;IACA,IAAI1B,KAAK,CAACS,QAAQ,IAAIT,KAAK,CAACS,QAAQ,CAAC1G,MAAM,GAAG,CAAC,EAAE;MAC/C4D,SAAS;MACP;MACA;MACAqC,KAAK,CAACtG,KAAK,KAAK,IAAI,EACpB,yDACuC,4CAAA6B,IAAI,SAAI,CAChD;MAED0F,aAAa,CAACjB,KAAK,CAACS,QAAQ,EAAEO,QAAQ,EAAEe,UAAU,EAAExG,IAAI,CAAC;IAC1D;IAED;IACA;IACA,IAAIyE,KAAK,CAACzE,IAAI,IAAI,IAAI,IAAI,CAACyE,KAAK,CAACtG,KAAK,EAAE;MACtC;IACD;IAEDsH,QAAQ,CAACrF,IAAI,CAAC;MACZJ,IAAI;MACJ0G,KAAK,EAAEC,YAAY,CAAC3G,IAAI,EAAEyE,KAAK,CAACtG,KAAK,CAAC;MACtCqI;IACD,EAAC;GACH;EACD7B,MAAM,CAACiC,OAAO,CAAC,CAACnC,KAAK,EAAEtG,KAAK,KAAI;IAAA,IAAA0I,WAAA;IAC9B;IACA,IAAIpC,KAAK,CAACzE,IAAI,KAAK,EAAE,IAAI,GAAA6G,WAAA,GAACpC,KAAK,CAACzE,IAAI,aAAV6G,WAAA,CAAYC,QAAQ,CAAC,GAAG,CAAC,CAAE;MACnDb,YAAY,CAACxB,KAAK,EAAEtG,KAAK,CAAC;IAC3B,OAAM;MACL,KAAK,IAAI4I,QAAQ,IAAIC,uBAAuB,CAACvC,KAAK,CAACzE,IAAI,CAAC,EAAE;QACxDiG,YAAY,CAACxB,KAAK,EAAEtG,KAAK,EAAE4I,QAAQ,CAAC;MACrC;IACF;EACH,CAAC,CAAC;EAEF,OAAOtB,QAAQ;AACjB;AAEA;;;;;;;;;;;;;AAaG;AACH,SAASuB,uBAAuBA,CAAChH,IAAY;EAC3C,IAAIiH,QAAQ,GAAGjH,IAAI,CAACkH,KAAK,CAAC,GAAG,CAAC;EAC9B,IAAID,QAAQ,CAACzI,MAAM,KAAK,CAAC,EAAE,OAAO,EAAE;EAEpC,IAAI,CAAC2I,KAAK,EAAE,GAAGC,IAAI,CAAC,GAAGH,QAAQ;EAE/B;EACA,IAAII,UAAU,GAAGF,KAAK,CAACG,QAAQ,CAAC,GAAG,CAAC;EACpC;EACA,IAAIC,QAAQ,GAAGJ,KAAK,CAAC1G,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;EAEvC,IAAI2G,IAAI,CAAC5I,MAAM,KAAK,CAAC,EAAE;IACrB;IACA;IACA,OAAO6I,UAAU,GAAG,CAACE,QAAQ,EAAE,EAAE,CAAC,GAAG,CAACA,QAAQ,CAAC;EAChD;EAED,IAAIC,YAAY,GAAGR,uBAAuB,CAACI,IAAI,CAACnC,IAAI,CAAC,GAAG,CAAC,CAAC;EAE1D,IAAIwC,MAAM,GAAa,EAAE;EAEzB;EACA;EACA;EACA;EACA;EACA;EACA;EACAA,MAAM,CAACrH,IAAI,CACT,GAAGoH,YAAY,CAACvJ,GAAG,CAAEyJ,OAAO,IAC1BA,OAAO,KAAK,EAAE,GAAGH,QAAQ,GAAG,CAACA,QAAQ,EAAEG,OAAO,CAAC,CAACzC,IAAI,CAAC,GAAG,CAAC,CAC1D,CACF;EAED;EACA,IAAIoC,UAAU,EAAE;IACdI,MAAM,CAACrH,IAAI,CAAC,GAAGoH,YAAY,CAAC;EAC7B;EAED;EACA,OAAOC,MAAM,CAACxJ,GAAG,CAAE8I,QAAQ,IACzB/G,IAAI,CAACsG,UAAU,CAAC,GAAG,CAAC,IAAIS,QAAQ,KAAK,EAAE,GAAG,GAAG,GAAGA,QAAQ,CACzD;AACH;AAEA,SAASpB,iBAAiBA,CAACF,QAAuB;EAChDA,QAAQ,CAACkC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KACjBD,CAAC,CAAClB,KAAK,KAAKmB,CAAC,CAACnB,KAAK,GACfmB,CAAC,CAACnB,KAAK,GAAGkB,CAAC,CAAClB,KAAK;EAAA,EACjBoB,cAAc,CACZF,CAAC,CAACpB,UAAU,CAACvI,GAAG,CAAEkI,IAAI,IAAKA,IAAI,CAACE,aAAa,CAAC,EAC9CwB,CAAC,CAACrB,UAAU,CAACvI,GAAG,CAAEkI,IAAI,IAAKA,IAAI,CAACE,aAAa,CAAC,CAC/C,CACN;AACH;AAEA,MAAM0B,OAAO,GAAG,QAAQ;AACxB,MAAMC,mBAAmB,GAAG,CAAC;AAC7B,MAAMC,eAAe,GAAG,CAAC;AACzB,MAAMC,iBAAiB,GAAG,CAAC;AAC3B,MAAMC,kBAAkB,GAAG,EAAE;AAC7B,MAAMC,YAAY,GAAG,CAAC,CAAC;AACvB,MAAMC,OAAO,GAAIC,CAAS,IAAKA,CAAC,KAAK,GAAG;AAExC,SAAS3B,YAAYA,CAAC3G,IAAY,EAAE7B,KAA0B;EAC5D,IAAI8I,QAAQ,GAAGjH,IAAI,CAACkH,KAAK,CAAC,GAAG,CAAC;EAC9B,IAAIqB,YAAY,GAAGtB,QAAQ,CAACzI,MAAM;EAClC,IAAIyI,QAAQ,CAACuB,IAAI,CAACH,OAAO,CAAC,EAAE;IAC1BE,YAAY,IAAIH,YAAY;EAC7B;EAED,IAAIjK,KAAK,EAAE;IACToK,YAAY,IAAIN,eAAe;EAChC;EAED,OAAOhB,QAAQ,CACZwB,MAAM,CAAEH,CAAC,IAAK,CAACD,OAAO,CAACC,CAAC,CAAC,CAAC,CAC1BI,MAAM,CACL,CAAChC,KAAK,EAAEiC,OAAO,KACbjC,KAAK,IACJqB,OAAO,CAACa,IAAI,CAACD,OAAO,CAAC,GAClBX,mBAAmB,GACnBW,OAAO,KAAK,EAAE,GACdT,iBAAiB,GACjBC,kBAAkB,CAAC,EACzBI,YAAY,CACb;AACL;AAEA,SAAST,cAAcA,CAACF,CAAW,EAAEC,CAAW;EAC9C,IAAIgB,QAAQ,GACVjB,CAAC,CAACpJ,MAAM,KAAKqJ,CAAC,CAACrJ,MAAM,IAAIoJ,CAAC,CAAC1F,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC4G,KAAK,CAAC,CAAClK,CAAC,EAAEiH,CAAC,KAAKjH,CAAC,KAAKiJ,CAAC,CAAChC,CAAC,CAAC,CAAC;EAErE,OAAOgD,QAAQ;EACX;EACA;EACA;EACA;EACAjB,CAAC,CAACA,CAAC,CAACpJ,MAAM,GAAG,CAAC,CAAC,GAAGqJ,CAAC,CAACA,CAAC,CAACrJ,MAAM,GAAG,CAAC,CAAC;EACjC;EACA;EACA,CAAC;AACP;AAEA,SAASsH,gBAAgBA,CAIvBiD,MAAoC,EACpC1J,QAAgB;EAEhB,IAAI;IAAEmH;EAAY,IAAGuC,MAAM;EAE3B,IAAIC,aAAa,GAAG,EAAE;EACtB,IAAIC,eAAe,GAAG,GAAG;EACzB,IAAIrD,OAAO,GAAoD,EAAE;EACjE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGW,UAAU,CAAChI,MAAM,EAAE,EAAEqH,CAAC,EAAE;IAC1C,IAAIM,IAAI,GAAGK,UAAU,CAACX,CAAC,CAAC;IACxB,IAAIqD,GAAG,GAAGrD,CAAC,KAAKW,UAAU,CAAChI,MAAM,GAAG,CAAC;IACrC,IAAI2K,iBAAiB,GACnBF,eAAe,KAAK,GAAG,GACnB5J,QAAQ,GACRA,QAAQ,CAAC6C,KAAK,CAAC+G,eAAe,CAACzK,MAAM,CAAC,IAAI,GAAG;IACnD,IAAI4K,KAAK,GAAGC,SAAS,CACnB;MAAErJ,IAAI,EAAEmG,IAAI,CAACD,YAAY;MAAEE,aAAa,EAAED,IAAI,CAACC,aAAa;MAAE8C;KAAK,EACnEC,iBAAiB,CAClB;IAED,IAAI,CAACC,KAAK,EAAE,OAAO,IAAI;IAEvBE,MAAM,CAACrF,MAAM,CAAC+E,aAAa,EAAEI,KAAK,CAACG,MAAM,CAAC;IAE1C,IAAI9E,KAAK,GAAG0B,IAAI,CAAC1B,KAAK;IAEtBmB,OAAO,CAACxF,IAAI,CAAC;MACX;MACAmJ,MAAM,EAAEP,aAAiC;MACzC3J,QAAQ,EAAEkH,SAAS,CAAC,CAAC0C,eAAe,EAAEG,KAAK,CAAC/J,QAAQ,CAAC,CAAC;MACtDmK,YAAY,EAAEC,iBAAiB,CAC7BlD,SAAS,CAAC,CAAC0C,eAAe,EAAEG,KAAK,CAACI,YAAY,CAAC,CAAC,CACjD;MACD/E;IACD,EAAC;IAEF,IAAI2E,KAAK,CAACI,YAAY,KAAK,GAAG,EAAE;MAC9BP,eAAe,GAAG1C,SAAS,CAAC,CAAC0C,eAAe,EAAEG,KAAK,CAACI,YAAY,CAAC,CAAC;IACnE;EACF;EAED,OAAO5D,OAAO;AAChB;AAEA;;;;AAIG;SACa8D,YAAYA,CAC1BC,YAAkB,EAClBJ,MAAA,EAEa;EAAA,IAFbA,MAAA;IAAAA,MAAA,GAEI,EAAS;EAAA;EAEb,IAAIvJ,IAAI,GAAW2J,YAAY;EAC/B,IAAI3J,IAAI,CAACsH,QAAQ,CAAC,GAAG,CAAC,IAAItH,IAAI,KAAK,GAAG,IAAI,CAACA,IAAI,CAACsH,QAAQ,CAAC,IAAI,CAAC,EAAE;IAC9DhI,OAAO,CACL,KAAK,EACL,eAAe,GAAAU,IAAI,GACb,8CAAAA,IAAI,CAACS,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC,GAAqC,0GACE,IAChC,uCAAAT,IAAI,CAACS,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC,SAAI,CACpE;IACDT,IAAI,GAAGA,IAAI,CAACS,OAAO,CAAC,KAAK,EAAE,IAAI,CAAS;EACzC;EAED;EACA,MAAMmJ,MAAM,GAAG5J,IAAI,CAACsG,UAAU,CAAC,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE;EAE9C,MAAM7G,SAAS,GAAIoK,CAAM,IACvBA,CAAC,IAAI,IAAI,GAAG,EAAE,GAAG,OAAOA,CAAC,KAAK,QAAQ,GAAGA,CAAC,GAAGC,MAAM,CAACD,CAAC,CAAC;EAExD,MAAM5C,QAAQ,GAAGjH,IAAI,CAClBkH,KAAK,CAAC,KAAK,CAAC,CACZjJ,GAAG,CAAC,CAAC0K,OAAO,EAAExK,KAAK,EAAE4L,KAAK,KAAI;IAC7B,MAAMC,aAAa,GAAG7L,KAAK,KAAK4L,KAAK,CAACvL,MAAM,GAAG,CAAC;IAEhD;IACA,IAAIwL,aAAa,IAAIrB,OAAO,KAAK,GAAG,EAAE;MACpC,MAAMsB,IAAI,GAAG,GAAsB;MACnC;MACA,OAAOxK,SAAS,CAAC8J,MAAM,CAACU,IAAI,CAAC,CAAC;IAC/B;IAED,MAAMC,QAAQ,GAAGvB,OAAO,CAACS,KAAK,CAAC,eAAe,CAAC;IAC/C,IAAIc,QAAQ,EAAE;MACZ,MAAM,GAAGhL,GAAG,EAAEiL,QAAQ,CAAC,GAAGD,QAAQ;MAClC,IAAIE,KAAK,GAAGb,MAAM,CAACrK,GAAsB,CAAC;MAC1CkD,SAAS,CAAC+H,QAAQ,KAAK,GAAG,IAAIC,KAAK,IAAI,IAAI,kBAAelL,GAAG,aAAS,CAAC;MACvE,OAAOO,SAAS,CAAC2K,KAAK,CAAC;IACxB;IAED;IACA,OAAOzB,OAAO,CAAClI,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC;GACnC;EACD;EAAA,CACCgI,MAAM,CAAEE,OAAO,IAAK,CAAC,CAACA,OAAO,CAAC;EAEjC,OAAOiB,MAAM,GAAG3C,QAAQ,CAAChC,IAAI,CAAC,GAAG,CAAC;AACpC;AAiDA;;;;;AAKG;AACa,SAAAoE,SAASA,CAIvBgB,OAAiC,EACjChL,QAAgB;EAEhB,IAAI,OAAOgL,OAAO,KAAK,QAAQ,EAAE;IAC/BA,OAAO,GAAG;MAAErK,IAAI,EAAEqK,OAAO;MAAEjE,aAAa,EAAE,KAAK;MAAE8C,GAAG,EAAE;KAAM;EAC7D;EAED,IAAI,CAACoB,OAAO,EAAEC,UAAU,CAAC,GAAGC,WAAW,CACrCH,OAAO,CAACrK,IAAI,EACZqK,OAAO,CAACjE,aAAa,EACrBiE,OAAO,CAACnB,GAAG,CACZ;EAED,IAAIE,KAAK,GAAG/J,QAAQ,CAAC+J,KAAK,CAACkB,OAAO,CAAC;EACnC,IAAI,CAAClB,KAAK,EAAE,OAAO,IAAI;EAEvB,IAAIH,eAAe,GAAGG,KAAK,CAAC,CAAC,CAAC;EAC9B,IAAII,YAAY,GAAGP,eAAe,CAACxI,OAAO,CAAC,SAAS,EAAE,IAAI,CAAC;EAC3D,IAAIgK,aAAa,GAAGrB,KAAK,CAAClH,KAAK,CAAC,CAAC,CAAC;EAClC,IAAIqH,MAAM,GAAWgB,UAAU,CAAC7B,MAAM,CACpC,CAACgC,IAAI,EAAEC,SAAS,EAAExM,KAAK,KAAI;IACzB;IACA;IACA,IAAIwM,SAAS,KAAK,GAAG,EAAE;MACrB,IAAIC,UAAU,GAAGH,aAAa,CAACtM,KAAK,CAAC,IAAI,EAAE;MAC3CqL,YAAY,GAAGP,eAAe,CAC3B/G,KAAK,CAAC,CAAC,EAAE+G,eAAe,CAACzK,MAAM,GAAGoM,UAAU,CAACpM,MAAM,CAAC,CACpDiC,OAAO,CAAC,SAAS,EAAE,IAAI,CAAC;IAC5B;IAEDiK,IAAI,CAACC,SAAS,CAAC,GAAGE,wBAAwB,CACxCJ,aAAa,CAACtM,KAAK,CAAC,IAAI,EAAE,EAC1BwM,SAAS,CACV;IACD,OAAOD,IAAI;GACZ,EACD,EAAE,CACH;EAED,OAAO;IACLnB,MAAM;IACNlK,QAAQ,EAAE4J,eAAe;IACzBO,YAAY;IACZa;GACD;AACH;AAEA,SAASG,WAAWA,CAClBxK,IAAY,EACZoG,aAAa,EACb8C,GAAG,EAAO;EAAA,IADV9C,aAAa;IAAbA,aAAa,GAAG,KAAK;EAAA;EAAA,IACrB8C,GAAG;IAAHA,GAAG,GAAG,IAAI;EAAA;EAEV5J,OAAO,CACLU,IAAI,KAAK,GAAG,IAAI,CAACA,IAAI,CAACsH,QAAQ,CAAC,GAAG,CAAC,IAAItH,IAAI,CAACsH,QAAQ,CAAC,IAAI,CAAC,EAC1D,kBAAetH,IAAI,GACb,8CAAAA,IAAI,CAACS,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC,GAAqC,0GACE,2CAChCT,IAAI,CAACS,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC,SAAI,CACpE;EAED,IAAI8J,UAAU,GAAa,EAAE;EAC7B,IAAIO,YAAY,GACd,GAAG,GACH9K,IAAI,CACDS,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC;EAAA,CACtBA,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC;EAAA,CACpBA,OAAO,CAAC,qBAAqB,EAAE,MAAM,CAAC;EAAA,CACtCA,OAAO,CAAC,WAAW,EAAE,CAACsK,CAAS,EAAEJ,SAAiB,KAAI;IACrDJ,UAAU,CAACnK,IAAI,CAACuK,SAAS,CAAC;IAC1B,OAAO,YAAY;EACrB,CAAC,CAAC;EAEN,IAAI3K,IAAI,CAACsH,QAAQ,CAAC,GAAG,CAAC,EAAE;IACtBiD,UAAU,CAACnK,IAAI,CAAC,GAAG,CAAC;IACpB0K,YAAY,IACV9K,IAAI,KAAK,GAAG,IAAIA,IAAI,KAAK,IAAI,GACzB,OAAO;IAAA,EACP,mBAAmB,CAAC;GAC3B,MAAM,IAAIkJ,GAAG,EAAE;IACd;IACA4B,YAAY,IAAI,OAAO;GACxB,MAAM,IAAI9K,IAAI,KAAK,EAAE,IAAIA,IAAI,KAAK,GAAG,EAAE;IACtC;IACA;IACA;IACA;IACA;IACA;IACA;IACA8K,YAAY,IAAI,eAAe;EAChC,OAAM;EAIP,IAAIR,OAAO,GAAG,IAAIU,MAAM,CAACF,YAAY,EAAE1E,aAAa,GAAG9H,SAAS,GAAG,GAAG,CAAC;EAEvE,OAAO,CAACgM,OAAO,EAAEC,UAAU,CAAC;AAC9B;AAEA,SAASxE,eAAeA,CAAC1D,KAAa;EACpC,IAAI;IACF,OAAO4I,SAAS,CAAC5I,KAAK,CAAC;GACxB,CAAC,OAAOyB,KAAK,EAAE;IACdxE,OAAO,CACL,KAAK,EACL,oBAAiB+C,KAAK,GAC2C,kIAClDyB,KAAK,QAAI,CACzB;IAED,OAAOzB,KAAK;EACb;AACH;AAEA,SAASwI,wBAAwBA,CAACxI,KAAa,EAAEsI,SAAiB;EAChE,IAAI;IACF,OAAOO,kBAAkB,CAAC7I,KAAK,CAAC;GACjC,CAAC,OAAOyB,KAAK,EAAE;IACdxE,OAAO,CACL,KAAK,EACL,gCAAgC,GAAAqL,SAAS,GACvB,uDAAAtI,KAAK,GAAgD,2FAClCyB,KAAK,QAAI,CAC/C;IAED,OAAOzB,KAAK;EACb;AACH;AAEA;;AAEG;AACa,SAAAmD,aAAaA,CAC3BnG,QAAgB,EAChBkG,QAAgB;EAEhB,IAAIA,QAAQ,KAAK,GAAG,EAAE,OAAOlG,QAAQ;EAErC,IAAI,CAACA,QAAQ,CAAC8L,WAAW,EAAE,CAAC7E,UAAU,CAACf,QAAQ,CAAC4F,WAAW,EAAE,CAAC,EAAE;IAC9D,OAAO,IAAI;EACZ;EAED;EACA;EACA,IAAIC,UAAU,GAAG7F,QAAQ,CAAC+B,QAAQ,CAAC,GAAG,CAAC,GACnC/B,QAAQ,CAAC/G,MAAM,GAAG,CAAC,GACnB+G,QAAQ,CAAC/G,MAAM;EACnB,IAAI6M,QAAQ,GAAGhM,QAAQ,CAACE,MAAM,CAAC6L,UAAU,CAAC;EAC1C,IAAIC,QAAQ,IAAIA,QAAQ,KAAK,GAAG,EAAE;IAChC;IACA,OAAO,IAAI;EACZ;EAED,OAAOhM,QAAQ,CAAC6C,KAAK,CAACkJ,UAAU,CAAC,IAAI,GAAG;AAC1C;AAEA;;;;AAIG;SACaE,WAAWA,CAACrM,EAAM,EAAEsM,YAAY,EAAM;EAAA,IAAlBA,YAAY;IAAZA,YAAY,GAAG,GAAG;EAAA;EACpD,IAAI;IACFlM,QAAQ,EAAEmM,UAAU;IACpBtL,MAAM,GAAG,EAAE;IACXC,IAAI,GAAG;GACR,GAAG,OAAOlB,EAAE,KAAK,QAAQ,GAAGgB,SAAS,CAAChB,EAAE,CAAC,GAAGA,EAAE;EAE/C,IAAII,QAAQ,GAAGmM,UAAU,GACrBA,UAAU,CAAClF,UAAU,CAAC,GAAG,CAAC,GACxBkF,UAAU,GACVC,eAAe,CAACD,UAAU,EAAED,YAAY,CAAC,GAC3CA,YAAY;EAEhB,OAAO;IACLlM,QAAQ;IACRa,MAAM,EAAEwL,eAAe,CAACxL,MAAM,CAAC;IAC/BC,IAAI,EAAEwL,aAAa,CAACxL,IAAI;GACzB;AACH;AAEA,SAASsL,eAAeA,CAACvF,YAAoB,EAAEqF,YAAoB;EACjE,IAAItE,QAAQ,GAAGsE,YAAY,CAAC9K,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,CAACyG,KAAK,CAAC,GAAG,CAAC;EAC1D,IAAI0E,gBAAgB,GAAG1F,YAAY,CAACgB,KAAK,CAAC,GAAG,CAAC;EAE9C0E,gBAAgB,CAAChF,OAAO,CAAE+B,OAAO,IAAI;IACnC,IAAIA,OAAO,KAAK,IAAI,EAAE;MACpB;MACA,IAAI1B,QAAQ,CAACzI,MAAM,GAAG,CAAC,EAAEyI,QAAQ,CAAC4E,GAAG,EAAE;IACxC,OAAM,IAAIlD,OAAO,KAAK,GAAG,EAAE;MAC1B1B,QAAQ,CAAC7G,IAAI,CAACuI,OAAO,CAAC;IACvB;EACH,CAAC,CAAC;EAEF,OAAO1B,QAAQ,CAACzI,MAAM,GAAG,CAAC,GAAGyI,QAAQ,CAAChC,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG;AACvD;AAEA,SAAS6G,mBAAmBA,CAC1BC,IAAY,EACZC,KAAa,EACbC,IAAY,EACZjM,IAAmB;EAEnB,OACE,oBAAqB,GAAA+L,IAAI,GACjB,mDAAAC,KAAK,iBAAaxM,IAAI,CAACC,SAAS,CACtCO,IAAI,CACL,wCAAoC,IAC7B,SAAAiM,IAAI,8DAA2D,GACJ;AAEvE;AAEA;;;;;;;;;;;;;;;;;;;;;;AAsBG;AACG,SAAUC,0BAA0BA,CAExCtG,OAAY;EACZ,OAAOA,OAAO,CAAC6C,MAAM,CACnB,CAACW,KAAK,EAAEjL,KAAK,KACXA,KAAK,KAAK,CAAC,IAAKiL,KAAK,CAAC3E,KAAK,CAACzE,IAAI,IAAIoJ,KAAK,CAAC3E,KAAK,CAACzE,IAAI,CAACxB,MAAM,GAAG,CAAE,CACnE;AACH;AAEA;;AAEG;AACG,SAAU2N,SAASA,CACvBC,KAAS,EACTC,cAAwB,EACxBC,gBAAwB,EACxBC,cAAc,EAAQ;EAAA,IAAtBA,cAAc;IAAdA,cAAc,GAAG,KAAK;EAAA;EAEtB,IAAItN,EAAiB;EACrB,IAAI,OAAOmN,KAAK,KAAK,QAAQ,EAAE;IAC7BnN,EAAE,GAAGgB,SAAS,CAACmM,KAAK,CAAC;EACtB,OAAM;IACLnN,EAAE,GAAAiE,QAAA,CAAQ,IAAAkJ,KAAK,CAAE;IAEjBhK,SAAS,CACP,CAACnD,EAAE,CAACI,QAAQ,IAAI,CAACJ,EAAE,CAACI,QAAQ,CAACyH,QAAQ,CAAC,GAAG,CAAC,EAC1CgF,mBAAmB,CAAC,GAAG,EAAE,UAAU,EAAE,QAAQ,EAAE7M,EAAE,CAAC,CACnD;IACDmD,SAAS,CACP,CAACnD,EAAE,CAACI,QAAQ,IAAI,CAACJ,EAAE,CAACI,QAAQ,CAACyH,QAAQ,CAAC,GAAG,CAAC,EAC1CgF,mBAAmB,CAAC,GAAG,EAAE,UAAU,EAAE,MAAM,EAAE7M,EAAE,CAAC,CACjD;IACDmD,SAAS,CACP,CAACnD,EAAE,CAACiB,MAAM,IAAI,CAACjB,EAAE,CAACiB,MAAM,CAAC4G,QAAQ,CAAC,GAAG,CAAC,EACtCgF,mBAAmB,CAAC,GAAG,EAAE,QAAQ,EAAE,MAAM,EAAE7M,EAAE,CAAC,CAC/C;EACF;EAED,IAAIuN,WAAW,GAAGJ,KAAK,KAAK,EAAE,IAAInN,EAAE,CAACI,QAAQ,KAAK,EAAE;EACpD,IAAImM,UAAU,GAAGgB,WAAW,GAAG,GAAG,GAAGvN,EAAE,CAACI,QAAQ;EAEhD,IAAIoN,IAAY;EAEhB;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,IAAIF,cAAc,IAAIf,UAAU,IAAI,IAAI,EAAE;IACxCiB,IAAI,GAAGH,gBAAgB;EACxB,OAAM;IACL,IAAII,kBAAkB,GAAGL,cAAc,CAAC7N,MAAM,GAAG,CAAC;IAElD,IAAIgN,UAAU,CAAClF,UAAU,CAAC,IAAI,CAAC,EAAE;MAC/B,IAAIqG,UAAU,GAAGnB,UAAU,CAACtE,KAAK,CAAC,GAAG,CAAC;MAEtC;MACA;MACA;MACA,OAAOyF,UAAU,CAAC,CAAC,CAAC,KAAK,IAAI,EAAE;QAC7BA,UAAU,CAACC,KAAK,EAAE;QAClBF,kBAAkB,IAAI,CAAC;MACxB;MAEDzN,EAAE,CAACI,QAAQ,GAAGsN,UAAU,CAAC1H,IAAI,CAAC,GAAG,CAAC;IACnC;IAED;IACA;IACAwH,IAAI,GAAGC,kBAAkB,IAAI,CAAC,GAAGL,cAAc,CAACK,kBAAkB,CAAC,GAAG,GAAG;EAC1E;EAED,IAAI1M,IAAI,GAAGsL,WAAW,CAACrM,EAAE,EAAEwN,IAAI,CAAC;EAEhC;EACA,IAAII,wBAAwB,GAC1BrB,UAAU,IAAIA,UAAU,KAAK,GAAG,IAAIA,UAAU,CAAClE,QAAQ,CAAC,GAAG,CAAC;EAC9D;EACA,IAAIwF,uBAAuB,GACzB,CAACN,WAAW,IAAIhB,UAAU,KAAK,GAAG,KAAKc,gBAAgB,CAAChF,QAAQ,CAAC,GAAG,CAAC;EACvE,IACE,CAACtH,IAAI,CAACX,QAAQ,CAACiI,QAAQ,CAAC,GAAG,CAAC,KAC3BuF,wBAAwB,IAAIC,uBAAuB,CAAC,EACrD;IACA9M,IAAI,CAACX,QAAQ,IAAI,GAAG;EACrB;EAED,OAAOW,IAAI;AACb;AAEA;;AAEG;AACG,SAAU+M,aAAaA,CAAC9N,EAAM;EAClC;EACA,OAAOA,EAAE,KAAK,EAAE,IAAKA,EAAW,CAACI,QAAQ,KAAK,EAAE,GAC5C,GAAG,GACH,OAAOJ,EAAE,KAAK,QAAQ,GACtBgB,SAAS,CAAChB,EAAE,CAAC,CAACI,QAAQ,GACtBJ,EAAE,CAACI,QAAQ;AACjB;AAEA;;AAEG;MACUkH,SAAS,GAAIyG,KAAe,IACvCA,KAAK,CAAC/H,IAAI,CAAC,GAAG,CAAC,CAACxE,OAAO,CAAC,QAAQ,EAAE,GAAG;AAEvC;;AAEG;MACUgJ,iBAAiB,GAAIpK,QAAgB,IAChDA,QAAQ,CAACoB,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,CAACA,OAAO,CAAC,MAAM,EAAE,GAAG;AAElD;;AAEG;AACI,MAAMiL,eAAe,GAAIxL,MAAc,IAC5C,CAACA,MAAM,IAAIA,MAAM,KAAK,GAAG,GACrB,EAAE,GACFA,MAAM,CAACoG,UAAU,CAAC,GAAG,CAAC,GACtBpG,MAAM,GACN,GAAG,GAAGA,MAAM;AAElB;;AAEG;AACI,MAAMyL,aAAa,GAAIxL,IAAY,IACxC,CAACA,IAAI,IAAIA,IAAI,KAAK,GAAG,GAAG,EAAE,GAAGA,IAAI,CAACmG,UAAU,CAAC,GAAG,CAAC,GAAGnG,IAAI,GAAG,GAAG,GAAGA,IAAI;AAOvE;;;AAGG;AACI,MAAM8M,IAAI,GAAiB,SAArBA,IAAIA,CAAkBC,IAAI,EAAEC,IAAI,EAAS;EAAA,IAAbA,IAAI;IAAJA,IAAI,GAAG,EAAE;EAAA;EAChD,IAAIC,YAAY,GAAG,OAAOD,IAAI,KAAK,QAAQ,GAAG;IAAEE,MAAM,EAAEF;EAAI,CAAE,GAAGA,IAAI;EAErE,IAAIG,OAAO,GAAG,IAAIC,OAAO,CAACH,YAAY,CAACE,OAAO,CAAC;EAC/C,IAAI,CAACA,OAAO,CAACE,GAAG,CAAC,cAAc,CAAC,EAAE;IAChCF,OAAO,CAACG,GAAG,CAAC,cAAc,EAAE,iCAAiC,CAAC;EAC/D;EAED,OAAO,IAAIC,QAAQ,CAAClO,IAAI,CAACC,SAAS,CAACyN,IAAI,CAAC,EAAAhK,QAAA,KACnCkK,YAAY;IACfE;EAAO,EACR,CAAC;AACJ;AAQM,MAAOK,oBAAqB,SAAQpL,KAAK;MAElCqL,YAAY;EAWvBC,WAAYA,CAAAX,IAA6B,EAAEE,YAA2B;IAV9D,KAAAU,cAAc,GAAgB,IAAIvJ,GAAG,EAAU;IAI/C,KAAAwJ,WAAW,GACjB,IAAIxJ,GAAG,EAAE;IAGX,IAAY,CAAAyJ,YAAA,GAAa,EAAE;IAGzB5L,SAAS,CACP8K,IAAI,IAAI,OAAOA,IAAI,KAAK,QAAQ,IAAI,CAACe,KAAK,CAACC,OAAO,CAAChB,IAAI,CAAC,EACxD,oCAAoC,CACrC;IAED;IACA;IACA,IAAIiB,MAAyC;IAC7C,IAAI,CAACC,YAAY,GAAG,IAAIC,OAAO,CAAC,CAACtD,CAAC,EAAEuD,CAAC,KAAMH,MAAM,GAAGG,CAAE,CAAC;IACvD,IAAI,CAACC,UAAU,GAAG,IAAIC,eAAe,EAAE;IACvC,IAAIC,OAAO,GAAGA,CAAA,KACZN,MAAM,CAAC,IAAIR,oBAAoB,CAAC,uBAAuB,CAAC,CAAC;IAC3D,IAAI,CAACe,mBAAmB,GAAG,MACzB,IAAI,CAACH,UAAU,CAACI,MAAM,CAACvK,mBAAmB,CAAC,OAAO,EAAEqK,OAAO,CAAC;IAC9D,IAAI,CAACF,UAAU,CAACI,MAAM,CAACxK,gBAAgB,CAAC,OAAO,EAAEsK,OAAO,CAAC;IAEzD,IAAI,CAACvB,IAAI,GAAG5D,MAAM,CAACtL,OAAO,CAACkP,IAAI,CAAC,CAACxE,MAAM,CACrC,CAACkG,GAAG,EAAAzL,IAAA;MAAA,IAAE,CAACjE,GAAG,EAAEmD,KAAK,CAAC,GAAAc,IAAA;MAAA,OAChBmG,MAAM,CAACrF,MAAM,CAAC2K,GAAG,EAAE;QACjB,CAAC1P,GAAG,GAAG,IAAI,CAAC2P,YAAY,CAAC3P,GAAG,EAAEmD,KAAK;OACpC,CAAC;KACJ,IAAE,CACH;IAED,IAAI,IAAI,CAACyM,IAAI,EAAE;MACb;MACA,IAAI,CAACJ,mBAAmB,EAAE;IAC3B;IAED,IAAI,CAACvB,IAAI,GAAGC,YAAY;EAC1B;EAEQyB,YAAYA,CAClB3P,GAAW,EACXmD,KAAiC;IAEjC,IAAI,EAAEA,KAAK,YAAYgM,OAAO,CAAC,EAAE;MAC/B,OAAOhM,KAAK;IACb;IAED,IAAI,CAAC2L,YAAY,CAAC5N,IAAI,CAAClB,GAAG,CAAC;IAC3B,IAAI,CAAC4O,cAAc,CAACiB,GAAG,CAAC7P,GAAG,CAAC;IAE5B;IACA;IACA,IAAI8P,OAAO,GAAmBX,OAAO,CAACY,IAAI,CAAC,CAAC5M,KAAK,EAAE,IAAI,CAAC+L,YAAY,CAAC,CAAC,CAACc,IAAI,CACxEhC,IAAI,IAAK,IAAI,CAACiC,QAAQ,CAACH,OAAO,EAAE9P,GAAG,EAAEZ,SAAS,EAAE4O,IAAe,CAAC,EAChEpJ,KAAK,IAAK,IAAI,CAACqL,QAAQ,CAACH,OAAO,EAAE9P,GAAG,EAAE4E,KAAgB,CAAC,CACzD;IAED;IACA;IACAkL,OAAO,CAACI,KAAK,CAAC,MAAO,EAAC,CAAC;IAEvB9F,MAAM,CAAC+F,cAAc,CAACL,OAAO,EAAE,UAAU,EAAE;MAAEM,GAAG,EAAEA,CAAA,KAAM;IAAI,CAAE,CAAC;IAC/D,OAAON,OAAO;EAChB;EAEQG,QAAQA,CACdH,OAAuB,EACvB9P,GAAW,EACX4E,KAAc,EACdoJ,IAAc;IAEd,IACE,IAAI,CAACqB,UAAU,CAACI,MAAM,CAACY,OAAO,IAC9BzL,KAAK,YAAY6J,oBAAoB,EACrC;MACA,IAAI,CAACe,mBAAmB,EAAE;MAC1BpF,MAAM,CAAC+F,cAAc,CAACL,OAAO,EAAE,QAAQ,EAAE;QAAEM,GAAG,EAAEA,CAAA,KAAMxL;MAAK,CAAE,CAAC;MAC9D,OAAOuK,OAAO,CAACF,MAAM,CAACrK,KAAK,CAAC;IAC7B;IAED,IAAI,CAACgK,cAAc,CAAC0B,MAAM,CAACtQ,GAAG,CAAC;IAE/B,IAAI,IAAI,CAAC4P,IAAI,EAAE;MACb;MACA,IAAI,CAACJ,mBAAmB,EAAE;IAC3B;IAED;IACA;IACA,IAAI5K,KAAK,KAAKxF,SAAS,IAAI4O,IAAI,KAAK5O,SAAS,EAAE;MAC7C,IAAImR,cAAc,GAAG,IAAIlN,KAAK,CAC5B,0BAA0B,GAAArD,GAAG,gGACwB,CACtD;MACDoK,MAAM,CAAC+F,cAAc,CAACL,OAAO,EAAE,QAAQ,EAAE;QAAEM,GAAG,EAAEA,CAAA,KAAMG;MAAc,CAAE,CAAC;MACvE,IAAI,CAACC,IAAI,CAAC,KAAK,EAAExQ,GAAG,CAAC;MACrB,OAAOmP,OAAO,CAACF,MAAM,CAACsB,cAAc,CAAC;IACtC;IAED,IAAIvC,IAAI,KAAK5O,SAAS,EAAE;MACtBgL,MAAM,CAAC+F,cAAc,CAACL,OAAO,EAAE,QAAQ,EAAE;QAAEM,GAAG,EAAEA,CAAA,KAAMxL;MAAK,CAAE,CAAC;MAC9D,IAAI,CAAC4L,IAAI,CAAC,KAAK,EAAExQ,GAAG,CAAC;MACrB,OAAOmP,OAAO,CAACF,MAAM,CAACrK,KAAK,CAAC;IAC7B;IAEDwF,MAAM,CAAC+F,cAAc,CAACL,OAAO,EAAE,OAAO,EAAE;MAAEM,GAAG,EAAEA,CAAA,KAAMpC;IAAI,CAAE,CAAC;IAC5D,IAAI,CAACwC,IAAI,CAAC,KAAK,EAAExQ,GAAG,CAAC;IACrB,OAAOgO,IAAI;EACb;EAEQwC,IAAIA,CAACH,OAAgB,EAAEI,UAAmB;IAChD,IAAI,CAAC5B,WAAW,CAACnH,OAAO,CAAEgJ,UAAU,IAAKA,UAAU,CAACL,OAAO,EAAEI,UAAU,CAAC,CAAC;EAC3E;EAEAE,SAASA,CAAC/O,EAAmD;IAC3D,IAAI,CAACiN,WAAW,CAACgB,GAAG,CAACjO,EAAE,CAAC;IACxB,OAAO,MAAM,IAAI,CAACiN,WAAW,CAACyB,MAAM,CAAC1O,EAAE,CAAC;EAC1C;EAEAgP,MAAMA,CAAA;IACJ,IAAI,CAACvB,UAAU,CAACwB,KAAK,EAAE;IACvB,IAAI,CAACjC,cAAc,CAAClH,OAAO,CAAC,CAACoJ,CAAC,EAAEC,CAAC,KAAK,IAAI,CAACnC,cAAc,CAAC0B,MAAM,CAACS,CAAC,CAAC,CAAC;IACpE,IAAI,CAACP,IAAI,CAAC,IAAI,CAAC;EACjB;EAEA,MAAMQ,WAAWA,CAACvB,MAAmB;IACnC,IAAIY,OAAO,GAAG,KAAK;IACnB,IAAI,CAAC,IAAI,CAACT,IAAI,EAAE;MACd,IAAIL,OAAO,GAAGA,CAAA,KAAM,IAAI,CAACqB,MAAM,EAAE;MACjCnB,MAAM,CAACxK,gBAAgB,CAAC,OAAO,EAAEsK,OAAO,CAAC;MACzCc,OAAO,GAAG,MAAM,IAAIlB,OAAO,CAAE8B,OAAO,IAAI;QACtC,IAAI,CAACN,SAAS,CAAEN,OAAO,IAAI;UACzBZ,MAAM,CAACvK,mBAAmB,CAAC,OAAO,EAAEqK,OAAO,CAAC;UAC5C,IAAIc,OAAO,IAAI,IAAI,CAACT,IAAI,EAAE;YACxBqB,OAAO,CAACZ,OAAO,CAAC;UACjB;QACH,CAAC,CAAC;MACJ,CAAC,CAAC;IACH;IACD,OAAOA,OAAO;EAChB;EAEA,IAAIT,IAAIA,CAAA;IACN,OAAO,IAAI,CAAChB,cAAc,CAACsC,IAAI,KAAK,CAAC;EACvC;EAEA,IAAIC,aAAaA,CAAA;IACfjO,SAAS,CACP,IAAI,CAAC8K,IAAI,KAAK,IAAI,IAAI,IAAI,CAAC4B,IAAI,EAC/B,2DAA2D,CAC5D;IAED,OAAOxF,MAAM,CAACtL,OAAO,CAAC,IAAI,CAACkP,IAAI,CAAC,CAACxE,MAAM,CACrC,CAACkG,GAAG,EAAA0B,KAAA;MAAA,IAAE,CAACpR,GAAG,EAAEmD,KAAK,CAAC,GAAAiO,KAAA;MAAA,OAChBhH,MAAM,CAACrF,MAAM,CAAC2K,GAAG,EAAE;QACjB,CAAC1P,GAAG,GAAGqR,oBAAoB,CAAClO,KAAK;OAClC,CAAC;KACJ,IAAE,CACH;EACH;EAEA,IAAImO,WAAWA,CAAA;IACb,OAAOvC,KAAK,CAACxB,IAAI,CAAC,IAAI,CAACqB,cAAc,CAAC;EACxC;AACD;AAED,SAAS2C,gBAAgBA,CAACpO,KAAU;EAClC,OACEA,KAAK,YAAYgM,OAAO,IAAKhM,KAAwB,CAACqO,QAAQ,KAAK,IAAI;AAE3E;AAEA,SAASH,oBAAoBA,CAAClO,KAAU;EACtC,IAAI,CAACoO,gBAAgB,CAACpO,KAAK,CAAC,EAAE;IAC5B,OAAOA,KAAK;EACb;EAED,IAAIA,KAAK,CAACsO,MAAM,EAAE;IAChB,MAAMtO,KAAK,CAACsO,MAAM;EACnB;EACD,OAAOtO,KAAK,CAACuO,KAAK;AACpB;AAOO,MAAMC,KAAK,GAAkB,SAAvBA,KAAKA,CAAmB3D,IAAI,EAAEC,IAAI,EAAS;EAAA,IAAbA,IAAI;IAAJA,IAAI,GAAG,EAAE;EAAA;EAClD,IAAIC,YAAY,GAAG,OAAOD,IAAI,KAAK,QAAQ,GAAG;IAAEE,MAAM,EAAEF;EAAI,CAAE,GAAGA,IAAI;EAErE,OAAO,IAAIS,YAAY,CAACV,IAAI,EAAEE,YAAY,CAAC;AAC7C;AAOA;;;AAGG;AACI,MAAM0D,QAAQ,GAAqB,SAA7BA,QAAQA,CAAsB/O,GAAG,EAAEoL,IAAI,EAAU;EAAA,IAAdA,IAAI;IAAJA,IAAI,GAAG,GAAG;EAAA;EACxD,IAAIC,YAAY,GAAGD,IAAI;EACvB,IAAI,OAAOC,YAAY,KAAK,QAAQ,EAAE;IACpCA,YAAY,GAAG;MAAEC,MAAM,EAAED;KAAc;GACxC,MAAM,IAAI,OAAOA,YAAY,CAACC,MAAM,KAAK,WAAW,EAAE;IACrDD,YAAY,CAACC,MAAM,GAAG,GAAG;EAC1B;EAED,IAAIC,OAAO,GAAG,IAAIC,OAAO,CAACH,YAAY,CAACE,OAAO,CAAC;EAC/CA,OAAO,CAACG,GAAG,CAAC,UAAU,EAAE1L,GAAG,CAAC;EAE5B,OAAO,IAAI2L,QAAQ,CAAC,IAAI,EAAAxK,QAAA,KACnBkK,YAAY;IACfE;EAAO,EACR,CAAC;AACJ;AAEA;;;AAGG;MACUyD,aAAa;EAOxBlD,WACEA,CAAAR,MAAc,EACd2D,UAA8B,EAC9B9D,IAAS,EACT+D,QAAQ,EAAQ;IAAA,IAAhBA,QAAQ;MAARA,QAAQ,GAAG,KAAK;IAAA;IAEhB,IAAI,CAAC5D,MAAM,GAAGA,MAAM;IACpB,IAAI,CAAC2D,UAAU,GAAGA,UAAU,IAAI,EAAE;IAClC,IAAI,CAACC,QAAQ,GAAGA,QAAQ;IACxB,IAAI/D,IAAI,YAAY3K,KAAK,EAAE;MACzB,IAAI,CAAC2K,IAAI,GAAGA,IAAI,CAACpK,QAAQ,EAAE;MAC3B,IAAI,CAACgB,KAAK,GAAGoJ,IAAI;IAClB,OAAM;MACL,IAAI,CAACA,IAAI,GAAGA,IAAI;IACjB;EACH;AACD;AAED;;;AAGG;AACG,SAAUgE,oBAAoBA,CAACpN,KAAU;EAC7C,OACEA,KAAK,IAAI,IAAI,IACb,OAAOA,KAAK,CAACuJ,MAAM,KAAK,QAAQ,IAChC,OAAOvJ,KAAK,CAACkN,UAAU,KAAK,QAAQ,IACpC,OAAOlN,KAAK,CAACmN,QAAQ,KAAK,SAAS,IACnC,MAAM,IAAInN,KAAK;AAEnB;AC/2BA,MAAMqN,uBAAuB,GAAyB,CACpD,MAAM,EACN,KAAK,EACL,OAAO,EACP,QAAQ,CACT;AACD,MAAMC,oBAAoB,GAAG,IAAI7M,GAAG,CAClC4M,uBAAuB,CACxB;AAED,MAAME,sBAAsB,GAAiB,CAC3C,KAAK,EACL,GAAGF,uBAAuB,CAC3B;AACD,MAAMG,mBAAmB,GAAG,IAAI/M,GAAG,CAAa8M,sBAAsB,CAAC;AAEvE,MAAME,mBAAmB,GAAG,IAAIhN,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;AAC9D,MAAMiN,iCAAiC,GAAG,IAAIjN,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;AAEtD,MAAMkN,eAAe,GAA6B;EACvDpT,KAAK,EAAE,MAAM;EACbc,QAAQ,EAAEb,SAAS;EACnBoT,UAAU,EAAEpT,SAAS;EACrBqT,UAAU,EAAErT,SAAS;EACrBsT,WAAW,EAAEtT,SAAS;EACtBuT,QAAQ,EAAEvT,SAAS;EACnB2O,IAAI,EAAE3O,SAAS;EACfwT,IAAI,EAAExT;;AAGD,MAAMyT,YAAY,GAA0B;EACjD1T,KAAK,EAAE,MAAM;EACb6O,IAAI,EAAE5O,SAAS;EACfoT,UAAU,EAAEpT,SAAS;EACrBqT,UAAU,EAAErT,SAAS;EACrBsT,WAAW,EAAEtT,SAAS;EACtBuT,QAAQ,EAAEvT,SAAS;EACnB2O,IAAI,EAAE3O,SAAS;EACfwT,IAAI,EAAExT;;AAGD,MAAM0T,YAAY,GAAqB;EAC5C3T,KAAK,EAAE,WAAW;EAClB4T,OAAO,EAAE3T,SAAS;EAClB4T,KAAK,EAAE5T,SAAS;EAChBa,QAAQ,EAAEb;;AAGZ,MAAM6T,kBAAkB,GAAG,+BAA+B;AAE1D,MAAMC,yBAAyB,GAAgC3N,KAAK,KAAM;EACxE4N,gBAAgB,EAAEC,OAAO,CAAC7N,KAAK,CAAC4N,gBAAgB;AACjD,EAAC;AAEF;AAEA;AACA;AACA;AAEA;;AAEG;AACG,SAAUE,YAAYA,CAACpF,IAAgB;EAC3C,MAAMqF,YAAY,GAAGrF,IAAI,CAAClM,MAAM,GAC5BkM,IAAI,CAAClM,MAAM,GACX,OAAOA,MAAM,KAAK,WAAW,GAC7BA,MAAM,GACN3C,SAAS;EACb,MAAMmU,SAAS,GACb,OAAOD,YAAY,KAAK,WAAW,IACnC,OAAOA,YAAY,CAAC7Q,QAAQ,KAAK,WAAW,IAC5C,OAAO6Q,YAAY,CAAC7Q,QAAQ,CAAC+Q,aAAa,KAAK,WAAW;EAC5D,MAAMC,QAAQ,GAAG,CAACF,SAAS;EAE3BrQ,SAAS,CACP+K,IAAI,CAACxI,MAAM,CAACnG,MAAM,GAAG,CAAC,EACtB,2DAA2D,CAC5D;EAED,IAAIoG,kBAA8C;EAClD,IAAIuI,IAAI,CAACvI,kBAAkB,EAAE;IAC3BA,kBAAkB,GAAGuI,IAAI,CAACvI,kBAAkB;EAC7C,OAAM,IAAIuI,IAAI,CAACyF,mBAAmB,EAAE;IACnC;IACA,IAAIA,mBAAmB,GAAGzF,IAAI,CAACyF,mBAAmB;IAClDhO,kBAAkB,GAAIH,KAAK,KAAM;MAC/B4N,gBAAgB,EAAEO,mBAAmB,CAACnO,KAAK;IAC5C,EAAC;EACH,OAAM;IACLG,kBAAkB,GAAGwN,yBAAyB;EAC/C;EAED;EACA,IAAItN,QAAQ,GAAkB,EAAE;EAChC;EACA,IAAI+N,UAAU,GAAGnO,yBAAyB,CACxCyI,IAAI,CAACxI,MAAM,EACXC,kBAAkB,EAClBtG,SAAS,EACTwG,QAAQ,CACT;EACD,IAAIgO,kBAAyD;EAC7D,IAAIvN,QAAQ,GAAG4H,IAAI,CAAC5H,QAAQ,IAAI,GAAG;EACnC;EACA,IAAIwN,MAAM,GAAA7P,QAAA;IACR8P,sBAAsB,EAAE,KAAK;IAC7BC,kBAAkB,EAAE;GACjB,EAAA9F,IAAI,CAAC4F,MAAM,CACf;EACD;EACA,IAAIG,eAAe,GAAwB,IAAI;EAC/C;EACA,IAAInF,WAAW,GAAG,IAAIxJ,GAAG,EAAoB;EAC7C;EACA,IAAI4O,oBAAoB,GAAkC,IAAI;EAC9D;EACA,IAAIC,uBAAuB,GAA2C,IAAI;EAC1E;EACA,IAAIC,iBAAiB,GAAqC,IAAI;EAC9D;EACA;EACA;EACA;EACA;EACA;EACA,IAAIC,qBAAqB,GAAGnG,IAAI,CAACoG,aAAa,IAAI,IAAI;EAEtD,IAAIC,cAAc,GAAGnO,WAAW,CAACwN,UAAU,EAAE1F,IAAI,CAACvN,OAAO,CAACT,QAAQ,EAAEoG,QAAQ,CAAC;EAC7E,IAAIkO,aAAa,GAAqB,IAAI;EAE1C,IAAID,cAAc,IAAI,IAAI,EAAE;IAC1B;IACA;IACA,IAAI1P,KAAK,GAAG4P,sBAAsB,CAAC,GAAG,EAAE;MACtCrU,QAAQ,EAAE8N,IAAI,CAACvN,OAAO,CAACT,QAAQ,CAACE;IACjC,EAAC;IACF,IAAI;MAAEuG,OAAO;MAAEnB;IAAK,CAAE,GAAGkP,sBAAsB,CAACd,UAAU,CAAC;IAC3DW,cAAc,GAAG5N,OAAO;IACxB6N,aAAa,GAAG;MAAE,CAAChP,KAAK,CAACO,EAAE,GAAGlB;KAAO;EACtC;EAED,IAAI8P,WAAW;EACb;EACA;EACA,CAACJ,cAAc,CAAChL,IAAI,CAAEqL,CAAC,IAAKA,CAAC,CAACpP,KAAK,CAACqP,IAAI,CAAC;EACzC;EACC,CAACN,cAAc,CAAChL,IAAI,CAAEqL,CAAC,IAAKA,CAAC,CAACpP,KAAK,CAACsP,MAAM,CAAC,IAAI5G,IAAI,CAACoG,aAAa,IAAI,IAAI,CAAC;EAE7E,IAAIS,MAAc;EAClB,IAAI3V,KAAK,GAAgB;IACvB4V,aAAa,EAAE9G,IAAI,CAACvN,OAAO,CAACnB,MAAM;IAClCU,QAAQ,EAAEgO,IAAI,CAACvN,OAAO,CAACT,QAAQ;IAC/ByG,OAAO,EAAE4N,cAAc;IACvBI,WAAW;IACXM,UAAU,EAAEzC,eAAe;IAC3B;IACA0C,qBAAqB,EAAEhH,IAAI,CAACoG,aAAa,IAAI,IAAI,GAAG,KAAK,GAAG,IAAI;IAChEa,kBAAkB,EAAE,KAAK;IACzBC,YAAY,EAAE,MAAM;IACpBC,UAAU,EAAGnH,IAAI,CAACoG,aAAa,IAAIpG,IAAI,CAACoG,aAAa,CAACe,UAAU,IAAK,EAAE;IACvEC,UAAU,EAAGpH,IAAI,CAACoG,aAAa,IAAIpG,IAAI,CAACoG,aAAa,CAACgB,UAAU,IAAK,IAAI;IACzEC,MAAM,EAAGrH,IAAI,CAACoG,aAAa,IAAIpG,IAAI,CAACoG,aAAa,CAACiB,MAAM,IAAKf,aAAa;IAC1EgB,QAAQ,EAAE,IAAIC,GAAG,EAAE;IACnBC,QAAQ,EAAE,IAAID,GAAG;GAClB;EAED;EACA;EACA,IAAIE,aAAa,GAAkBnX,MAAa,CAACiB,GAAG;EAEpD;EACA;EACA,IAAImW,yBAAyB,GAAG,KAAK;EAErC;EACA,IAAIC,2BAAmD;EAEvD;EACA;EACA,IAAIC,2BAA2B,GAAG,KAAK;EAEvC;EACA;EACA;EACA;EACA,IAAIC,sBAAsB,GAAG,KAAK;EAElC;EACA;EACA,IAAIC,uBAAuB,GAAa,EAAE;EAE1C;EACA;EACA,IAAIC,qBAAqB,GAAa,EAAE;EAExC;EACA,IAAIC,gBAAgB,GAAG,IAAIT,GAAG,EAA2B;EAEzD;EACA,IAAIU,kBAAkB,GAAG,CAAC;EAE1B;EACA;EACA;EACA,IAAIC,uBAAuB,GAAG,CAAC,CAAC;EAEhC;EACA,IAAIC,cAAc,GAAG,IAAIZ,GAAG,EAAkB;EAE9C;EACA,IAAIa,gBAAgB,GAAG,IAAIhR,GAAG,EAAU;EAExC;EACA,IAAIiR,gBAAgB,GAAG,IAAId,GAAG,EAA0B;EAExD;EACA;EACA;EACA;EACA,IAAIe,eAAe,GAAG,IAAIf,GAAG,EAAwB;EAErD;EACA;EACA,IAAIgB,gBAAgB,GAAG,IAAIhB,GAAG,EAA2B;EAEzD;EACA;EACA,IAAIiB,uBAAuB,GAAG,KAAK;EAEnC;EACA;EACA;EACA,SAASC,UAAUA,CAAA;IACjB;IACA;IACA1C,eAAe,GAAG/F,IAAI,CAACvN,OAAO,CAACiB,MAAM,CACnCsC,IAAA,IAA+C;MAAA,IAA9C;QAAE1E,MAAM,EAAEwV,aAAa;QAAE9U,QAAQ;QAAEqB;MAAK,CAAE,GAAA2C,IAAA;MACzC;MACA;MACA,IAAIwS,uBAAuB,EAAE;QAC3BA,uBAAuB,GAAG,KAAK;QAC/B;MACD;MAEDrW,OAAO,CACLoW,gBAAgB,CAACtF,IAAI,KAAK,CAAC,IAAI5P,KAAK,IAAI,IAAI,EAC5C,oEAAoE,GAClE,wEAAwE,GACxE,uEAAuE,GACvE,yEAAyE,GACzE,iEAAiE,GACjE,yDAAyD,CAC5D;MAED,IAAIqV,UAAU,GAAGC,qBAAqB,CAAC;QACrCC,eAAe,EAAE1X,KAAK,CAACc,QAAQ;QAC/BmB,YAAY,EAAEnB,QAAQ;QACtB8U;MACD,EAAC;MAEF,IAAI4B,UAAU,IAAIrV,KAAK,IAAI,IAAI,EAAE;QAC/B;QACAmV,uBAAuB,GAAG,IAAI;QAC9BxI,IAAI,CAACvN,OAAO,CAACe,EAAE,CAACH,KAAK,GAAG,CAAC,CAAC,CAAC;QAE3B;QACAwV,aAAa,CAACH,UAAU,EAAE;UACxBxX,KAAK,EAAE,SAAS;UAChBc,QAAQ;UACR8S,OAAOA,CAAA;YACL+D,aAAa,CAACH,UAAW,EAAE;cACzBxX,KAAK,EAAE,YAAY;cACnB4T,OAAO,EAAE3T,SAAS;cAClB4T,KAAK,EAAE5T,SAAS;cAChBa;YACD,EAAC;YACF;YACAgO,IAAI,CAACvN,OAAO,CAACe,EAAE,CAACH,KAAK,CAAC;WACvB;UACD0R,KAAKA,CAAA;YACH,IAAIyC,QAAQ,GAAG,IAAID,GAAG,CAACrW,KAAK,CAACsW,QAAQ,CAAC;YACtCA,QAAQ,CAAClH,GAAG,CAACoI,UAAW,EAAE7D,YAAY,CAAC;YACvCiE,WAAW,CAAC;cAAEtB;YAAQ,CAAE,CAAC;UAC3B;QACD,EAAC;QACF;MACD;MAED,OAAOuB,eAAe,CAACjC,aAAa,EAAE9U,QAAQ,CAAC;IACjD,CAAC,CACF;IAED;IACA;IACA;IACA;IACA;IACA,IAAI,CAACd,KAAK,CAACuV,WAAW,EAAE;MACtBsC,eAAe,CAACzY,MAAa,CAACiB,GAAG,EAAEL,KAAK,CAACc,QAAQ,CAAC;IACnD;IAED,OAAO6U,MAAM;EACf;EAEA;EACA,SAASmC,OAAOA,CAAA;IACd,IAAIjD,eAAe,EAAE;MACnBA,eAAe,EAAE;IAClB;IACDnF,WAAW,CAACqI,KAAK,EAAE;IACnBtB,2BAA2B,IAAIA,2BAA2B,CAAC/E,KAAK,EAAE;IAClE1R,KAAK,CAACoW,QAAQ,CAAC7N,OAAO,CAAC,CAACmE,CAAC,EAAE7L,GAAG,KAAKmX,aAAa,CAACnX,GAAG,CAAC,CAAC;IACtDb,KAAK,CAACsW,QAAQ,CAAC/N,OAAO,CAAC,CAACmE,CAAC,EAAE7L,GAAG,KAAKoX,aAAa,CAACpX,GAAG,CAAC,CAAC;EACxD;EAEA;EACA,SAAS2Q,SAASA,CAAC/O,EAAoB;IACrCiN,WAAW,CAACgB,GAAG,CAACjO,EAAE,CAAC;IACnB,OAAO,MAAMiN,WAAW,CAACyB,MAAM,CAAC1O,EAAE,CAAC;EACrC;EAEA;EACA,SAASmV,WAAWA,CAACM,QAA8B;IACjDlY,KAAK,GAAA6E,QAAA,KACA7E,KAAK,EACLkY,QAAQ,CACZ;IACDxI,WAAW,CAACnH,OAAO,CAAEgJ,UAAU,IAAKA,UAAU,CAACvR,KAAK,CAAC,CAAC;EACxD;EAEA;EACA;EACA;EACA;EACA;EACA,SAASmY,kBAAkBA,CACzBrX,QAAkB,EAClBoX,QAA0E;IAAA,IAAAE,eAAA,EAAAC,gBAAA;IAE1E;IACA;IACA;IACA;IACA;IACA,IAAIC,cAAc,GAChBtY,KAAK,CAACkW,UAAU,IAAI,IAAI,IACxBlW,KAAK,CAAC6V,UAAU,CAACxC,UAAU,IAAI,IAAI,IACnCkF,gBAAgB,CAACvY,KAAK,CAAC6V,UAAU,CAACxC,UAAU,CAAC,IAC7CrT,KAAK,CAAC6V,UAAU,CAAC7V,KAAK,KAAK,SAAS,IACpC,EAAAoY,eAAA,GAAAtX,QAAQ,CAACd,KAAK,qBAAdoY,eAAA,CAAgBI,WAAW,MAAK,IAAI;IAEtC,IAAItC,UAA4B;IAChC,IAAIgC,QAAQ,CAAChC,UAAU,EAAE;MACvB,IAAIjL,MAAM,CAACwN,IAAI,CAACP,QAAQ,CAAChC,UAAU,CAAC,CAAC/V,MAAM,GAAG,CAAC,EAAE;QAC/C+V,UAAU,GAAGgC,QAAQ,CAAChC,UAAU;MACjC,OAAM;QACL;QACAA,UAAU,GAAG,IAAI;MAClB;KACF,MAAM,IAAIoC,cAAc,EAAE;MACzB;MACApC,UAAU,GAAGlW,KAAK,CAACkW,UAAU;IAC9B,OAAM;MACL;MACAA,UAAU,GAAG,IAAI;IAClB;IAED;IACA,IAAID,UAAU,GAAGiC,QAAQ,CAACjC,UAAU,GAChCyC,eAAe,CACb1Y,KAAK,CAACiW,UAAU,EAChBiC,QAAQ,CAACjC,UAAU,EACnBiC,QAAQ,CAAC3Q,OAAO,IAAI,EAAE,EACtB2Q,QAAQ,CAAC/B,MAAM,CAChB,GACDnW,KAAK,CAACiW,UAAU;IAEpB;IACA;IACA,IAAIK,QAAQ,GAAGtW,KAAK,CAACsW,QAAQ;IAC7B,IAAIA,QAAQ,CAACvE,IAAI,GAAG,CAAC,EAAE;MACrBuE,QAAQ,GAAG,IAAID,GAAG,CAACC,QAAQ,CAAC;MAC5BA,QAAQ,CAAC/N,OAAO,CAAC,CAACmE,CAAC,EAAEkF,CAAC,KAAK0E,QAAQ,CAAClH,GAAG,CAACwC,CAAC,EAAE+B,YAAY,CAAC,CAAC;IAC1D;IAED;IACA;IACA,IAAIoC,kBAAkB,GACpBS,yBAAyB,KAAK,IAAI,IACjCxW,KAAK,CAAC6V,UAAU,CAACxC,UAAU,IAAI,IAAI,IAClCkF,gBAAgB,CAACvY,KAAK,CAAC6V,UAAU,CAACxC,UAAU,CAAC,IAC7C,EAAAgF,gBAAA,GAAAvX,QAAQ,CAACd,KAAK,KAAd,gBAAAqY,gBAAA,CAAgBG,WAAW,MAAK,IAAK;IAEzC,IAAI/D,kBAAkB,EAAE;MACtBD,UAAU,GAAGC,kBAAkB;MAC/BA,kBAAkB,GAAGxU,SAAS;IAC/B;IAED,IAAIyW,2BAA2B,EAAE,CAEhC,KAAM,IAAIH,aAAa,KAAKnX,MAAa,CAACiB,GAAG,EAAE,CAE/C,KAAM,IAAIkW,aAAa,KAAKnX,MAAa,CAAC4C,IAAI,EAAE;MAC/C8M,IAAI,CAACvN,OAAO,CAACQ,IAAI,CAACjB,QAAQ,EAAEA,QAAQ,CAACd,KAAK,CAAC;IAC5C,OAAM,IAAIuW,aAAa,KAAKnX,MAAa,CAACiD,OAAO,EAAE;MAClDyM,IAAI,CAACvN,OAAO,CAACa,OAAO,CAACtB,QAAQ,EAAEA,QAAQ,CAACd,KAAK,CAAC;IAC/C;IAED4X,WAAW,CAAA/S,QAAA,KACNqT,QAAQ;MACXhC,UAAU;MACVD,UAAU;MACVL,aAAa,EAAEW,aAAa;MAC5BzV,QAAQ;MACRyU,WAAW,EAAE,IAAI;MACjBM,UAAU,EAAEzC,eAAe;MAC3B4C,YAAY,EAAE,MAAM;MACpBF,qBAAqB,EAAE6C,sBAAsB,CAC3C7X,QAAQ,EACRoX,QAAQ,CAAC3Q,OAAO,IAAIvH,KAAK,CAACuH,OAAO,CAClC;MACDwO,kBAAkB;MAClBO;IAAQ,EACT,CAAC;IAEF;IACAC,aAAa,GAAGnX,MAAa,CAACiB,GAAG;IACjCmW,yBAAyB,GAAG,KAAK;IACjCE,2BAA2B,GAAG,KAAK;IACnCC,sBAAsB,GAAG,KAAK;IAC9BC,uBAAuB,GAAG,EAAE;IAC5BC,qBAAqB,GAAG,EAAE;EAC5B;EAEA;EACA;EACA,eAAe+B,QAAQA,CACrBhY,EAAsB,EACtBiY,IAA4B;IAE5B,IAAI,OAAOjY,EAAE,KAAK,QAAQ,EAAE;MAC1BkO,IAAI,CAACvN,OAAO,CAACe,EAAE,CAAC1B,EAAE,CAAC;MACnB;IACD;IAED,IAAIkY,cAAc,GAAGC,WAAW,CAC9B/Y,KAAK,CAACc,QAAQ,EACdd,KAAK,CAACuH,OAAO,EACbL,QAAQ,EACRwN,MAAM,CAACE,kBAAkB,EACzBhU,EAAE,EACFiY,IAAI,oBAAJA,IAAI,CAAEG,WAAW,EACjBH,IAAI,oBAAJA,IAAI,CAAEI,QAAQ,CACf;IACD,IAAI;MAAEtX,IAAI;MAAEuX,UAAU;MAAEzT;IAAK,CAAE,GAAG0T,wBAAwB,CACxDzE,MAAM,CAACC,sBAAsB,EAC7B,KAAK,EACLmE,cAAc,EACdD,IAAI,CACL;IAED,IAAInB,eAAe,GAAG1X,KAAK,CAACc,QAAQ;IACpC,IAAImB,YAAY,GAAGlB,cAAc,CAACf,KAAK,CAACc,QAAQ,EAAEa,IAAI,EAAEkX,IAAI,IAAIA,IAAI,CAAC7Y,KAAK,CAAC;IAE3E;IACA;IACA;IACA;IACA;IACAiC,YAAY,GAAA4C,QAAA,CACP,IAAA5C,YAAY,EACZ6M,IAAI,CAACvN,OAAO,CAACG,cAAc,CAACO,YAAY,CAAC,CAC7C;IAED,IAAImX,WAAW,GAAGP,IAAI,IAAIA,IAAI,CAACzW,OAAO,IAAI,IAAI,GAAGyW,IAAI,CAACzW,OAAO,GAAGnC,SAAS;IAEzE,IAAI2V,aAAa,GAAGxW,MAAa,CAAC4C,IAAI;IAEtC,IAAIoX,WAAW,KAAK,IAAI,EAAE;MACxBxD,aAAa,GAAGxW,MAAa,CAACiD,OAAO;IACtC,OAAM,IAAI+W,WAAW,KAAK,KAAK,EAAE,CAEjC,KAAM,IACLF,UAAU,IAAI,IAAI,IAClBX,gBAAgB,CAACW,UAAU,CAAC7F,UAAU,CAAC,IACvC6F,UAAU,CAAC5F,UAAU,KAAKtT,KAAK,CAACc,QAAQ,CAACE,QAAQ,GAAGhB,KAAK,CAACc,QAAQ,CAACe,MAAM,EACzE;MACA;MACA;MACA;MACA;MACA+T,aAAa,GAAGxW,MAAa,CAACiD,OAAO;IACtC;IAED,IAAI0T,kBAAkB,GACpB8C,IAAI,IAAI,oBAAoB,IAAIA,IAAI,GAChCA,IAAI,CAAC9C,kBAAkB,KAAK,IAAI,GAChC9V,SAAS;IAEf,IAAIuX,UAAU,GAAGC,qBAAqB,CAAC;MACrCC,eAAe;MACfzV,YAAY;MACZ2T;IACD,EAAC;IAEF,IAAI4B,UAAU,EAAE;MACd;MACAG,aAAa,CAACH,UAAU,EAAE;QACxBxX,KAAK,EAAE,SAAS;QAChBc,QAAQ,EAAEmB,YAAY;QACtB2R,OAAOA,CAAA;UACL+D,aAAa,CAACH,UAAW,EAAE;YACzBxX,KAAK,EAAE,YAAY;YACnB4T,OAAO,EAAE3T,SAAS;YAClB4T,KAAK,EAAE5T,SAAS;YAChBa,QAAQ,EAAEmB;UACX,EAAC;UACF;UACA2W,QAAQ,CAAChY,EAAE,EAAEiY,IAAI,CAAC;SACnB;QACDhF,KAAKA,CAAA;UACH,IAAIyC,QAAQ,GAAG,IAAID,GAAG,CAACrW,KAAK,CAACsW,QAAQ,CAAC;UACtCA,QAAQ,CAAClH,GAAG,CAACoI,UAAW,EAAE7D,YAAY,CAAC;UACvCiE,WAAW,CAAC;YAAEtB;UAAQ,CAAE,CAAC;QAC3B;MACD,EAAC;MACF;IACD;IAED,OAAO,MAAMuB,eAAe,CAACjC,aAAa,EAAE3T,YAAY,EAAE;MACxDiX,UAAU;MACV;MACA;MACAG,YAAY,EAAE5T,KAAK;MACnBsQ,kBAAkB;MAClB3T,OAAO,EAAEyW,IAAI,IAAIA,IAAI,CAACzW;IACvB,EAAC;EACJ;EAEA;EACA;EACA;EACA,SAASkX,UAAUA,CAAA;IACjBC,oBAAoB,EAAE;IACtB3B,WAAW,CAAC;MAAE5B,YAAY,EAAE;IAAS,CAAE,CAAC;IAExC;IACA;IACA,IAAIhW,KAAK,CAAC6V,UAAU,CAAC7V,KAAK,KAAK,YAAY,EAAE;MAC3C;IACD;IAED;IACA;IACA;IACA,IAAIA,KAAK,CAAC6V,UAAU,CAAC7V,KAAK,KAAK,MAAM,EAAE;MACrC6X,eAAe,CAAC7X,KAAK,CAAC4V,aAAa,EAAE5V,KAAK,CAACc,QAAQ,EAAE;QACnD0Y,8BAA8B,EAAE;MACjC,EAAC;MACF;IACD;IAED;IACA;IACA;IACA3B,eAAe,CACbtB,aAAa,IAAIvW,KAAK,CAAC4V,aAAa,EACpC5V,KAAK,CAAC6V,UAAU,CAAC/U,QAAQ,EACzB;MAAE2Y,kBAAkB,EAAEzZ,KAAK,CAAC6V;IAAY,EACzC;EACH;EAEA;EACA;EACA;EACA,eAAegC,eAAeA,CAC5BjC,aAA4B,EAC5B9U,QAAkB,EAClB+X,IAQC;IAED;IACA;IACA;IACApC,2BAA2B,IAAIA,2BAA2B,CAAC/E,KAAK,EAAE;IAClE+E,2BAA2B,GAAG,IAAI;IAClCF,aAAa,GAAGX,aAAa;IAC7Bc,2BAA2B,GACzB,CAACmC,IAAI,IAAIA,IAAI,CAACW,8BAA8B,MAAM,IAAI;IAExD;IACA;IACAE,kBAAkB,CAAC1Z,KAAK,CAACc,QAAQ,EAAEd,KAAK,CAACuH,OAAO,CAAC;IACjDiP,yBAAyB,GAAG,CAACqC,IAAI,IAAIA,IAAI,CAAC9C,kBAAkB,MAAM,IAAI;IAEtE,IAAI4D,WAAW,GAAGlF,kBAAkB,IAAID,UAAU;IAClD,IAAIoF,iBAAiB,GAAGf,IAAI,IAAIA,IAAI,CAACY,kBAAkB;IACvD,IAAIlS,OAAO,GAAGP,WAAW,CAAC2S,WAAW,EAAE7Y,QAAQ,EAAEoG,QAAQ,CAAC;IAE1D;IACA,IAAI,CAACK,OAAO,EAAE;MACZ,IAAI9B,KAAK,GAAG4P,sBAAsB,CAAC,GAAG,EAAE;QAAErU,QAAQ,EAAEF,QAAQ,CAACE;MAAQ,CAAE,CAAC;MACxE,IAAI;QAAEuG,OAAO,EAAEsS,eAAe;QAAEzT;MAAO,IACrCkP,sBAAsB,CAACqE,WAAW,CAAC;MACrC;MACAG,qBAAqB,EAAE;MACvB3B,kBAAkB,CAACrX,QAAQ,EAAE;QAC3ByG,OAAO,EAAEsS,eAAe;QACxB5D,UAAU,EAAE,EAAE;QACdE,MAAM,EAAE;UACN,CAAC/P,KAAK,CAACO,EAAE,GAAGlB;QACb;MACF,EAAC;MACF;IACD;IAED;IACA;IACA;IACA;IACA;IACA;IACA,IACEzF,KAAK,CAACuV,WAAW,IACjB,CAACoB,sBAAsB,IACvBoD,gBAAgB,CAAC/Z,KAAK,CAACc,QAAQ,EAAEA,QAAQ,CAAC,IAC1C,EAAE+X,IAAI,IAAIA,IAAI,CAACK,UAAU,IAAIX,gBAAgB,CAACM,IAAI,CAACK,UAAU,CAAC7F,UAAU,CAAC,CAAC,EAC1E;MACA8E,kBAAkB,CAACrX,QAAQ,EAAE;QAAEyG;MAAO,CAAE,CAAC;MACzC;IACD;IAED;IACAkP,2BAA2B,GAAG,IAAItG,eAAe,EAAE;IACnD,IAAI6J,OAAO,GAAGC,uBAAuB,CACnCnL,IAAI,CAACvN,OAAO,EACZT,QAAQ,EACR2V,2BAA2B,CAACnG,MAAM,EAClCuI,IAAI,IAAIA,IAAI,CAACK,UAAU,CACxB;IACD,IAAIgB,iBAAwC;IAC5C,IAAIb,YAAmC;IAEvC,IAAIR,IAAI,IAAIA,IAAI,CAACQ,YAAY,EAAE;MAC7B;MACA;MACA;MACA;MACAA,YAAY,GAAG;QACb,CAACc,mBAAmB,CAAC5S,OAAO,CAAC,CAACnB,KAAK,CAACO,EAAE,GAAGkS,IAAI,CAACQ;OAC/C;IACF,OAAM,IACLR,IAAI,IACJA,IAAI,CAACK,UAAU,IACfX,gBAAgB,CAACM,IAAI,CAACK,UAAU,CAAC7F,UAAU,CAAC,EAC5C;MACA;MACA,IAAI+G,YAAY,GAAG,MAAMC,YAAY,CACnCL,OAAO,EACPlZ,QAAQ,EACR+X,IAAI,CAACK,UAAU,EACf3R,OAAO,EACP;QAAEnF,OAAO,EAAEyW,IAAI,CAACzW;MAAS,EAC1B;MAED,IAAIgY,YAAY,CAACE,cAAc,EAAE;QAC/B;MACD;MAEDJ,iBAAiB,GAAGE,YAAY,CAACF,iBAAiB;MAClDb,YAAY,GAAGe,YAAY,CAACG,kBAAkB;MAC9CX,iBAAiB,GAAGY,oBAAoB,CAAC1Z,QAAQ,EAAE+X,IAAI,CAACK,UAAU,CAAC;MAEnE;MACAc,OAAO,GAAG,IAAIS,OAAO,CAACT,OAAO,CAACtW,GAAG,EAAE;QAAE4M,MAAM,EAAE0J,OAAO,CAAC1J;MAAM,CAAE,CAAC;IAC/D;IAED;IACA,IAAI;MAAEgK,cAAc;MAAErE,UAAU;MAAEE;KAAQ,GAAG,MAAMuE,aAAa,CAC9DV,OAAO,EACPlZ,QAAQ,EACRyG,OAAO,EACPqS,iBAAiB,EACjBf,IAAI,IAAIA,IAAI,CAACK,UAAU,EACvBL,IAAI,IAAIA,IAAI,CAAC8B,iBAAiB,EAC9B9B,IAAI,IAAIA,IAAI,CAACzW,OAAO,EACpB8X,iBAAiB,EACjBb,YAAY,CACb;IAED,IAAIiB,cAAc,EAAE;MAClB;IACD;IAED;IACA;IACA;IACA7D,2BAA2B,GAAG,IAAI;IAElC0B,kBAAkB,CAACrX,QAAQ,EAAA+D,QAAA;MACzB0C;IAAO,GACH2S,iBAAiB,GAAG;MAAEhE,UAAU,EAAEgE;KAAmB,GAAG,EAAE;MAC9DjE,UAAU;MACVE;IAAM,EACP,CAAC;EACJ;EAEA;EACA;EACA,eAAekE,YAAYA,CACzBL,OAAgB,EAChBlZ,QAAkB,EAClBoY,UAAsB,EACtB3R,OAAiC,EACjCsR,IAAA,EAAgC;IAAA,IAAhCA,IAAA;MAAAA,IAAA,GAA8B,EAAE;IAAA;IAEhCU,oBAAoB,EAAE;IAEtB;IACA,IAAI1D,UAAU,GAAG+E,uBAAuB,CAAC9Z,QAAQ,EAAEoY,UAAU,CAAC;IAC9DtB,WAAW,CAAC;MAAE/B;IAAU,CAAE,CAAC;IAE3B;IACA,IAAIzM,MAAkB;IACtB,IAAIyR,WAAW,GAAGC,cAAc,CAACvT,OAAO,EAAEzG,QAAQ,CAAC;IAEnD,IAAI,CAAC+Z,WAAW,CAACzU,KAAK,CAAChG,MAAM,IAAI,CAACya,WAAW,CAACzU,KAAK,CAACqP,IAAI,EAAE;MACxDrM,MAAM,GAAG;QACP2R,IAAI,EAAE/U,UAAU,CAACP,KAAK;QACtBA,KAAK,EAAE4P,sBAAsB,CAAC,GAAG,EAAE;UACjC2F,MAAM,EAAEhB,OAAO,CAACgB,MAAM;UACtBha,QAAQ,EAAEF,QAAQ,CAACE,QAAQ;UAC3Bia,OAAO,EAAEJ,WAAW,CAACzU,KAAK,CAACO;SAC5B;OACF;IACF,OAAM;MACLyC,MAAM,GAAG,MAAM8R,kBAAkB,CAC/B,QAAQ,EACRlB,OAAO,EACPa,WAAW,EACXtT,OAAO,EACPd,QAAQ,EACRF,kBAAkB,EAClBW,QAAQ,CACT;MAED,IAAI8S,OAAO,CAAC1J,MAAM,CAACY,OAAO,EAAE;QAC1B,OAAO;UAAEoJ,cAAc,EAAE;SAAM;MAChC;IACF;IAED,IAAIa,gBAAgB,CAAC/R,MAAM,CAAC,EAAE;MAC5B,IAAIhH,OAAgB;MACpB,IAAIyW,IAAI,IAAIA,IAAI,CAACzW,OAAO,IAAI,IAAI,EAAE;QAChCA,OAAO,GAAGyW,IAAI,CAACzW,OAAO;MACvB,OAAM;QACL;QACA;QACA;QACAA,OAAO,GACLgH,MAAM,CAACtI,QAAQ,KAAKd,KAAK,CAACc,QAAQ,CAACE,QAAQ,GAAGhB,KAAK,CAACc,QAAQ,CAACe,MAAM;MACtE;MACD,MAAMuZ,uBAAuB,CAACpb,KAAK,EAAEoJ,MAAM,EAAE;QAAE8P,UAAU;QAAE9W;MAAS,EAAC;MACrE,OAAO;QAAEkY,cAAc,EAAE;OAAM;IAChC;IAED,IAAIe,aAAa,CAACjS,MAAM,CAAC,EAAE;MACzB;MACA;MACA,IAAIkS,aAAa,GAAGnB,mBAAmB,CAAC5S,OAAO,EAAEsT,WAAW,CAACzU,KAAK,CAACO,EAAE,CAAC;MAEtE;MACA;MACA;MACA;MACA,IAAI,CAACkS,IAAI,IAAIA,IAAI,CAACzW,OAAO,MAAM,IAAI,EAAE;QACnCmU,aAAa,GAAGnX,MAAa,CAAC4C,IAAI;MACnC;MAED,OAAO;QACL;QACAkY,iBAAiB,EAAE,EAAE;QACrBK,kBAAkB,EAAE;UAAE,CAACe,aAAa,CAAClV,KAAK,CAACO,EAAE,GAAGyC,MAAM,CAAC3D;QAAO;OAC/D;IACF;IAED,IAAI8V,gBAAgB,CAACnS,MAAM,CAAC,EAAE;MAC5B,MAAMiM,sBAAsB,CAAC,GAAG,EAAE;QAAE0F,IAAI,EAAE;MAAgB,EAAC;IAC5D;IAED,OAAO;MACLb,iBAAiB,EAAE;QAAE,CAACW,WAAW,CAACzU,KAAK,CAACO,EAAE,GAAGyC,MAAM,CAACyF;MAAM;KAC3D;EACH;EAEA;EACA;EACA,eAAe6L,aAAaA,CAC1BV,OAAgB,EAChBlZ,QAAkB,EAClByG,OAAiC,EACjCkS,kBAA+B,EAC/BP,UAAuB,EACvByB,iBAA8B,EAC9BvY,OAAiB,EACjB8X,iBAA6B,EAC7Bb,YAAwB;IAExB;IACA,IAAIO,iBAAiB,GACnBH,kBAAkB,IAAIe,oBAAoB,CAAC1Z,QAAQ,EAAEoY,UAAU,CAAC;IAElE;IACA;IACA,IAAIsC,gBAAgB,GAClBtC,UAAU,IACVyB,iBAAiB,IACjBc,2BAA2B,CAAC7B,iBAAiB,CAAC;IAEhD,IAAID,WAAW,GAAGlF,kBAAkB,IAAID,UAAU;IAClD,IAAI,CAACkH,aAAa,EAAEC,oBAAoB,CAAC,GAAGC,gBAAgB,CAC1D9M,IAAI,CAACvN,OAAO,EACZvB,KAAK,EACLuH,OAAO,EACPiU,gBAAgB,EAChB1a,QAAQ,EACR6V,sBAAsB,EACtBC,uBAAuB,EACvBC,qBAAqB,EACrBM,gBAAgB,EAChBD,gBAAgB,EAChByC,WAAW,EACXzS,QAAQ,EACRgT,iBAAiB,EACjBb,YAAY,CACb;IAED;IACA;IACA;IACAS,qBAAqB,CAClBmB,OAAO,IACN,EAAE1T,OAAO,IAAIA,OAAO,CAAC4C,IAAI,CAAEqL,CAAC,IAAKA,CAAC,CAACpP,KAAK,CAACO,EAAE,KAAKsU,OAAO,CAAC,CAAC,IACxDS,aAAa,IAAIA,aAAa,CAACvR,IAAI,CAAEqL,CAAC,IAAKA,CAAC,CAACpP,KAAK,CAACO,EAAE,KAAKsU,OAAO,CAAE,CACvE;IAEDjE,uBAAuB,GAAG,EAAED,kBAAkB;IAE9C;IACA,IAAI2E,aAAa,CAACvb,MAAM,KAAK,CAAC,IAAIwb,oBAAoB,CAACxb,MAAM,KAAK,CAAC,EAAE;MACnE,IAAI0b,eAAe,GAAGC,sBAAsB,EAAE;MAC9C3D,kBAAkB,CAACrX,QAAQ,EAAA+D,QAAA;QACzB0C,OAAO;QACP0O,UAAU,EAAE,EAAE;QACd;QACAE,MAAM,EAAEkD,YAAY,IAAI;MAAI,GACxBa,iBAAiB,GAAG;QAAEhE,UAAU,EAAEgE;MAAmB,IAAG,EAAE,EAC1D2B,eAAe,GAAG;QAAEzF,QAAQ,EAAE,IAAIC,GAAG,CAACrW,KAAK,CAACoW,QAAQ;MAAC,CAAE,GAAG,EAAE,CACjE,CAAC;MACF,OAAO;QAAEkE,cAAc,EAAE;OAAM;IAChC;IAED;IACA;IACA;IACA;IACA,IAAI,CAAC5D,2BAA2B,EAAE;MAChCiF,oBAAoB,CAACpT,OAAO,CAAEwT,EAAE,IAAI;QAClC,IAAIC,OAAO,GAAGhc,KAAK,CAACoW,QAAQ,CAACnF,GAAG,CAAC8K,EAAE,CAAClb,GAAG,CAAC;QACxC,IAAIob,mBAAmB,GAAGC,iBAAiB,CACzCjc,SAAS,EACT+b,OAAO,GAAGA,OAAO,CAACnN,IAAI,GAAG5O,SAAS,CACnC;QACDD,KAAK,CAACoW,QAAQ,CAAChH,GAAG,CAAC2M,EAAE,CAAClb,GAAG,EAAEob,mBAAmB,CAAC;MACjD,CAAC,CAAC;MACF,IAAI/F,UAAU,GAAGgE,iBAAiB,IAAIla,KAAK,CAACkW,UAAU;MACtD0B,WAAW,CAAA/S,QAAA;QACTgR,UAAU,EAAE+D;MAAiB,GACzB1D,UAAU,GACVjL,MAAM,CAACwN,IAAI,CAACvC,UAAU,CAAC,CAAC/V,MAAM,KAAK,CAAC,GAClC;QAAE+V,UAAU,EAAE;MAAM,IACpB;QAAEA;OAAY,GAChB,EAAE,EACFyF,oBAAoB,CAACxb,MAAM,GAAG,CAAC,GAC/B;QAAEiW,QAAQ,EAAE,IAAIC,GAAG,CAACrW,KAAK,CAACoW,QAAQ;MAAG,IACrC,EAAE,CACP,CAAC;IACH;IAEDuF,oBAAoB,CAACpT,OAAO,CAAEwT,EAAE,IAAI;MAClC,IAAIjF,gBAAgB,CAAC3H,GAAG,CAAC4M,EAAE,CAAClb,GAAG,CAAC,EAAE;QAChCsb,YAAY,CAACJ,EAAE,CAAClb,GAAG,CAAC;MACrB;MACD,IAAIkb,EAAE,CAAC7L,UAAU,EAAE;QACjB;QACA;QACA;QACA4G,gBAAgB,CAAC1H,GAAG,CAAC2M,EAAE,CAAClb,GAAG,EAAEkb,EAAE,CAAC7L,UAAU,CAAC;MAC5C;IACH,CAAC,CAAC;IAEF;IACA,IAAIkM,8BAA8B,GAAGA,CAAA,KACnCT,oBAAoB,CAACpT,OAAO,CAAE8T,CAAC,IAAKF,YAAY,CAACE,CAAC,CAACxb,GAAG,CAAC,CAAC;IAC1D,IAAI4V,2BAA2B,EAAE;MAC/BA,2BAA2B,CAACnG,MAAM,CAACxK,gBAAgB,CACjD,OAAO,EACPsW,8BAA8B,CAC/B;IACF;IAED,IAAI;MAAEE,OAAO;MAAEC,aAAa;MAAEC;IAAc,CAAE,GAC5C,MAAMC,8BAA8B,CAClCzc,KAAK,CAACuH,OAAO,EACbA,OAAO,EACPmU,aAAa,EACbC,oBAAoB,EACpB3B,OAAO,CACR;IAEH,IAAIA,OAAO,CAAC1J,MAAM,CAACY,OAAO,EAAE;MAC1B,OAAO;QAAEoJ,cAAc,EAAE;OAAM;IAChC;IAED;IACA;IACA;IACA,IAAI7D,2BAA2B,EAAE;MAC/BA,2BAA2B,CAACnG,MAAM,CAACvK,mBAAmB,CACpD,OAAO,EACPqW,8BAA8B,CAC/B;IACF;IACDT,oBAAoB,CAACpT,OAAO,CAAEwT,EAAE,IAAKjF,gBAAgB,CAAC3F,MAAM,CAAC4K,EAAE,CAAClb,GAAG,CAAC,CAAC;IAErE;IACA,IAAI4R,QAAQ,GAAGiK,YAAY,CAACJ,OAAO,CAAC;IACpC,IAAI7J,QAAQ,EAAE;MACZ,IAAIA,QAAQ,CAAC9N,GAAG,IAAI+W,aAAa,CAACvb,MAAM,EAAE;QACxC;QACA;QACA;QACA,IAAIwc,UAAU,GACZhB,oBAAoB,CAAClJ,QAAQ,CAAC9N,GAAG,GAAG+W,aAAa,CAACvb,MAAM,CAAC,CAACU,GAAG;QAC/DqW,gBAAgB,CAACxG,GAAG,CAACiM,UAAU,CAAC;MACjC;MACD,MAAMvB,uBAAuB,CAACpb,KAAK,EAAEyS,QAAQ,CAACrJ,MAAM,EAAE;QAAEhH;MAAS,EAAC;MAClE,OAAO;QAAEkY,cAAc,EAAE;OAAM;IAChC;IAED;IACA,IAAI;MAAErE,UAAU;MAAEE;IAAM,CAAE,GAAGyG,iBAAiB,CAC5C5c,KAAK,EACLuH,OAAO,EACPmU,aAAa,EACba,aAAa,EACblD,YAAY,EACZsC,oBAAoB,EACpBa,cAAc,EACdpF,eAAe,CAChB;IAED;IACAA,eAAe,CAAC7O,OAAO,CAAC,CAACsU,YAAY,EAAE5B,OAAO,KAAI;MAChD4B,YAAY,CAACrL,SAAS,CAAEN,OAAO,IAAI;QACjC;QACA;QACA;QACA,IAAIA,OAAO,IAAI2L,YAAY,CAACpM,IAAI,EAAE;UAChC2G,eAAe,CAACjG,MAAM,CAAC8J,OAAO,CAAC;QAChC;MACH,CAAC,CAAC;IACJ,CAAC,CAAC;IAEF,IAAIY,eAAe,GAAGC,sBAAsB,EAAE;IAC9C,IAAIgB,kBAAkB,GAAGC,oBAAoB,CAAC/F,uBAAuB,CAAC;IACtE,IAAIgG,oBAAoB,GACtBnB,eAAe,IAAIiB,kBAAkB,IAAInB,oBAAoB,CAACxb,MAAM,GAAG,CAAC;IAE1E,OAAA0E,QAAA;MACEoR,UAAU;MACVE;IAAM,GACF6G,oBAAoB,GAAG;MAAE5G,QAAQ,EAAE,IAAIC,GAAG,CAACrW,KAAK,CAACoW,QAAQ;KAAG,GAAG,EAAE;EAEzE;EAEA,SAAS6G,UAAUA,CAAcpc,GAAW;IAC1C,OAAOb,KAAK,CAACoW,QAAQ,CAACnF,GAAG,CAACpQ,GAAG,CAAC,IAAI6S,YAAY;EAChD;EAEA;EACA,SAASwJ,KAAKA,CACZrc,GAAW,EACXoa,OAAe,EACfzX,IAAmB,EACnBqV,IAAyB;IAEzB,IAAIvE,QAAQ,EAAE;MACZ,MAAM,IAAIpQ,KAAK,CACb,2EAA2E,GACzE,8EAA8E,GAC9E,6CAA6C,CAChD;IACF;IAED,IAAI4S,gBAAgB,CAAC3H,GAAG,CAACtO,GAAG,CAAC,EAAEsb,YAAY,CAACtb,GAAG,CAAC;IAEhD,IAAI8Y,WAAW,GAAGlF,kBAAkB,IAAID,UAAU;IAClD,IAAIsE,cAAc,GAAGC,WAAW,CAC9B/Y,KAAK,CAACc,QAAQ,EACdd,KAAK,CAACuH,OAAO,EACbL,QAAQ,EACRwN,MAAM,CAACE,kBAAkB,EACzBpR,IAAI,EACJyX,OAAO,EACPpC,IAAI,IAAJ,gBAAAA,IAAI,CAAEI,QAAQ,CACf;IACD,IAAI1R,OAAO,GAAGP,WAAW,CAAC2S,WAAW,EAAEb,cAAc,EAAE5R,QAAQ,CAAC;IAEhE,IAAI,CAACK,OAAO,EAAE;MACZ4V,eAAe,CACbtc,GAAG,EACHoa,OAAO,EACP5F,sBAAsB,CAAC,GAAG,EAAE;QAAErU,QAAQ,EAAE8X;MAAgB,EAAC,CAC1D;MACD;IACD;IAED,IAAI;MAAEnX,IAAI;MAAEuX,UAAU;MAAEzT;IAAK,CAAE,GAAG0T,wBAAwB,CACxDzE,MAAM,CAACC,sBAAsB,EAC7B,IAAI,EACJmE,cAAc,EACdD,IAAI,CACL;IAED,IAAIpT,KAAK,EAAE;MACT0X,eAAe,CAACtc,GAAG,EAAEoa,OAAO,EAAExV,KAAK,CAAC;MACpC;IACD;IAED,IAAIsF,KAAK,GAAG+P,cAAc,CAACvT,OAAO,EAAE5F,IAAI,CAAC;IAEzC6U,yBAAyB,GAAG,CAACqC,IAAI,IAAIA,IAAI,CAAC9C,kBAAkB,MAAM,IAAI;IAEtE,IAAImD,UAAU,IAAIX,gBAAgB,CAACW,UAAU,CAAC7F,UAAU,CAAC,EAAE;MACzD+J,mBAAmB,CAACvc,GAAG,EAAEoa,OAAO,EAAEtZ,IAAI,EAAEoJ,KAAK,EAAExD,OAAO,EAAE2R,UAAU,CAAC;MACnE;IACD;IAED;IACA;IACA/B,gBAAgB,CAAC/H,GAAG,CAACvO,GAAG,EAAE;MAAEoa,OAAO;MAAEtZ;IAAM,EAAC;IAC5C0b,mBAAmB,CAACxc,GAAG,EAAEoa,OAAO,EAAEtZ,IAAI,EAAEoJ,KAAK,EAAExD,OAAO,EAAE2R,UAAU,CAAC;EACrE;EAEA;EACA;EACA,eAAekE,mBAAmBA,CAChCvc,GAAW,EACXoa,OAAe,EACftZ,IAAY,EACZoJ,KAA6B,EAC7BuS,cAAwC,EACxCpE,UAAsB;IAEtBK,oBAAoB,EAAE;IACtBpC,gBAAgB,CAAChG,MAAM,CAACtQ,GAAG,CAAC;IAE5B,IAAI,CAACkK,KAAK,CAAC3E,KAAK,CAAChG,MAAM,IAAI,CAAC2K,KAAK,CAAC3E,KAAK,CAACqP,IAAI,EAAE;MAC5C,IAAIhQ,KAAK,GAAG4P,sBAAsB,CAAC,GAAG,EAAE;QACtC2F,MAAM,EAAE9B,UAAU,CAAC7F,UAAU;QAC7BrS,QAAQ,EAAEW,IAAI;QACdsZ,OAAO,EAAEA;MACV,EAAC;MACFkC,eAAe,CAACtc,GAAG,EAAEoa,OAAO,EAAExV,KAAK,CAAC;MACpC;IACD;IAED;IACA,IAAI8X,eAAe,GAAGvd,KAAK,CAACoW,QAAQ,CAACnF,GAAG,CAACpQ,GAAG,CAAC;IAC7C,IAAImb,OAAO,GAAGwB,oBAAoB,CAACtE,UAAU,EAAEqE,eAAe,CAAC;IAC/Dvd,KAAK,CAACoW,QAAQ,CAAChH,GAAG,CAACvO,GAAG,EAAEmb,OAAO,CAAC;IAChCpE,WAAW,CAAC;MAAExB,QAAQ,EAAE,IAAIC,GAAG,CAACrW,KAAK,CAACoW,QAAQ;IAAC,CAAE,CAAC;IAElD;IACA,IAAIqH,eAAe,GAAG,IAAItN,eAAe,EAAE;IAC3C,IAAIuN,YAAY,GAAGzD,uBAAuB,CACxCnL,IAAI,CAACvN,OAAO,EACZI,IAAI,EACJ8b,eAAe,CAACnN,MAAM,EACtB4I,UAAU,CACX;IACDpC,gBAAgB,CAAC1H,GAAG,CAACvO,GAAG,EAAE4c,eAAe,CAAC;IAE1C,IAAIE,iBAAiB,GAAG5G,kBAAkB;IAC1C,IAAI6G,YAAY,GAAG,MAAM1C,kBAAkB,CACzC,QAAQ,EACRwC,YAAY,EACZ3S,KAAK,EACLuS,cAAc,EACd7W,QAAQ,EACRF,kBAAkB,EAClBW,QAAQ,CACT;IAED,IAAIwW,YAAY,CAACpN,MAAM,CAACY,OAAO,EAAE;MAC/B;MACA;MACA,IAAI4F,gBAAgB,CAAC7F,GAAG,CAACpQ,GAAG,CAAC,KAAK4c,eAAe,EAAE;QACjD3G,gBAAgB,CAAC3F,MAAM,CAACtQ,GAAG,CAAC;MAC7B;MACD;IACD;IAED,IAAIsa,gBAAgB,CAACyC,YAAY,CAAC,EAAE;MAClC9G,gBAAgB,CAAC3F,MAAM,CAACtQ,GAAG,CAAC;MAC5B,IAAImW,uBAAuB,GAAG2G,iBAAiB,EAAE;QAC/C;QACA;QACA;QACA;QACA,IAAIE,WAAW,GAAGC,cAAc,CAAC7d,SAAS,CAAC;QAC3CD,KAAK,CAACoW,QAAQ,CAAChH,GAAG,CAACvO,GAAG,EAAEgd,WAAW,CAAC;QACpCjG,WAAW,CAAC;UAAExB,QAAQ,EAAE,IAAIC,GAAG,CAACrW,KAAK,CAACoW,QAAQ;QAAC,CAAE,CAAC;QAClD;MACD,OAAM;QACLc,gBAAgB,CAACxG,GAAG,CAAC7P,GAAG,CAAC;QACzB,IAAIkd,cAAc,GAAG7B,iBAAiB,CAAChD,UAAU,CAAC;QAClDlZ,KAAK,CAACoW,QAAQ,CAAChH,GAAG,CAACvO,GAAG,EAAEkd,cAAc,CAAC;QACvCnG,WAAW,CAAC;UAAExB,QAAQ,EAAE,IAAIC,GAAG,CAACrW,KAAK,CAACoW,QAAQ;QAAC,CAAE,CAAC;QAElD,OAAOgF,uBAAuB,CAACpb,KAAK,EAAE4d,YAAY,EAAE;UAClD1E,UAAU;UACV8E,qBAAqB,EAAE;QACxB,EAAC;MACH;IACF;IAED;IACA,IAAI3C,aAAa,CAACuC,YAAY,CAAC,EAAE;MAC/BT,eAAe,CAACtc,GAAG,EAAEoa,OAAO,EAAE2C,YAAY,CAACnY,KAAK,CAAC;MACjD;IACD;IAED,IAAI8V,gBAAgB,CAACqC,YAAY,CAAC,EAAE;MAClC,MAAMvI,sBAAsB,CAAC,GAAG,EAAE;QAAE0F,IAAI,EAAE;MAAgB,EAAC;IAC5D;IAED;IACA;IACA,IAAI9Y,YAAY,GAAGjC,KAAK,CAAC6V,UAAU,CAAC/U,QAAQ,IAAId,KAAK,CAACc,QAAQ;IAC9D,IAAImd,mBAAmB,GAAGhE,uBAAuB,CAC/CnL,IAAI,CAACvN,OAAO,EACZU,YAAY,EACZwb,eAAe,CAACnN,MAAM,CACvB;IACD,IAAIqJ,WAAW,GAAGlF,kBAAkB,IAAID,UAAU;IAClD,IAAIjN,OAAO,GACTvH,KAAK,CAAC6V,UAAU,CAAC7V,KAAK,KAAK,MAAM,GAC7BgH,WAAW,CAAC2S,WAAW,EAAE3Z,KAAK,CAAC6V,UAAU,CAAC/U,QAAQ,EAAEoG,QAAQ,CAAC,GAC7DlH,KAAK,CAACuH,OAAO;IAEnBxD,SAAS,CAACwD,OAAO,EAAE,8CAA8C,CAAC;IAElE,IAAI2W,MAAM,GAAG,EAAEnH,kBAAkB;IACjCE,cAAc,CAAC7H,GAAG,CAACvO,GAAG,EAAEqd,MAAM,CAAC;IAE/B,IAAIC,WAAW,GAAGjC,iBAAiB,CAAChD,UAAU,EAAE0E,YAAY,CAAC/O,IAAI,CAAC;IAClE7O,KAAK,CAACoW,QAAQ,CAAChH,GAAG,CAACvO,GAAG,EAAEsd,WAAW,CAAC;IAEpC,IAAI,CAACzC,aAAa,EAAEC,oBAAoB,CAAC,GAAGC,gBAAgB,CAC1D9M,IAAI,CAACvN,OAAO,EACZvB,KAAK,EACLuH,OAAO,EACP2R,UAAU,EACVjX,YAAY,EACZ0U,sBAAsB,EACtBC,uBAAuB,EACvBC,qBAAqB,EACrBM,gBAAgB,EAChBD,gBAAgB,EAChByC,WAAW,EACXzS,QAAQ,EACR;MAAE,CAAC6D,KAAK,CAAC3E,KAAK,CAACO,EAAE,GAAGiX,YAAY,CAAC/O;KAAM,EACvC5O,SAAS;KACV;IAED;IACA;IACA;IACA0b,oBAAoB,CACjBvR,MAAM,CAAE2R,EAAE,IAAKA,EAAE,CAAClb,GAAG,KAAKA,GAAG,CAAC,CAC9B0H,OAAO,CAAEwT,EAAE,IAAI;MACd,IAAIqC,QAAQ,GAAGrC,EAAE,CAAClb,GAAG;MACrB,IAAI0c,eAAe,GAAGvd,KAAK,CAACoW,QAAQ,CAACnF,GAAG,CAACmN,QAAQ,CAAC;MAClD,IAAInC,mBAAmB,GAAGC,iBAAiB,CACzCjc,SAAS,EACTsd,eAAe,GAAGA,eAAe,CAAC1O,IAAI,GAAG5O,SAAS,CACnD;MACDD,KAAK,CAACoW,QAAQ,CAAChH,GAAG,CAACgP,QAAQ,EAAEnC,mBAAmB,CAAC;MACjD,IAAInF,gBAAgB,CAAC3H,GAAG,CAACiP,QAAQ,CAAC,EAAE;QAClCjC,YAAY,CAACiC,QAAQ,CAAC;MACvB;MACD,IAAIrC,EAAE,CAAC7L,UAAU,EAAE;QACjB4G,gBAAgB,CAAC1H,GAAG,CAACgP,QAAQ,EAAErC,EAAE,CAAC7L,UAAU,CAAC;MAC9C;IACH,CAAC,CAAC;IAEJ0H,WAAW,CAAC;MAAExB,QAAQ,EAAE,IAAIC,GAAG,CAACrW,KAAK,CAACoW,QAAQ;IAAC,CAAE,CAAC;IAElD,IAAIgG,8BAA8B,GAAGA,CAAA,KACnCT,oBAAoB,CAACpT,OAAO,CAAEwT,EAAE,IAAKI,YAAY,CAACJ,EAAE,CAAClb,GAAG,CAAC,CAAC;IAE5D4c,eAAe,CAACnN,MAAM,CAACxK,gBAAgB,CACrC,OAAO,EACPsW,8BAA8B,CAC/B;IAED,IAAI;MAAEE,OAAO;MAAEC,aAAa;MAAEC;IAAc,CAAE,GAC5C,MAAMC,8BAA8B,CAClCzc,KAAK,CAACuH,OAAO,EACbA,OAAO,EACPmU,aAAa,EACbC,oBAAoB,EACpBsC,mBAAmB,CACpB;IAEH,IAAIR,eAAe,CAACnN,MAAM,CAACY,OAAO,EAAE;MAClC;IACD;IAEDuM,eAAe,CAACnN,MAAM,CAACvK,mBAAmB,CACxC,OAAO,EACPqW,8BAA8B,CAC/B;IAEDnF,cAAc,CAAC9F,MAAM,CAACtQ,GAAG,CAAC;IAC1BiW,gBAAgB,CAAC3F,MAAM,CAACtQ,GAAG,CAAC;IAC5B8a,oBAAoB,CAACpT,OAAO,CAAE0H,CAAC,IAAK6G,gBAAgB,CAAC3F,MAAM,CAAClB,CAAC,CAACpP,GAAG,CAAC,CAAC;IAEnE,IAAI4R,QAAQ,GAAGiK,YAAY,CAACJ,OAAO,CAAC;IACpC,IAAI7J,QAAQ,EAAE;MACZ,IAAIA,QAAQ,CAAC9N,GAAG,IAAI+W,aAAa,CAACvb,MAAM,EAAE;QACxC;QACA;QACA;QACA,IAAIwc,UAAU,GACZhB,oBAAoB,CAAClJ,QAAQ,CAAC9N,GAAG,GAAG+W,aAAa,CAACvb,MAAM,CAAC,CAACU,GAAG;QAC/DqW,gBAAgB,CAACxG,GAAG,CAACiM,UAAU,CAAC;MACjC;MACD,OAAOvB,uBAAuB,CAACpb,KAAK,EAAEyS,QAAQ,CAACrJ,MAAM,CAAC;IACvD;IAED;IACA,IAAI;MAAE6M,UAAU;MAAEE;KAAQ,GAAGyG,iBAAiB,CAC5C5c,KAAK,EACLA,KAAK,CAACuH,OAAO,EACbmU,aAAa,EACba,aAAa,EACbtc,SAAS,EACT0b,oBAAoB,EACpBa,cAAc,EACdpF,eAAe,CAChB;IAED;IACA;IACA,IAAIpX,KAAK,CAACoW,QAAQ,CAACjH,GAAG,CAACtO,GAAG,CAAC,EAAE;MAC3B,IAAIgd,WAAW,GAAGC,cAAc,CAACF,YAAY,CAAC/O,IAAI,CAAC;MACnD7O,KAAK,CAACoW,QAAQ,CAAChH,GAAG,CAACvO,GAAG,EAAEgd,WAAW,CAAC;IACrC;IAED,IAAIf,kBAAkB,GAAGC,oBAAoB,CAACmB,MAAM,CAAC;IAErD;IACA;IACA;IACA,IACEle,KAAK,CAAC6V,UAAU,CAAC7V,KAAK,KAAK,SAAS,IACpCke,MAAM,GAAGlH,uBAAuB,EAChC;MACAjT,SAAS,CAACwS,aAAa,EAAE,yBAAyB,CAAC;MACnDE,2BAA2B,IAAIA,2BAA2B,CAAC/E,KAAK,EAAE;MAElEyG,kBAAkB,CAACnY,KAAK,CAAC6V,UAAU,CAAC/U,QAAQ,EAAE;QAC5CyG,OAAO;QACP0O,UAAU;QACVE,MAAM;QACNC,QAAQ,EAAE,IAAIC,GAAG,CAACrW,KAAK,CAACoW,QAAQ;MACjC,EAAC;IACH,OAAM;MACL;MACA;MACA;MACAwB,WAAW,CAAA/S,QAAA;QACTsR,MAAM;QACNF,UAAU,EAAEyC,eAAe,CACzB1Y,KAAK,CAACiW,UAAU,EAChBA,UAAU,EACV1O,OAAO,EACP4O,MAAM;MACP,GACG2G,kBAAkB,IAAInB,oBAAoB,CAACxb,MAAM,GAAG,CAAC,GACrD;QAAEiW,QAAQ,EAAE,IAAIC,GAAG,CAACrW,KAAK,CAACoW,QAAQ;MAAG,IACrC,EAAE,CACP,CAAC;MACFO,sBAAsB,GAAG,KAAK;IAC/B;EACH;EAEA;EACA,eAAe0G,mBAAmBA,CAChCxc,GAAW,EACXoa,OAAe,EACftZ,IAAY,EACZoJ,KAA6B,EAC7BxD,OAAiC,EACjC2R,UAAuB;IAEvB,IAAIqE,eAAe,GAAGvd,KAAK,CAACoW,QAAQ,CAACnF,GAAG,CAACpQ,GAAG,CAAC;IAC7C;IACA,IAAIkd,cAAc,GAAG7B,iBAAiB,CACpChD,UAAU,EACVqE,eAAe,GAAGA,eAAe,CAAC1O,IAAI,GAAG5O,SAAS,CACnD;IACDD,KAAK,CAACoW,QAAQ,CAAChH,GAAG,CAACvO,GAAG,EAAEkd,cAAc,CAAC;IACvCnG,WAAW,CAAC;MAAExB,QAAQ,EAAE,IAAIC,GAAG,CAACrW,KAAK,CAACoW,QAAQ;IAAC,CAAE,CAAC;IAElD;IACA,IAAIqH,eAAe,GAAG,IAAItN,eAAe,EAAE;IAC3C,IAAIuN,YAAY,GAAGzD,uBAAuB,CACxCnL,IAAI,CAACvN,OAAO,EACZI,IAAI,EACJ8b,eAAe,CAACnN,MAAM,CACvB;IACDwG,gBAAgB,CAAC1H,GAAG,CAACvO,GAAG,EAAE4c,eAAe,CAAC;IAE1C,IAAIE,iBAAiB,GAAG5G,kBAAkB;IAC1C,IAAI3N,MAAM,GAAe,MAAM8R,kBAAkB,CAC/C,QAAQ,EACRwC,YAAY,EACZ3S,KAAK,EACLxD,OAAO,EACPd,QAAQ,EACRF,kBAAkB,EAClBW,QAAQ,CACT;IAED;IACA;IACA;IACA;IACA,IAAIqU,gBAAgB,CAACnS,MAAM,CAAC,EAAE;MAC5BA,MAAM,GACJ,CAAC,MAAMiV,mBAAmB,CAACjV,MAAM,EAAEsU,YAAY,CAACpN,MAAM,EAAE,IAAI,CAAC,KAC7DlH,MAAM;IACT;IAED;IACA;IACA,IAAI0N,gBAAgB,CAAC7F,GAAG,CAACpQ,GAAG,CAAC,KAAK4c,eAAe,EAAE;MACjD3G,gBAAgB,CAAC3F,MAAM,CAACtQ,GAAG,CAAC;IAC7B;IAED,IAAI6c,YAAY,CAACpN,MAAM,CAACY,OAAO,EAAE;MAC/B;IACD;IAED;IACA,IAAIiK,gBAAgB,CAAC/R,MAAM,CAAC,EAAE;MAC5B,IAAI4N,uBAAuB,GAAG2G,iBAAiB,EAAE;QAC/C;QACA;QACA,IAAIE,WAAW,GAAGC,cAAc,CAAC7d,SAAS,CAAC;QAC3CD,KAAK,CAACoW,QAAQ,CAAChH,GAAG,CAACvO,GAAG,EAAEgd,WAAW,CAAC;QACpCjG,WAAW,CAAC;UAAExB,QAAQ,EAAE,IAAIC,GAAG,CAACrW,KAAK,CAACoW,QAAQ;QAAC,CAAE,CAAC;QAClD;MACD,OAAM;QACLc,gBAAgB,CAACxG,GAAG,CAAC7P,GAAG,CAAC;QACzB,MAAMua,uBAAuB,CAACpb,KAAK,EAAEoJ,MAAM,CAAC;QAC5C;MACD;IACF;IAED;IACA,IAAIiS,aAAa,CAACjS,MAAM,CAAC,EAAE;MACzB,IAAIkS,aAAa,GAAGnB,mBAAmB,CAACna,KAAK,CAACuH,OAAO,EAAE0T,OAAO,CAAC;MAC/Djb,KAAK,CAACoW,QAAQ,CAACjF,MAAM,CAACtQ,GAAG,CAAC;MAC1B;MACA;MACA;MACA+W,WAAW,CAAC;QACVxB,QAAQ,EAAE,IAAIC,GAAG,CAACrW,KAAK,CAACoW,QAAQ,CAAC;QACjCD,MAAM,EAAE;UACN,CAACmF,aAAa,CAAClV,KAAK,CAACO,EAAE,GAAGyC,MAAM,CAAC3D;QAClC;MACF,EAAC;MACF;IACD;IAED1B,SAAS,CAAC,CAACwX,gBAAgB,CAACnS,MAAM,CAAC,EAAE,iCAAiC,CAAC;IAEvE;IACA,IAAIyU,WAAW,GAAGC,cAAc,CAAC1U,MAAM,CAACyF,IAAI,CAAC;IAC7C7O,KAAK,CAACoW,QAAQ,CAAChH,GAAG,CAACvO,GAAG,EAAEgd,WAAW,CAAC;IACpCjG,WAAW,CAAC;MAAExB,QAAQ,EAAE,IAAIC,GAAG,CAACrW,KAAK,CAACoW,QAAQ;IAAC,CAAE,CAAC;EACpD;EAEA;;;;;;;;;;;;;;;;;;AAkBG;EACH,eAAegF,uBAAuBA,CACpCpb,KAAkB,EAClByS,QAAwB,EAAA6L,KAAA,EASlB;IAAA,IARN;MACEpF,UAAU;MACV9W,OAAO;MACP4b;2BAKE,EAAE,GAAAM,KAAA;IAEN,IAAI7L,QAAQ,CAAC6G,UAAU,EAAE;MACvB3C,sBAAsB,GAAG,IAAI;IAC9B;IAED,IAAI4H,gBAAgB,GAAGxd,cAAc,CACnCf,KAAK,CAACc,QAAQ,EACd2R,QAAQ,CAAC3R,QAAQ;IAAA;IACjB+D,QAAA;MAEE2T,WAAW,EAAE;IAAI,GACbwF,qBAAqB,GAAG;MAAEQ,sBAAsB,EAAE;IAAM,IAAG,EAAE,CAClE,CACF;IACDza,SAAS,CACPwa,gBAAgB,EAChB,gDAAgD,CACjD;IACD;IACA,IAAIzK,kBAAkB,CAACvJ,IAAI,CAACkI,QAAQ,CAAC3R,QAAQ,CAAC,IAAIsT,SAAS,EAAE;MAC3D,IAAI1Q,GAAG,GAAGoL,IAAI,CAACvN,OAAO,CAACC,SAAS,CAACiR,QAAQ,CAAC3R,QAAQ,CAAC;MACnD,IAAI2d,mBAAmB,GAAGtX,aAAa,CAACzD,GAAG,CAAC1C,QAAQ,EAAEkG,QAAQ,CAAC,IAAI,IAAI;MAEvE,IAAIiN,YAAY,CAACrT,QAAQ,CAAC+E,MAAM,KAAKnC,GAAG,CAACmC,MAAM,IAAI4Y,mBAAmB,EAAE;QACtE,IAAIrc,OAAO,EAAE;UACX+R,YAAY,CAACrT,QAAQ,CAACsB,OAAO,CAACqQ,QAAQ,CAAC3R,QAAQ,CAAC;QACjD,OAAM;UACLqT,YAAY,CAACrT,QAAQ,CAAC8E,MAAM,CAAC6M,QAAQ,CAAC3R,QAAQ,CAAC;QAChD;QACD;MACD;IACF;IAED;IACA;IACA2V,2BAA2B,GAAG,IAAI;IAElC,IAAIiI,qBAAqB,GACvBtc,OAAO,KAAK,IAAI,GAAGhD,MAAa,CAACiD,OAAO,GAAGjD,MAAa,CAAC4C,IAAI;IAE/D;IACA;IACA,IAAIwZ,gBAAgB,GAClBtC,UAAU,IAAIuC,2BAA2B,CAACzb,KAAK,CAAC6V,UAAU,CAAC;IAE7D;IACA;IACA;IACA,IACE1C,iCAAiC,CAAChE,GAAG,CAACsD,QAAQ,CAACzD,MAAM,CAAC,IACtDwM,gBAAgB,IAChBjD,gBAAgB,CAACiD,gBAAgB,CAACnI,UAAU,CAAC,EAC7C;MACA,MAAMwE,eAAe,CAAC6G,qBAAqB,EAAEH,gBAAgB,EAAE;QAC7DrF,UAAU,EAAArU,QAAA,KACL2W,gBAAgB;UACnBlI,UAAU,EAAEb,QAAQ,CAAC3R;SACtB;QACD;QACAiV,kBAAkB,EAAES;MACrB,EAAC;KACH,MAAM,IAAIwH,qBAAqB,EAAE;MAChC;MACA;MACA,MAAMnG,eAAe,CAAC6G,qBAAqB,EAAEH,gBAAgB,EAAE;QAC7D9E,kBAAkB,EAAEe,oBAAoB,CAAC+D,gBAAgB,CAAC;QAC1D5D,iBAAiB,EAAEa,gBAAgB;QACnC;QACAzF,kBAAkB,EAAES;MACrB,EAAC;IACH,OAAM;MACL;MACA,IAAIiD,kBAAkB,GAAGe,oBAAoB,CAC3C+D,gBAAgB,EAChB/C,gBAAgB,CACjB;MACD,MAAM3D,eAAe,CAAC6G,qBAAqB,EAAEH,gBAAgB,EAAE;QAC7D9E,kBAAkB;QAClB;QACA1D,kBAAkB,EAAES;MACrB,EAAC;IACH;EACH;EAEA,eAAeiG,8BAA8BA,CAC3CkC,cAAwC,EACxCpX,OAAiC,EACjCmU,aAAuC,EACvCkD,cAAqC,EACrC5E,OAAgB;IAEhB;IACA;IACA;IACA,IAAIsC,OAAO,GAAG,MAAMtM,OAAO,CAAC6O,GAAG,CAAC,CAC9B,GAAGnD,aAAa,CAAC9b,GAAG,CAAEmL,KAAK,IACzBmQ,kBAAkB,CAChB,QAAQ,EACRlB,OAAO,EACPjP,KAAK,EACLxD,OAAO,EACPd,QAAQ,EACRF,kBAAkB,EAClBW,QAAQ,CACT,CACF,EACD,GAAG0X,cAAc,CAAChf,GAAG,CAAEyc,CAAC,IAAI;MAC1B,IAAIA,CAAC,CAAC9U,OAAO,IAAI8U,CAAC,CAACtR,KAAK,IAAIsR,CAAC,CAACnM,UAAU,EAAE;QACxC,OAAOgL,kBAAkB,CACvB,QAAQ,EACRjB,uBAAuB,CAACnL,IAAI,CAACvN,OAAO,EAAE8a,CAAC,CAAC1a,IAAI,EAAE0a,CAAC,CAACnM,UAAU,CAACI,MAAM,CAAC,EAClE+L,CAAC,CAACtR,KAAK,EACPsR,CAAC,CAAC9U,OAAO,EACTd,QAAQ,EACRF,kBAAkB,EAClBW,QAAQ,CACT;MACF,OAAM;QACL,IAAIzB,KAAK,GAAgB;UACvBsV,IAAI,EAAE/U,UAAU,CAACP,KAAK;UACtBA,KAAK,EAAE4P,sBAAsB,CAAC,GAAG,EAAE;YAAErU,QAAQ,EAAEqb,CAAC,CAAC1a;WAAM;SACxD;QACD,OAAO8D,KAAK;MACb;KACF,CAAC,CACH,CAAC;IACF,IAAI8W,aAAa,GAAGD,OAAO,CAACzY,KAAK,CAAC,CAAC,EAAE6X,aAAa,CAACvb,MAAM,CAAC;IAC1D,IAAIqc,cAAc,GAAGF,OAAO,CAACzY,KAAK,CAAC6X,aAAa,CAACvb,MAAM,CAAC;IAExD,MAAM6P,OAAO,CAAC6O,GAAG,CAAC,CAChBC,sBAAsB,CACpBH,cAAc,EACdjD,aAAa,EACba,aAAa,EACbA,aAAa,CAAC3c,GAAG,CAAC,MAAMoa,OAAO,CAAC1J,MAAM,CAAC,EACvC,KAAK,EACLtQ,KAAK,CAACiW,UAAU,CACjB,EACD6I,sBAAsB,CACpBH,cAAc,EACdC,cAAc,CAAChf,GAAG,CAAEyc,CAAC,IAAKA,CAAC,CAACtR,KAAK,CAAC,EAClCyR,cAAc,EACdoC,cAAc,CAAChf,GAAG,CAAEyc,CAAC,IAAMA,CAAC,CAACnM,UAAU,GAAGmM,CAAC,CAACnM,UAAU,CAACI,MAAM,GAAG,IAAK,CAAC,EACtE,IAAI,CACL,CACF,CAAC;IAEF,OAAO;MAAEgM,OAAO;MAAEC,aAAa;MAAEC;KAAgB;EACnD;EAEA,SAASjD,oBAAoBA,CAAA;IAC3B;IACA5C,sBAAsB,GAAG,IAAI;IAE7B;IACA;IACAC,uBAAuB,CAAC7U,IAAI,CAAC,GAAG+X,qBAAqB,EAAE,CAAC;IAExD;IACA3C,gBAAgB,CAAC5O,OAAO,CAAC,CAACmE,CAAC,EAAE7L,GAAG,KAAI;MAClC,IAAIiW,gBAAgB,CAAC3H,GAAG,CAACtO,GAAG,CAAC,EAAE;QAC7BgW,qBAAqB,CAAC9U,IAAI,CAAClB,GAAG,CAAC;QAC/Bsb,YAAY,CAACtb,GAAG,CAAC;MAClB;IACH,CAAC,CAAC;EACJ;EAEA,SAASsc,eAAeA,CAACtc,GAAW,EAAEoa,OAAe,EAAExV,KAAU;IAC/D,IAAI6V,aAAa,GAAGnB,mBAAmB,CAACna,KAAK,CAACuH,OAAO,EAAE0T,OAAO,CAAC;IAC/DjD,aAAa,CAACnX,GAAG,CAAC;IAClB+W,WAAW,CAAC;MACVzB,MAAM,EAAE;QACN,CAACmF,aAAa,CAAClV,KAAK,CAACO,EAAE,GAAGlB;OAC3B;MACD2Q,QAAQ,EAAE,IAAIC,GAAG,CAACrW,KAAK,CAACoW,QAAQ;IACjC,EAAC;EACJ;EAEA,SAAS4B,aAAaA,CAACnX,GAAW;IAChC,IAAImb,OAAO,GAAGhc,KAAK,CAACoW,QAAQ,CAACnF,GAAG,CAACpQ,GAAG,CAAC;IACrC;IACA;IACA;IACA,IACEiW,gBAAgB,CAAC3H,GAAG,CAACtO,GAAG,CAAC,IACzB,EAAEmb,OAAO,IAAIA,OAAO,CAAChc,KAAK,KAAK,SAAS,IAAIiX,cAAc,CAAC9H,GAAG,CAACtO,GAAG,CAAC,CAAC,EACpE;MACAsb,YAAY,CAACtb,GAAG,CAAC;IAClB;IACDsW,gBAAgB,CAAChG,MAAM,CAACtQ,GAAG,CAAC;IAC5BoW,cAAc,CAAC9F,MAAM,CAACtQ,GAAG,CAAC;IAC1BqW,gBAAgB,CAAC/F,MAAM,CAACtQ,GAAG,CAAC;IAC5Bb,KAAK,CAACoW,QAAQ,CAACjF,MAAM,CAACtQ,GAAG,CAAC;EAC5B;EAEA,SAASsb,YAAYA,CAACtb,GAAW;IAC/B,IAAIqP,UAAU,GAAG4G,gBAAgB,CAAC7F,GAAG,CAACpQ,GAAG,CAAC;IAC1CkD,SAAS,CAACmM,UAAU,EAAgC,gCAAArP,GAAK,CAAC;IAC1DqP,UAAU,CAACwB,KAAK,EAAE;IAClBoF,gBAAgB,CAAC3F,MAAM,CAACtQ,GAAG,CAAC;EAC9B;EAEA,SAASke,gBAAgBA,CAACtG,IAAc;IACtC,KAAK,IAAI5X,GAAG,IAAI4X,IAAI,EAAE;MACpB,IAAIuD,OAAO,GAAGiB,UAAU,CAACpc,GAAG,CAAC;MAC7B,IAAIgd,WAAW,GAAGC,cAAc,CAAC9B,OAAO,CAACnN,IAAI,CAAC;MAC9C7O,KAAK,CAACoW,QAAQ,CAAChH,GAAG,CAACvO,GAAG,EAAEgd,WAAW,CAAC;IACrC;EACH;EAEA,SAAS/B,sBAAsBA,CAAA;IAC7B,IAAIkD,QAAQ,GAAG,EAAE;IACjB,IAAInD,eAAe,GAAG,KAAK;IAC3B,KAAK,IAAIhb,GAAG,IAAIqW,gBAAgB,EAAE;MAChC,IAAI8E,OAAO,GAAGhc,KAAK,CAACoW,QAAQ,CAACnF,GAAG,CAACpQ,GAAG,CAAC;MACrCkD,SAAS,CAACiY,OAAO,EAAuB,uBAAAnb,GAAK,CAAC;MAC9C,IAAImb,OAAO,CAAChc,KAAK,KAAK,SAAS,EAAE;QAC/BkX,gBAAgB,CAAC/F,MAAM,CAACtQ,GAAG,CAAC;QAC5Bme,QAAQ,CAACjd,IAAI,CAAClB,GAAG,CAAC;QAClBgb,eAAe,GAAG,IAAI;MACvB;IACF;IACDkD,gBAAgB,CAACC,QAAQ,CAAC;IAC1B,OAAOnD,eAAe;EACxB;EAEA,SAASkB,oBAAoBA,CAACkC,QAAgB;IAC5C,IAAIC,UAAU,GAAG,EAAE;IACnB,KAAK,IAAI,CAACre,GAAG,EAAE8F,EAAE,CAAC,IAAIsQ,cAAc,EAAE;MACpC,IAAItQ,EAAE,GAAGsY,QAAQ,EAAE;QACjB,IAAIjD,OAAO,GAAGhc,KAAK,CAACoW,QAAQ,CAACnF,GAAG,CAACpQ,GAAG,CAAC;QACrCkD,SAAS,CAACiY,OAAO,EAAuB,uBAAAnb,GAAK,CAAC;QAC9C,IAAImb,OAAO,CAAChc,KAAK,KAAK,SAAS,EAAE;UAC/Bmc,YAAY,CAACtb,GAAG,CAAC;UACjBoW,cAAc,CAAC9F,MAAM,CAACtQ,GAAG,CAAC;UAC1Bqe,UAAU,CAACnd,IAAI,CAAClB,GAAG,CAAC;QACrB;MACF;IACF;IACDke,gBAAgB,CAACG,UAAU,CAAC;IAC5B,OAAOA,UAAU,CAAC/e,MAAM,GAAG,CAAC;EAC9B;EAEA,SAASgf,UAAUA,CAACte,GAAW,EAAE4B,EAAmB;IAClD,IAAI2c,OAAO,GAAYpf,KAAK,CAACsW,QAAQ,CAACrF,GAAG,CAACpQ,GAAG,CAAC,IAAI8S,YAAY;IAE9D,IAAI0D,gBAAgB,CAACpG,GAAG,CAACpQ,GAAG,CAAC,KAAK4B,EAAE,EAAE;MACpC4U,gBAAgB,CAACjI,GAAG,CAACvO,GAAG,EAAE4B,EAAE,CAAC;IAC9B;IAED,OAAO2c,OAAO;EAChB;EAEA,SAASnH,aAAaA,CAACpX,GAAW;IAChCb,KAAK,CAACsW,QAAQ,CAACnF,MAAM,CAACtQ,GAAG,CAAC;IAC1BwW,gBAAgB,CAAClG,MAAM,CAACtQ,GAAG,CAAC;EAC9B;EAEA;EACA,SAAS8W,aAAaA,CAAC9W,GAAW,EAAEwe,UAAmB;IACrD,IAAID,OAAO,GAAGpf,KAAK,CAACsW,QAAQ,CAACrF,GAAG,CAACpQ,GAAG,CAAC,IAAI8S,YAAY;IAErD;IACA;IACA5P,SAAS,CACNqb,OAAO,CAACpf,KAAK,KAAK,WAAW,IAAIqf,UAAU,CAACrf,KAAK,KAAK,SAAS,IAC7Dof,OAAO,CAACpf,KAAK,KAAK,SAAS,IAAIqf,UAAU,CAACrf,KAAK,KAAK,SAAU,IAC9Dof,OAAO,CAACpf,KAAK,KAAK,SAAS,IAAIqf,UAAU,CAACrf,KAAK,KAAK,YAAa,IACjEof,OAAO,CAACpf,KAAK,KAAK,SAAS,IAAIqf,UAAU,CAACrf,KAAK,KAAK,WAAY,IAChEof,OAAO,CAACpf,KAAK,KAAK,YAAY,IAAIqf,UAAU,CAACrf,KAAK,KAAK,WAAY,yCACjCof,OAAO,CAACpf,KAAK,YAAOqf,UAAU,CAACrf,KAAO,CAC5E;IAED,IAAIsW,QAAQ,GAAG,IAAID,GAAG,CAACrW,KAAK,CAACsW,QAAQ,CAAC;IACtCA,QAAQ,CAAClH,GAAG,CAACvO,GAAG,EAAEwe,UAAU,CAAC;IAC7BzH,WAAW,CAAC;MAAEtB;IAAQ,CAAE,CAAC;EAC3B;EAEA,SAASmB,qBAAqBA,CAAAxF,KAAA,EAQ7B;IAAA,IAR8B;MAC7ByF,eAAe;MACfzV,YAAY;MACZ2T;IAKD,IAAA3D,KAAA;IACC,IAAIoF,gBAAgB,CAACtF,IAAI,KAAK,CAAC,EAAE;MAC/B;IACD;IAED;IACA;IACA,IAAIsF,gBAAgB,CAACtF,IAAI,GAAG,CAAC,EAAE;MAC7B9Q,OAAO,CAAC,KAAK,EAAE,8CAA8C,CAAC;IAC/D;IAED,IAAItB,OAAO,GAAGiQ,KAAK,CAACxB,IAAI,CAACiJ,gBAAgB,CAAC1X,OAAO,EAAE,CAAC;IACpD,IAAI,CAAC6X,UAAU,EAAE8H,eAAe,CAAC,GAAG3f,OAAO,CAACA,OAAO,CAACQ,MAAM,GAAG,CAAC,CAAC;IAC/D,IAAIif,OAAO,GAAGpf,KAAK,CAACsW,QAAQ,CAACrF,GAAG,CAACuG,UAAU,CAAC;IAE5C,IAAI4H,OAAO,IAAIA,OAAO,CAACpf,KAAK,KAAK,YAAY,EAAE;MAC7C;MACA;MACA;IACD;IAED;IACA;IACA,IAAIsf,eAAe,CAAC;MAAE5H,eAAe;MAAEzV,YAAY;MAAE2T;IAAe,EAAC,EAAE;MACrE,OAAO4B,UAAU;IAClB;EACH;EAEA,SAASsC,qBAAqBA,CAC5ByF,SAAwC;IAExC,IAAIC,iBAAiB,GAAa,EAAE;IACpCpI,eAAe,CAAC7O,OAAO,CAAC,CAACkX,GAAG,EAAExE,OAAO,KAAI;MACvC,IAAI,CAACsE,SAAS,IAAIA,SAAS,CAACtE,OAAO,CAAC,EAAE;QACpC;QACA;QACA;QACAwE,GAAG,CAAChO,MAAM,EAAE;QACZ+N,iBAAiB,CAACzd,IAAI,CAACkZ,OAAO,CAAC;QAC/B7D,eAAe,CAACjG,MAAM,CAAC8J,OAAO,CAAC;MAChC;IACH,CAAC,CAAC;IACF,OAAOuE,iBAAiB;EAC1B;EAEA;EACA;EACA,SAASE,uBAAuBA,CAC9BC,SAAiC,EACjCC,WAAsC,EACtCC,MAAwC;IAExC/K,oBAAoB,GAAG6K,SAAS;IAChC3K,iBAAiB,GAAG4K,WAAW;IAC/B7K,uBAAuB,GAAG8K,MAAM,IAAI,IAAI;IAExC;IACA;IACA;IACA,IAAI,CAAC5K,qBAAqB,IAAIjV,KAAK,CAAC6V,UAAU,KAAKzC,eAAe,EAAE;MAClE6B,qBAAqB,GAAG,IAAI;MAC5B,IAAI6K,CAAC,GAAGnH,sBAAsB,CAAC3Y,KAAK,CAACc,QAAQ,EAAEd,KAAK,CAACuH,OAAO,CAAC;MAC7D,IAAIuY,CAAC,IAAI,IAAI,EAAE;QACblI,WAAW,CAAC;UAAE9B,qBAAqB,EAAEgK;QAAC,CAAE,CAAC;MAC1C;IACF;IAED,OAAO,MAAK;MACVhL,oBAAoB,GAAG,IAAI;MAC3BE,iBAAiB,GAAG,IAAI;MACxBD,uBAAuB,GAAG,IAAI;KAC/B;EACH;EAEA,SAASgL,YAAYA,CAACjf,QAAkB,EAAEyG,OAAiC;IACzE,IAAIwN,uBAAuB,EAAE;MAC3B,IAAIlU,GAAG,GAAGkU,uBAAuB,CAC/BjU,QAAQ,EACRyG,OAAO,CAAC3H,GAAG,CAAE4V,CAAC,IAAKwK,qBAAqB,CAACxK,CAAC,EAAExV,KAAK,CAACiW,UAAU,CAAC,CAAC,CAC/D;MACD,OAAOpV,GAAG,IAAIC,QAAQ,CAACD,GAAG;IAC3B;IACD,OAAOC,QAAQ,CAACD,GAAG;EACrB;EAEA,SAAS6Y,kBAAkBA,CACzB5Y,QAAkB,EAClByG,OAAiC;IAEjC,IAAIuN,oBAAoB,IAAIE,iBAAiB,EAAE;MAC7C,IAAInU,GAAG,GAAGkf,YAAY,CAACjf,QAAQ,EAAEyG,OAAO,CAAC;MACzCuN,oBAAoB,CAACjU,GAAG,CAAC,GAAGmU,iBAAiB,EAAE;IAChD;EACH;EAEA,SAAS2D,sBAAsBA,CAC7B7X,QAAkB,EAClByG,OAAiC;IAEjC,IAAIuN,oBAAoB,EAAE;MACxB,IAAIjU,GAAG,GAAGkf,YAAY,CAACjf,QAAQ,EAAEyG,OAAO,CAAC;MACzC,IAAIuY,CAAC,GAAGhL,oBAAoB,CAACjU,GAAG,CAAC;MACjC,IAAI,OAAOif,CAAC,KAAK,QAAQ,EAAE;QACzB,OAAOA,CAAC;MACT;IACF;IACD,OAAO,IAAI;EACb;EAEA,SAASG,kBAAkBA,CAACC,SAAoC;IAC9DzZ,QAAQ,GAAG,EAAE;IACbgO,kBAAkB,GAAGpO,yBAAyB,CAC5C6Z,SAAS,EACT3Z,kBAAkB,EAClBtG,SAAS,EACTwG,QAAQ,CACT;EACH;EAEAkP,MAAM,GAAG;IACP,IAAIzO,QAAQA,CAAA;MACV,OAAOA,QAAQ;KAChB;IACD,IAAIlH,KAAKA,CAAA;MACP,OAAOA,KAAK;KACb;IACD,IAAIsG,MAAMA,CAAA;MACR,OAAOkO,UAAU;KAClB;IACD+C,UAAU;IACV/F,SAAS;IACTkO,uBAAuB;IACvB9G,QAAQ;IACRsE,KAAK;IACL5D,UAAU;IACV;IACA;IACAjY,UAAU,EAAGT,EAAM,IAAKkO,IAAI,CAACvN,OAAO,CAACF,UAAU,CAACT,EAAE,CAAC;IACnDc,cAAc,EAAGd,EAAM,IAAKkO,IAAI,CAACvN,OAAO,CAACG,cAAc,CAACd,EAAE,CAAC;IAC3Dqc,UAAU;IACVjF,aAAa;IACbF,OAAO;IACPqH,UAAU;IACVlH,aAAa;IACbkI,yBAAyB,EAAErJ,gBAAgB;IAC3CsJ,wBAAwB,EAAEhJ,eAAe;IACzC;IACA;IACA6I;GACD;EAED,OAAOtK,MAAM;AACf;AACA;AAEA;AACA;AACA;MAEa0K,sBAAsB,GAAGC,MAAM,CAAC,UAAU;AAWvC,SAAAC,mBAAmBA,CACjCja,MAA6B,EAC7BuS,IAAiC;EAEjC9U,SAAS,CACPuC,MAAM,CAACnG,MAAM,GAAG,CAAC,EACjB,kEAAkE,CACnE;EAED,IAAIsG,QAAQ,GAAkB,EAAE;EAChC,IAAIS,QAAQ,GAAG,CAAC2R,IAAI,GAAGA,IAAI,CAAC3R,QAAQ,GAAG,IAAI,KAAK,GAAG;EACnD,IAAIX,kBAA8C;EAClD,IAAIsS,IAAI,YAAJA,IAAI,CAAEtS,kBAAkB,EAAE;IAC5BA,kBAAkB,GAAGsS,IAAI,CAACtS,kBAAkB;EAC7C,OAAM,IAAIsS,IAAI,YAAJA,IAAI,CAAEtE,mBAAmB,EAAE;IACpC;IACA,IAAIA,mBAAmB,GAAGsE,IAAI,CAACtE,mBAAmB;IAClDhO,kBAAkB,GAAIH,KAAK,KAAM;MAC/B4N,gBAAgB,EAAEO,mBAAmB,CAACnO,KAAK;IAC5C,EAAC;EACH,OAAM;IACLG,kBAAkB,GAAGwN,yBAAyB;EAC/C;EAED,IAAIS,UAAU,GAAGnO,yBAAyB,CACxCC,MAAM,EACNC,kBAAkB,EAClBtG,SAAS,EACTwG,QAAQ,CACT;EAED;;;;;;;;;;;;;;;;;;AAkBG;EACH,eAAe+Z,KAAKA,CAClBxG,OAAgB,EAAAyG,MAAA,EACqC;IAAA,IAArD;MAAEC;4BAAiD,EAAE,GAAAD,MAAA;IAErD,IAAI/c,GAAG,GAAG,IAAIjC,GAAG,CAACuY,OAAO,CAACtW,GAAG,CAAC;IAC9B,IAAIsX,MAAM,GAAGhB,OAAO,CAACgB,MAAM;IAC3B,IAAIla,QAAQ,GAAGC,cAAc,CAAC,EAAE,EAAEO,UAAU,CAACoC,GAAG,CAAC,EAAE,IAAI,EAAE,SAAS,CAAC;IACnE,IAAI6D,OAAO,GAAGP,WAAW,CAACwN,UAAU,EAAE1T,QAAQ,EAAEoG,QAAQ,CAAC;IAEzD;IACA,IAAI,CAACyZ,aAAa,CAAC3F,MAAM,CAAC,IAAIA,MAAM,KAAK,MAAM,EAAE;MAC/C,IAAIvV,KAAK,GAAG4P,sBAAsB,CAAC,GAAG,EAAE;QAAE2F;MAAQ,EAAC;MACnD,IAAI;QAAEzT,OAAO,EAAEqZ,uBAAuB;QAAExa;MAAO,IAC7CkP,sBAAsB,CAACd,UAAU,CAAC;MACpC,OAAO;QACLtN,QAAQ;QACRpG,QAAQ;QACRyG,OAAO,EAAEqZ,uBAAuB;QAChC3K,UAAU,EAAE,EAAE;QACdC,UAAU,EAAE,IAAI;QAChBC,MAAM,EAAE;UACN,CAAC/P,KAAK,CAACO,EAAE,GAAGlB;SACb;QACDob,UAAU,EAAEpb,KAAK,CAACuJ,MAAM;QACxB8R,aAAa,EAAE,EAAE;QACjBC,aAAa,EAAE,EAAE;QACjB3J,eAAe,EAAE;OAClB;IACF,OAAM,IAAI,CAAC7P,OAAO,EAAE;MACnB,IAAI9B,KAAK,GAAG4P,sBAAsB,CAAC,GAAG,EAAE;QAAErU,QAAQ,EAAEF,QAAQ,CAACE;MAAQ,CAAE,CAAC;MACxE,IAAI;QAAEuG,OAAO,EAAEsS,eAAe;QAAEzT;MAAO,IACrCkP,sBAAsB,CAACd,UAAU,CAAC;MACpC,OAAO;QACLtN,QAAQ;QACRpG,QAAQ;QACRyG,OAAO,EAAEsS,eAAe;QACxB5D,UAAU,EAAE,EAAE;QACdC,UAAU,EAAE,IAAI;QAChBC,MAAM,EAAE;UACN,CAAC/P,KAAK,CAACO,EAAE,GAAGlB;SACb;QACDob,UAAU,EAAEpb,KAAK,CAACuJ,MAAM;QACxB8R,aAAa,EAAE,EAAE;QACjBC,aAAa,EAAE,EAAE;QACjB3J,eAAe,EAAE;OAClB;IACF;IAED,IAAIhO,MAAM,GAAG,MAAM4X,SAAS,CAAChH,OAAO,EAAElZ,QAAQ,EAAEyG,OAAO,EAAEmZ,cAAc,CAAC;IACxE,IAAIO,UAAU,CAAC7X,MAAM,CAAC,EAAE;MACtB,OAAOA,MAAM;IACd;IAED;IACA;IACA;IACA,OAAAvE,QAAA;MAAS/D,QAAQ;MAAEoG;IAAQ,GAAKkC,MAAM;EACxC;EAEA;;;;;;;;;;;;;;;;;;;AAmBG;EACH,eAAe8X,UAAUA,CACvBlH,OAAgB,EAAAmH,MAAA,EAIsC;IAAA,IAHtD;MACElG,OAAO;MACPyF;IAAc,IAAAS,MAAA,cACoC,EAAE,GAAAA,MAAA;IAEtD,IAAIzd,GAAG,GAAG,IAAIjC,GAAG,CAACuY,OAAO,CAACtW,GAAG,CAAC;IAC9B,IAAIsX,MAAM,GAAGhB,OAAO,CAACgB,MAAM;IAC3B,IAAIla,QAAQ,GAAGC,cAAc,CAAC,EAAE,EAAEO,UAAU,CAACoC,GAAG,CAAC,EAAE,IAAI,EAAE,SAAS,CAAC;IACnE,IAAI6D,OAAO,GAAGP,WAAW,CAACwN,UAAU,EAAE1T,QAAQ,EAAEoG,QAAQ,CAAC;IAEzD;IACA,IAAI,CAACyZ,aAAa,CAAC3F,MAAM,CAAC,IAAIA,MAAM,KAAK,MAAM,IAAIA,MAAM,KAAK,SAAS,EAAE;MACvE,MAAM3F,sBAAsB,CAAC,GAAG,EAAE;QAAE2F;MAAM,CAAE,CAAC;IAC9C,OAAM,IAAI,CAACzT,OAAO,EAAE;MACnB,MAAM8N,sBAAsB,CAAC,GAAG,EAAE;QAAErU,QAAQ,EAAEF,QAAQ,CAACE;MAAU,EAAC;IACnE;IAED,IAAI+J,KAAK,GAAGkQ,OAAO,GACf1T,OAAO,CAAC6Z,IAAI,CAAE5L,CAAC,IAAKA,CAAC,CAACpP,KAAK,CAACO,EAAE,KAAKsU,OAAO,CAAC,GAC3CH,cAAc,CAACvT,OAAO,EAAEzG,QAAQ,CAAC;IAErC,IAAIma,OAAO,IAAI,CAAClQ,KAAK,EAAE;MACrB,MAAMsK,sBAAsB,CAAC,GAAG,EAAE;QAChCrU,QAAQ,EAAEF,QAAQ,CAACE,QAAQ;QAC3Bia;MACD,EAAC;IACH,OAAM,IAAI,CAAClQ,KAAK,EAAE;MACjB;MACA,MAAMsK,sBAAsB,CAAC,GAAG,EAAE;QAAErU,QAAQ,EAAEF,QAAQ,CAACE;MAAU,EAAC;IACnE;IAED,IAAIoI,MAAM,GAAG,MAAM4X,SAAS,CAC1BhH,OAAO,EACPlZ,QAAQ,EACRyG,OAAO,EACPmZ,cAAc,EACd3V,KAAK,CACN;IACD,IAAIkW,UAAU,CAAC7X,MAAM,CAAC,EAAE;MACtB,OAAOA,MAAM;IACd;IAED,IAAI3D,KAAK,GAAG2D,MAAM,CAAC+M,MAAM,GAAGlL,MAAM,CAACoW,MAAM,CAACjY,MAAM,CAAC+M,MAAM,CAAC,CAAC,CAAC,CAAC,GAAGlW,SAAS;IACvE,IAAIwF,KAAK,KAAKxF,SAAS,EAAE;MACvB;MACA;MACA;MACA;MACA,MAAMwF,KAAK;IACZ;IAED;IACA,IAAI2D,MAAM,CAAC8M,UAAU,EAAE;MACrB,OAAOjL,MAAM,CAACoW,MAAM,CAACjY,MAAM,CAAC8M,UAAU,CAAC,CAAC,CAAC,CAAC;IAC3C;IAED,IAAI9M,MAAM,CAAC6M,UAAU,EAAE;MAAA,IAAAqL,qBAAA;MACrB,IAAIzS,IAAI,GAAG5D,MAAM,CAACoW,MAAM,CAACjY,MAAM,CAAC6M,UAAU,CAAC,CAAC,CAAC,CAAC;MAC9C,KAAAqL,qBAAA,GAAIlY,MAAM,CAACgO,eAAe,KAAtB,QAAAkK,qBAAA,CAAyBvW,KAAK,CAAC3E,KAAK,CAACO,EAAE,CAAC,EAAE;QAC5CkI,IAAI,CAACwR,sBAAsB,CAAC,GAAGjX,MAAM,CAACgO,eAAe,CAACrM,KAAK,CAAC3E,KAAK,CAACO,EAAE,CAAC;MACtE;MACD,OAAOkI,IAAI;IACZ;IAED,OAAO5O,SAAS;EAClB;EAEA,eAAe+gB,SAASA,CACtBhH,OAAgB,EAChBlZ,QAAkB,EAClByG,OAAiC,EACjCmZ,cAAuB,EACvBa,UAAmC;IAEnCxd,SAAS,CACPiW,OAAO,CAAC1J,MAAM,EACd,sEAAsE,CACvE;IAED,IAAI;MACF,IAAIiI,gBAAgB,CAACyB,OAAO,CAACgB,MAAM,CAAClO,WAAW,EAAE,CAAC,EAAE;QAClD,IAAI1D,MAAM,GAAG,MAAMoY,MAAM,CACvBxH,OAAO,EACPzS,OAAO,EACPga,UAAU,IAAIzG,cAAc,CAACvT,OAAO,EAAEzG,QAAQ,CAAC,EAC/C4f,cAAc,EACda,UAAU,IAAI,IAAI,CACnB;QACD,OAAOnY,MAAM;MACd;MAED,IAAIA,MAAM,GAAG,MAAMqY,aAAa,CAC9BzH,OAAO,EACPzS,OAAO,EACPmZ,cAAc,EACda,UAAU,CACX;MACD,OAAON,UAAU,CAAC7X,MAAM,CAAC,GACrBA,MAAM,GAAAvE,QAAA,KAEDuE,MAAM;QACT8M,UAAU,EAAE,IAAI;QAChB6K,aAAa,EAAE;OAChB;KACN,CAAC,OAAOzc,CAAC,EAAE;MACV;MACA;MACA;MACA,IAAIod,oBAAoB,CAACpd,CAAC,CAAC,EAAE;QAC3B,IAAIA,CAAC,CAACyW,IAAI,KAAK/U,UAAU,CAACP,KAAK,IAAI,CAACkc,kBAAkB,CAACrd,CAAC,CAACsd,QAAQ,CAAC,EAAE;UAClE,MAAMtd,CAAC,CAACsd,QAAQ;QACjB;QACD,OAAOtd,CAAC,CAACsd,QAAQ;MAClB;MACD;MACA;MACA,IAAID,kBAAkB,CAACrd,CAAC,CAAC,EAAE;QACzB,OAAOA,CAAC;MACT;MACD,MAAMA,CAAC;IACR;EACH;EAEA,eAAekd,MAAMA,CACnBxH,OAAgB,EAChBzS,OAAiC,EACjCsT,WAAmC,EACnC6F,cAAuB,EACvBmB,cAAuB;IAEvB,IAAIzY,MAAkB;IAEtB,IAAI,CAACyR,WAAW,CAACzU,KAAK,CAAChG,MAAM,IAAI,CAACya,WAAW,CAACzU,KAAK,CAACqP,IAAI,EAAE;MACxD,IAAIhQ,KAAK,GAAG4P,sBAAsB,CAAC,GAAG,EAAE;QACtC2F,MAAM,EAAEhB,OAAO,CAACgB,MAAM;QACtBha,QAAQ,EAAE,IAAIS,GAAG,CAACuY,OAAO,CAACtW,GAAG,CAAC,CAAC1C,QAAQ;QACvCia,OAAO,EAAEJ,WAAW,CAACzU,KAAK,CAACO;MAC5B,EAAC;MACF,IAAIkb,cAAc,EAAE;QAClB,MAAMpc,KAAK;MACZ;MACD2D,MAAM,GAAG;QACP2R,IAAI,EAAE/U,UAAU,CAACP,KAAK;QACtBA;OACD;IACF,OAAM;MACL2D,MAAM,GAAG,MAAM8R,kBAAkB,CAC/B,QAAQ,EACRlB,OAAO,EACPa,WAAW,EACXtT,OAAO,EACPd,QAAQ,EACRF,kBAAkB,EAClBW,QAAQ,EACR;QAAE4a,eAAe,EAAE,IAAI;QAAED,cAAc;QAAEnB;MAAgB,EAC1D;MAED,IAAI1G,OAAO,CAAC1J,MAAM,CAACY,OAAO,EAAE;QAC1B,IAAI8J,MAAM,GAAG6G,cAAc,GAAG,YAAY,GAAG,OAAO;QACpD,MAAM,IAAI3d,KAAK,CAAI8W,MAAM,oBAAiB,CAAC;MAC5C;IACF;IAED,IAAIG,gBAAgB,CAAC/R,MAAM,CAAC,EAAE;MAC5B;MACA;MACA;MACA;MACA,MAAM,IAAIiG,QAAQ,CAAC,IAAI,EAAE;QACvBL,MAAM,EAAE5F,MAAM,CAAC4F,MAAM;QACrBC,OAAO,EAAE;UACP8S,QAAQ,EAAE3Y,MAAM,CAACtI;QAClB;MACF,EAAC;IACH;IAED,IAAIya,gBAAgB,CAACnS,MAAM,CAAC,EAAE;MAC5B,IAAI3D,KAAK,GAAG4P,sBAAsB,CAAC,GAAG,EAAE;QAAE0F,IAAI,EAAE;MAAgB,EAAC;MACjE,IAAI8G,cAAc,EAAE;QAClB,MAAMpc,KAAK;MACZ;MACD2D,MAAM,GAAG;QACP2R,IAAI,EAAE/U,UAAU,CAACP,KAAK;QACtBA;OACD;IACF;IAED,IAAIoc,cAAc,EAAE;MAClB;MACA;MACA,IAAIxG,aAAa,CAACjS,MAAM,CAAC,EAAE;QACzB,MAAMA,MAAM,CAAC3D,KAAK;MACnB;MAED,OAAO;QACL8B,OAAO,EAAE,CAACsT,WAAW,CAAC;QACtB5E,UAAU,EAAE,EAAE;QACdC,UAAU,EAAE;UAAE,CAAC2E,WAAW,CAACzU,KAAK,CAACO,EAAE,GAAGyC,MAAM,CAACyF;SAAM;QACnDsH,MAAM,EAAE,IAAI;QACZ;QACA;QACA0K,UAAU,EAAE,GAAG;QACfC,aAAa,EAAE,EAAE;QACjBC,aAAa,EAAE,EAAE;QACjB3J,eAAe,EAAE;OAClB;IACF;IAED,IAAIiE,aAAa,CAACjS,MAAM,CAAC,EAAE;MACzB;MACA;MACA,IAAIkS,aAAa,GAAGnB,mBAAmB,CAAC5S,OAAO,EAAEsT,WAAW,CAACzU,KAAK,CAACO,EAAE,CAAC;MACtE,IAAIqb,OAAO,GAAG,MAAMP,aAAa,CAC/BzH,OAAO,EACPzS,OAAO,EACPmZ,cAAc,EACdzgB,SAAS,EACT;QACE,CAACqb,aAAa,CAAClV,KAAK,CAACO,EAAE,GAAGyC,MAAM,CAAC3D;MAClC,EACF;MAED;MACA,OAAAZ,QAAA,KACKmd,OAAO;QACVnB,UAAU,EAAEhO,oBAAoB,CAACzJ,MAAM,CAAC3D,KAAK,CAAC,GAC1C2D,MAAM,CAAC3D,KAAK,CAACuJ,MAAM,GACnB,GAAG;QACPkH,UAAU,EAAE,IAAI;QAChB6K,aAAa,EAAAlc,QAAA,KACPuE,MAAM,CAAC6F,OAAO,GAAG;UAAE,CAAC4L,WAAW,CAACzU,KAAK,CAACO,EAAE,GAAGyC,MAAM,CAAC6F;SAAS,GAAG,EAAE;MACrE;IAEJ;IAED;IACA,IAAIgT,aAAa,GAAG,IAAIxH,OAAO,CAACT,OAAO,CAACtW,GAAG,EAAE;MAC3CuL,OAAO,EAAE+K,OAAO,CAAC/K,OAAO;MACxBwD,QAAQ,EAAEuH,OAAO,CAACvH,QAAQ;MAC1BnC,MAAM,EAAE0J,OAAO,CAAC1J;IACjB,EAAC;IACF,IAAI0R,OAAO,GAAG,MAAMP,aAAa,CAACQ,aAAa,EAAE1a,OAAO,EAAEmZ,cAAc,CAAC;IAEzE,OAAA7b,QAAA,CACK,IAAAmd,OAAO,EAEN5Y,MAAM,CAACyX,UAAU,GAAG;MAAEA,UAAU,EAAEzX,MAAM,CAACyX;KAAY,GAAG,EAAE;MAC9D3K,UAAU,EAAE;QACV,CAAC2E,WAAW,CAACzU,KAAK,CAACO,EAAE,GAAGyC,MAAM,CAACyF;OAChC;MACDkS,aAAa,EAAAlc,QAAA,KACPuE,MAAM,CAAC6F,OAAO,GAAG;QAAE,CAAC4L,WAAW,CAACzU,KAAK,CAACO,EAAE,GAAGyC,MAAM,CAAC6F;OAAS,GAAG,EAAE;IACrE;EAEL;EAEA,eAAewS,aAAaA,CAC1BzH,OAAgB,EAChBzS,OAAiC,EACjCmZ,cAAuB,EACvBa,UAAmC,EACnChH,kBAA8B;IAQ9B,IAAIsH,cAAc,GAAGN,UAAU,IAAI,IAAI;IAEvC;IACA,IACEM,cAAc,IACd,EAACN,UAAU,IAAV,QAAAA,UAAU,CAAEnb,KAAK,CAACsP,MAAM,CACzB,MAAC6L,UAAU,IAAV,QAAAA,UAAU,CAAEnb,KAAK,CAACqP,IAAI,CACvB;MACA,MAAMJ,sBAAsB,CAAC,GAAG,EAAE;QAChC2F,MAAM,EAAEhB,OAAO,CAACgB,MAAM;QACtBha,QAAQ,EAAE,IAAIS,GAAG,CAACuY,OAAO,CAACtW,GAAG,CAAC,CAAC1C,QAAQ;QACvCia,OAAO,EAAEsG,UAAU,oBAAVA,UAAU,CAAEnb,KAAK,CAACO;MAC5B,EAAC;IACH;IAED,IAAI2W,cAAc,GAAGiE,UAAU,GAC3B,CAACA,UAAU,CAAC,GACZW,6BAA6B,CAC3B3a,OAAO,EACP0D,MAAM,CAACwN,IAAI,CAAC8B,kBAAkB,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,CACzC;IACL,IAAImB,aAAa,GAAG4B,cAAc,CAAClT,MAAM,CACtCoL,CAAC,IAAKA,CAAC,CAACpP,KAAK,CAACsP,MAAM,IAAIF,CAAC,CAACpP,KAAK,CAACqP,IAAI,CACtC;IAED;IACA,IAAIiG,aAAa,CAACvb,MAAM,KAAK,CAAC,EAAE;MAC9B,OAAO;QACLoH,OAAO;QACP;QACA0O,UAAU,EAAE1O,OAAO,CAAC8C,MAAM,CACxB,CAACkG,GAAG,EAAEiF,CAAC,KAAKvK,MAAM,CAACrF,MAAM,CAAC2K,GAAG,EAAE;UAAE,CAACiF,CAAC,CAACpP,KAAK,CAACO,EAAE,GAAG;QAAI,CAAE,CAAC,EACtD,EAAE,CACH;QACDwP,MAAM,EAAEoE,kBAAkB,IAAI,IAAI;QAClCsG,UAAU,EAAE,GAAG;QACfC,aAAa,EAAE,EAAE;QACjB1J,eAAe,EAAE;OAClB;IACF;IAED,IAAIkF,OAAO,GAAG,MAAMtM,OAAO,CAAC6O,GAAG,CAAC,CAC9B,GAAGnD,aAAa,CAAC9b,GAAG,CAAEmL,KAAK,IACzBmQ,kBAAkB,CAChB,QAAQ,EACRlB,OAAO,EACPjP,KAAK,EACLxD,OAAO,EACPd,QAAQ,EACRF,kBAAkB,EAClBW,QAAQ,EACR;MAAE4a,eAAe,EAAE,IAAI;MAAED,cAAc;MAAEnB;KAAgB,CAC1D,CACF,CACF,CAAC;IAEF,IAAI1G,OAAO,CAAC1J,MAAM,CAACY,OAAO,EAAE;MAC1B,IAAI8J,MAAM,GAAG6G,cAAc,GAAG,YAAY,GAAG,OAAO;MACpD,MAAM,IAAI3d,KAAK,CAAI8W,MAAM,oBAAiB,CAAC;IAC5C;IAED;IACA,IAAI5D,eAAe,GAAG,IAAIf,GAAG,EAAwB;IACrD,IAAI2L,OAAO,GAAGG,sBAAsB,CAClC5a,OAAO,EACPmU,aAAa,EACbY,OAAO,EACP/B,kBAAkB,EAClBnD,eAAe,CAChB;IAED;IACA,IAAIgL,eAAe,GAAG,IAAIlc,GAAG,CAC3BwV,aAAa,CAAC9b,GAAG,CAAEmL,KAAK,IAAKA,KAAK,CAAC3E,KAAK,CAACO,EAAE,CAAC,CAC7C;IACDY,OAAO,CAACgB,OAAO,CAAEwC,KAAK,IAAI;MACxB,IAAI,CAACqX,eAAe,CAACjT,GAAG,CAACpE,KAAK,CAAC3E,KAAK,CAACO,EAAE,CAAC,EAAE;QACxCqb,OAAO,CAAC/L,UAAU,CAAClL,KAAK,CAAC3E,KAAK,CAACO,EAAE,CAAC,GAAG,IAAI;MAC1C;IACH,CAAC,CAAC;IAEF,OAAA9B,QAAA,KACKmd,OAAO;MACVza,OAAO;MACP6P,eAAe,EACbA,eAAe,CAACrF,IAAI,GAAG,CAAC,GACpB9G,MAAM,CAACoX,WAAW,CAACjL,eAAe,CAACzX,OAAO,EAAE,CAAC,GAC7C;IAAI;EAEd;EAEA,OAAO;IACL6U,UAAU;IACVgM,KAAK;IACLU;GACD;AACH;AAEA;AAEA;AACA;AACA;AAEA;;;AAGG;SACaoB,yBAAyBA,CACvChc,MAAiC,EACjC0b,OAA6B,EAC7Bvc,KAAU;EAEV,IAAI8c,UAAU,GAAA1d,QAAA,KACTmd,OAAO;IACVnB,UAAU,EAAE,GAAG;IACf1K,MAAM,EAAE;MACN,CAAC6L,OAAO,CAACQ,0BAA0B,IAAIlc,MAAM,CAAC,CAAC,CAAC,CAACK,EAAE,GAAGlB;IACvD;GACF;EACD,OAAO8c,UAAU;AACnB;AAEA,SAASE,sBAAsBA,CAC7B5J,IAA2B;EAE3B,OACEA,IAAI,IAAI,IAAI,KACV,UAAU,IAAIA,IAAI,IAAIA,IAAI,CAACrF,QAAQ,IAAI,IAAI,IAC1C,MAAM,IAAIqF,IAAI,IAAIA,IAAI,CAAC6J,IAAI,KAAKziB,SAAU,CAAC;AAElD;AAEA,SAAS8Y,WAAWA,CAClBjY,QAAc,EACdyG,OAAiC,EACjCL,QAAgB,EAChByb,eAAwB,EACxB/hB,EAAa,EACboY,WAAoB,EACpBC,QAA8B;EAE9B,IAAI2J,iBAA2C;EAC/C,IAAIC,gBAAoD;EACxD,IAAI7J,WAAW,IAAI,IAAI,IAAIC,QAAQ,KAAK,MAAM,EAAE;IAC9C;IACA;IACA;IACA;IACA2J,iBAAiB,GAAG,EAAE;IACtB,KAAK,IAAI7X,KAAK,IAAIxD,OAAO,EAAE;MACzBqb,iBAAiB,CAAC7gB,IAAI,CAACgJ,KAAK,CAAC;MAC7B,IAAIA,KAAK,CAAC3E,KAAK,CAACO,EAAE,KAAKqS,WAAW,EAAE;QAClC6J,gBAAgB,GAAG9X,KAAK;QACxB;MACD;IACF;EACF,OAAM;IACL6X,iBAAiB,GAAGrb,OAAO;IAC3Bsb,gBAAgB,GAAGtb,OAAO,CAACA,OAAO,CAACpH,MAAM,GAAG,CAAC,CAAC;EAC/C;EAED;EACA,IAAIwB,IAAI,GAAGmM,SAAS,CAClBlN,EAAE,GAAGA,EAAE,GAAG,GAAG,EACbiN,0BAA0B,CAAC+U,iBAAiB,CAAC,CAAChjB,GAAG,CAAE4V,CAAC,IAAKA,CAAC,CAACrK,YAAY,CAAC,EACxEhE,aAAa,CAACrG,QAAQ,CAACE,QAAQ,EAAEkG,QAAQ,CAAC,IAAIpG,QAAQ,CAACE,QAAQ,EAC/DiY,QAAQ,KAAK,MAAM,CACpB;EAED;EACA;EACA;EACA,IAAIrY,EAAE,IAAI,IAAI,EAAE;IACde,IAAI,CAACE,MAAM,GAAGf,QAAQ,CAACe,MAAM;IAC7BF,IAAI,CAACG,IAAI,GAAGhB,QAAQ,CAACgB,IAAI;EAC1B;EAED;EACA,IACE,CAAClB,EAAE,IAAI,IAAI,IAAIA,EAAE,KAAK,EAAE,IAAIA,EAAE,KAAK,GAAG,KACtCiiB,gBAAgB,IAChBA,gBAAgB,CAACzc,KAAK,CAACtG,KAAK,IAC5B,CAACgjB,kBAAkB,CAACnhB,IAAI,CAACE,MAAM,CAAC,EAChC;IACAF,IAAI,CAACE,MAAM,GAAGF,IAAI,CAACE,MAAM,GACrBF,IAAI,CAACE,MAAM,CAACO,OAAO,CAAC,KAAK,EAAE,SAAS,CAAC,GACrC,QAAQ;EACb;EAED;EACA;EACA;EACA;EACA,IAAIugB,eAAe,IAAIzb,QAAQ,KAAK,GAAG,EAAE;IACvCvF,IAAI,CAACX,QAAQ,GACXW,IAAI,CAACX,QAAQ,KAAK,GAAG,GAAGkG,QAAQ,GAAGgB,SAAS,CAAC,CAAChB,QAAQ,EAAEvF,IAAI,CAACX,QAAQ,CAAC,CAAC;EAC1E;EAED,OAAOM,UAAU,CAACK,IAAI,CAAC;AACzB;AAEA;AACA;AACA,SAASwX,wBAAwBA,CAC/B4J,mBAA4B,EAC5BC,SAAkB,EAClBrhB,IAAY,EACZkX,IAA4B;EAM5B;EACA,IAAI,CAACA,IAAI,IAAI,CAAC4J,sBAAsB,CAAC5J,IAAI,CAAC,EAAE;IAC1C,OAAO;MAAElX;KAAM;EAChB;EAED,IAAIkX,IAAI,CAACxF,UAAU,IAAI,CAACsN,aAAa,CAAC9H,IAAI,CAACxF,UAAU,CAAC,EAAE;IACtD,OAAO;MACL1R,IAAI;MACJ8D,KAAK,EAAE4P,sBAAsB,CAAC,GAAG,EAAE;QAAE2F,MAAM,EAAEnC,IAAI,CAACxF;OAAY;KAC/D;EACF;EAED,IAAI4P,mBAAmB,GAAGA,CAAA,MAAO;IAC/BthB,IAAI;IACJ8D,KAAK,EAAE4P,sBAAsB,CAAC,GAAG,EAAE;MAAE0F,IAAI,EAAE;KAAgB;EAC5D,EAAC;EAEF;EACA,IAAImI,aAAa,GAAGrK,IAAI,CAACxF,UAAU,IAAI,KAAK;EAC5C,IAAIA,UAAU,GAAG0P,mBAAmB,GAC/BG,aAAa,CAACC,WAAW,EAAoB,GAC7CD,aAAa,CAACpW,WAAW,EAAiB;EAC/C,IAAIwG,UAAU,GAAG8P,iBAAiB,CAACzhB,IAAI,CAAC;EAExC,IAAIkX,IAAI,CAAC6J,IAAI,KAAKziB,SAAS,EAAE;IAC3B,IAAI4Y,IAAI,CAACtF,WAAW,KAAK,YAAY,EAAE;MACrC;MACA,IAAI,CAACgF,gBAAgB,CAAClF,UAAU,CAAC,EAAE;QACjC,OAAO4P,mBAAmB,EAAE;MAC7B;MAED,IAAIxP,IAAI,GACN,OAAOoF,IAAI,CAAC6J,IAAI,KAAK,QAAQ,GACzB7J,IAAI,CAAC6J,IAAI,GACT7J,IAAI,CAAC6J,IAAI,YAAYW,QAAQ,IAC7BxK,IAAI,CAAC6J,IAAI,YAAYY,eAAe;MACpC;MACA1T,KAAK,CAACxB,IAAI,CAACyK,IAAI,CAAC6J,IAAI,CAAC/iB,OAAO,EAAE,CAAC,CAAC0K,MAAM,CACpC,CAACkG,GAAG,EAAAgT,KAAA;QAAA,IAAE,CAAC5d,IAAI,EAAE3B,KAAK,CAAC,GAAAuf,KAAA;QAAA,YAAQhT,GAAG,GAAG5K,IAAI,SAAI3B,KAAK;OAAI,EAClD,EAAE,CACH,GACDyH,MAAM,CAACoN,IAAI,CAAC6J,IAAI,CAAC;MAEvB,OAAO;QACL/gB,IAAI;QACJuX,UAAU,EAAE;UACV7F,UAAU;UACVC,UAAU;UACVC,WAAW,EAAEsF,IAAI,CAACtF,WAAW;UAC7BC,QAAQ,EAAEvT,SAAS;UACnB2O,IAAI,EAAE3O,SAAS;UACfwT;QACD;OACF;IACF,OAAM,IAAIoF,IAAI,CAACtF,WAAW,KAAK,kBAAkB,EAAE;MAClD;MACA,IAAI,CAACgF,gBAAgB,CAAClF,UAAU,CAAC,EAAE;QACjC,OAAO4P,mBAAmB,EAAE;MAC7B;MAED,IAAI;QACF,IAAIrU,IAAI,GACN,OAAOiK,IAAI,CAAC6J,IAAI,KAAK,QAAQ,GAAGvhB,IAAI,CAACqiB,KAAK,CAAC3K,IAAI,CAAC6J,IAAI,CAAC,GAAG7J,IAAI,CAAC6J,IAAI;QAEnE,OAAO;UACL/gB,IAAI;UACJuX,UAAU,EAAE;YACV7F,UAAU;YACVC,UAAU;YACVC,WAAW,EAAEsF,IAAI,CAACtF,WAAW;YAC7BC,QAAQ,EAAEvT,SAAS;YACnB2O,IAAI;YACJ6E,IAAI,EAAExT;UACP;SACF;OACF,CAAC,OAAOqE,CAAC,EAAE;QACV,OAAO2e,mBAAmB,EAAE;MAC7B;IACF;EACF;EAEDlf,SAAS,CACP,OAAOsf,QAAQ,KAAK,UAAU,EAC9B,+CAA+C,CAChD;EAED,IAAII,YAA6B;EACjC,IAAIjQ,QAAkB;EAEtB,IAAIqF,IAAI,CAACrF,QAAQ,EAAE;IACjBiQ,YAAY,GAAGC,6BAA6B,CAAC7K,IAAI,CAACrF,QAAQ,CAAC;IAC3DA,QAAQ,GAAGqF,IAAI,CAACrF,QAAQ;EACzB,OAAM,IAAIqF,IAAI,CAAC6J,IAAI,YAAYW,QAAQ,EAAE;IACxCI,YAAY,GAAGC,6BAA6B,CAAC7K,IAAI,CAAC6J,IAAI,CAAC;IACvDlP,QAAQ,GAAGqF,IAAI,CAAC6J,IAAI;EACrB,OAAM,IAAI7J,IAAI,CAAC6J,IAAI,YAAYY,eAAe,EAAE;IAC/CG,YAAY,GAAG5K,IAAI,CAAC6J,IAAI;IACxBlP,QAAQ,GAAGmQ,6BAA6B,CAACF,YAAY,CAAC;EACvD,OAAM,IAAI5K,IAAI,CAAC6J,IAAI,IAAI,IAAI,EAAE;IAC5Be,YAAY,GAAG,IAAIH,eAAe,EAAE;IACpC9P,QAAQ,GAAG,IAAI6P,QAAQ,EAAE;EAC1B,OAAM;IACL,IAAI;MACFI,YAAY,GAAG,IAAIH,eAAe,CAACzK,IAAI,CAAC6J,IAAI,CAAC;MAC7ClP,QAAQ,GAAGmQ,6BAA6B,CAACF,YAAY,CAAC;KACvD,CAAC,OAAOnf,CAAC,EAAE;MACV,OAAO2e,mBAAmB,EAAE;IAC7B;EACF;EAED,IAAI/J,UAAU,GAAe;IAC3B7F,UAAU;IACVC,UAAU;IACVC,WAAW,EACRsF,IAAI,IAAIA,IAAI,CAACtF,WAAW,IAAK,mCAAmC;IACnEC,QAAQ;IACR5E,IAAI,EAAE3O,SAAS;IACfwT,IAAI,EAAExT;GACP;EAED,IAAIsY,gBAAgB,CAACW,UAAU,CAAC7F,UAAU,CAAC,EAAE;IAC3C,OAAO;MAAE1R,IAAI;MAAEuX;KAAY;EAC5B;EAED;EACA,IAAInU,UAAU,GAAGnD,SAAS,CAACD,IAAI,CAAC;EAChC;EACA;EACA;EACA,IAAIqhB,SAAS,IAAIje,UAAU,CAAClD,MAAM,IAAIihB,kBAAkB,CAAC/d,UAAU,CAAClD,MAAM,CAAC,EAAE;IAC3E4hB,YAAY,CAACG,MAAM,CAAC,OAAO,EAAE,EAAE,CAAC;EACjC;EACD7e,UAAU,CAAClD,MAAM,SAAO4hB,YAAc;EAEtC,OAAO;IAAE9hB,IAAI,EAAEL,UAAU,CAACyD,UAAU,CAAC;IAAEmU;GAAY;AACrD;AAEA;AACA;AACA,SAASgJ,6BAA6BA,CACpC3a,OAAiC,EACjCsc,UAAmB;EAEnB,IAAIC,eAAe,GAAGvc,OAAO;EAC7B,IAAIsc,UAAU,EAAE;IACd,IAAI/jB,KAAK,GAAGyH,OAAO,CAACwc,SAAS,CAAEvO,CAAC,IAAKA,CAAC,CAACpP,KAAK,CAACO,EAAE,KAAKkd,UAAU,CAAC;IAC/D,IAAI/jB,KAAK,IAAI,CAAC,EAAE;MACdgkB,eAAe,GAAGvc,OAAO,CAAC1D,KAAK,CAAC,CAAC,EAAE/D,KAAK,CAAC;IAC1C;EACF;EACD,OAAOgkB,eAAe;AACxB;AAEA,SAASlI,gBAAgBA,CACvBra,OAAgB,EAChBvB,KAAkB,EAClBuH,OAAiC,EACjC2R,UAAkC,EAClCpY,QAAkB,EAClB6V,sBAA+B,EAC/BC,uBAAiC,EACjCC,qBAA+B,EAC/BM,gBAA6C,EAC7CD,gBAA6B,EAC7ByC,WAAsC,EACtCzS,QAA4B,EAC5BgT,iBAA6B,EAC7Bb,YAAwB;EAExB,IAAIuE,YAAY,GAAGvE,YAAY,GAC3BpO,MAAM,CAACoW,MAAM,CAAChI,YAAY,CAAC,CAAC,CAAC,CAAC,GAC9Ba,iBAAiB,GACjBjP,MAAM,CAACoW,MAAM,CAACnH,iBAAiB,CAAC,CAAC,CAAC,CAAC,GACnCja,SAAS;EAEb,IAAI+jB,UAAU,GAAGziB,OAAO,CAACC,SAAS,CAACxB,KAAK,CAACc,QAAQ,CAAC;EAClD,IAAImjB,OAAO,GAAG1iB,OAAO,CAACC,SAAS,CAACV,QAAQ,CAAC;EAEzC;EACA,IAAI+iB,UAAU,GAAGxK,YAAY,GAAGpO,MAAM,CAACwN,IAAI,CAACY,YAAY,CAAC,CAAC,CAAC,CAAC,GAAGpZ,SAAS;EACxE,IAAI6jB,eAAe,GAAG5B,6BAA6B,CAAC3a,OAAO,EAAEsc,UAAU,CAAC;EAExE,IAAIK,iBAAiB,GAAGJ,eAAe,CAAC1Z,MAAM,CAAC,CAACW,KAAK,EAAEjL,KAAK,KAAI;IAC9D,IAAIiL,KAAK,CAAC3E,KAAK,CAACqP,IAAI,EAAE;MACpB;MACA,OAAO,IAAI;IACZ;IACD,IAAI1K,KAAK,CAAC3E,KAAK,CAACsP,MAAM,IAAI,IAAI,EAAE;MAC9B,OAAO,KAAK;IACb;IAED;IACA,IACEyO,WAAW,CAACnkB,KAAK,CAACiW,UAAU,EAAEjW,KAAK,CAACuH,OAAO,CAACzH,KAAK,CAAC,EAAEiL,KAAK,CAAC,IAC1D6L,uBAAuB,CAACzM,IAAI,CAAExD,EAAE,IAAKA,EAAE,KAAKoE,KAAK,CAAC3E,KAAK,CAACO,EAAE,CAAC,EAC3D;MACA,OAAO,IAAI;IACZ;IAED;IACA;IACA;IACA;IACA,IAAIyd,iBAAiB,GAAGpkB,KAAK,CAACuH,OAAO,CAACzH,KAAK,CAAC;IAC5C,IAAIukB,cAAc,GAAGtZ,KAAK;IAE1B,OAAOuZ,sBAAsB,CAACvZ,KAAK,EAAAlG,QAAA;MACjCmf,UAAU;MACVO,aAAa,EAAEH,iBAAiB,CAAClZ,MAAM;MACvC+Y,OAAO;MACPO,UAAU,EAAEH,cAAc,CAACnZ;IAAM,GAC9BgO,UAAU;MACb0E,YAAY;MACZ6G,uBAAuB;MACrB;MACA9N,sBAAsB;MACtB;MACAqN,UAAU,CAAChjB,QAAQ,GAAGgjB,UAAU,CAACniB,MAAM,KACrCoiB,OAAO,CAACjjB,QAAQ,GAAGijB,OAAO,CAACpiB,MAAM;MACnC;MACAmiB,UAAU,CAACniB,MAAM,KAAKoiB,OAAO,CAACpiB,MAAM,IACpC6iB,kBAAkB,CAACN,iBAAiB,EAAEC,cAAc;IAAC,EACxD,CAAC;EACJ,CAAC,CAAC;EAEF;EACA,IAAI1I,oBAAoB,GAA0B,EAAE;EACpDxE,gBAAgB,CAAC5O,OAAO,CAAC,CAAC8T,CAAC,EAAExb,GAAG,KAAI;IAClC;IACA,IAAI,CAAC0G,OAAO,CAAC4C,IAAI,CAAEqL,CAAC,IAAKA,CAAC,CAACpP,KAAK,CAACO,EAAE,KAAK0V,CAAC,CAACpB,OAAO,CAAC,EAAE;MAClD;IACD;IAED,IAAI0J,cAAc,GAAG3d,WAAW,CAAC2S,WAAW,EAAE0C,CAAC,CAAC1a,IAAI,EAAEuF,QAAQ,CAAC;IAE/D;IACA;IACA;IACA;IACA,IAAI,CAACyd,cAAc,EAAE;MACnBhJ,oBAAoB,CAAC5Z,IAAI,CAAC;QACxBlB,GAAG;QACHoa,OAAO,EAAEoB,CAAC,CAACpB,OAAO;QAClBtZ,IAAI,EAAE0a,CAAC,CAAC1a,IAAI;QACZ4F,OAAO,EAAE,IAAI;QACbwD,KAAK,EAAE,IAAI;QACXmF,UAAU,EAAE;MACb,EAAC;MACF;IACD;IAED;IACA;IACA;IACA,IAAI8L,OAAO,GAAGhc,KAAK,CAACoW,QAAQ,CAACnF,GAAG,CAACpQ,GAAG,CAAC;IACrC,IAAI+jB,YAAY,GAAG9J,cAAc,CAAC6J,cAAc,EAAEtI,CAAC,CAAC1a,IAAI,CAAC;IAEzD,IAAIkjB,gBAAgB,GAAG,KAAK;IAC5B,IAAI3N,gBAAgB,CAAC/H,GAAG,CAACtO,GAAG,CAAC,EAAE;MAC7B;MACAgkB,gBAAgB,GAAG,KAAK;KACzB,MAAM,IAAIhO,qBAAqB,CAACpO,QAAQ,CAAC5H,GAAG,CAAC,EAAE;MAC9C;MACAgkB,gBAAgB,GAAG,IAAI;IACxB,OAAM,IACL7I,OAAO,IACPA,OAAO,CAAChc,KAAK,KAAK,MAAM,IACxBgc,OAAO,CAACnN,IAAI,KAAK5O,SAAS,EAC1B;MACA;MACA;MACA;MACA4kB,gBAAgB,GAAGlO,sBAAsB;IAC1C,OAAM;MACL;MACA;MACAkO,gBAAgB,GAAGP,sBAAsB,CAACM,YAAY,EAAA/f,QAAA;QACpDmf,UAAU;QACVO,aAAa,EAAEvkB,KAAK,CAACuH,OAAO,CAACvH,KAAK,CAACuH,OAAO,CAACpH,MAAM,GAAG,CAAC,CAAC,CAAC+K,MAAM;QAC7D+Y,OAAO;QACPO,UAAU,EAAEjd,OAAO,CAACA,OAAO,CAACpH,MAAM,GAAG,CAAC,CAAC,CAAC+K;MAAM,GAC3CgO,UAAU;QACb0E,YAAY;QACZ6G,uBAAuB,EAAE9N;MAAsB,EAChD,CAAC;IACH;IAED,IAAIkO,gBAAgB,EAAE;MACpBlJ,oBAAoB,CAAC5Z,IAAI,CAAC;QACxBlB,GAAG;QACHoa,OAAO,EAAEoB,CAAC,CAACpB,OAAO;QAClBtZ,IAAI,EAAE0a,CAAC,CAAC1a,IAAI;QACZ4F,OAAO,EAAEod,cAAc;QACvB5Z,KAAK,EAAE6Z,YAAY;QACnB1U,UAAU,EAAE,IAAIC,eAAe;MAChC,EAAC;IACH;EACH,CAAC,CAAC;EAEF,OAAO,CAAC+T,iBAAiB,EAAEvI,oBAAoB,CAAC;AAClD;AAEA,SAASwI,WAAWA,CAClBW,iBAA4B,EAC5BC,YAAoC,EACpCha,KAA6B;EAE7B,IAAIia,KAAK;EACP;EACA,CAACD,YAAY;EACb;EACAha,KAAK,CAAC3E,KAAK,CAACO,EAAE,KAAKoe,YAAY,CAAC3e,KAAK,CAACO,EAAE;EAE1C;EACA;EACA,IAAIse,aAAa,GAAGH,iBAAiB,CAAC/Z,KAAK,CAAC3E,KAAK,CAACO,EAAE,CAAC,KAAK1G,SAAS;EAEnE;EACA,OAAO+kB,KAAK,IAAIC,aAAa;AAC/B;AAEA,SAASP,kBAAkBA,CACzBK,YAAoC,EACpCha,KAA6B;EAE7B,IAAIma,WAAW,GAAGH,YAAY,CAAC3e,KAAK,CAACzE,IAAI;EACzC;IACE;IACAojB,YAAY,CAAC/jB,QAAQ,KAAK+J,KAAK,CAAC/J,QAAQ;IACxC;IACA;IACCkkB,WAAW,IAAI,IAAI,IAClBA,WAAW,CAACjc,QAAQ,CAAC,GAAG,CAAC,IACzB8b,YAAY,CAAC7Z,MAAM,CAAC,GAAG,CAAC,KAAKH,KAAK,CAACG,MAAM,CAAC,GAAG;EAAA;AAEnD;AAEA,SAASoZ,sBAAsBA,CAC7Ba,WAAmC,EACnCC,GAA4C;EAE5C,IAAID,WAAW,CAAC/e,KAAK,CAACye,gBAAgB,EAAE;IACtC,IAAIQ,WAAW,GAAGF,WAAW,CAAC/e,KAAK,CAACye,gBAAgB,CAACO,GAAG,CAAC;IACzD,IAAI,OAAOC,WAAW,KAAK,SAAS,EAAE;MACpC,OAAOA,WAAW;IACnB;EACF;EAED,OAAOD,GAAG,CAACX,uBAAuB;AACpC;AAEA;;;;AAIG;AACH,eAAea,mBAAmBA,CAChClf,KAA8B,EAC9BG,kBAA8C,EAC9CE,QAAuB;EAEvB,IAAI,CAACL,KAAK,CAACqP,IAAI,EAAE;IACf;EACD;EAED,IAAI8P,SAAS,GAAG,MAAMnf,KAAK,CAACqP,IAAI,EAAE;EAElC;EACA;EACA;EACA,IAAI,CAACrP,KAAK,CAACqP,IAAI,EAAE;IACf;EACD;EAED,IAAI+P,aAAa,GAAG/e,QAAQ,CAACL,KAAK,CAACO,EAAE,CAAC;EACtC5C,SAAS,CAACyhB,aAAa,EAAE,4BAA4B,CAAC;EAEtD;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,IAAIC,YAAY,GAAwB,EAAE;EAC1C,KAAK,IAAIC,iBAAiB,IAAIH,SAAS,EAAE;IACvC,IAAII,gBAAgB,GAClBH,aAAa,CAACE,iBAA+C,CAAC;IAEhE,IAAIE,2BAA2B,GAC7BD,gBAAgB,KAAK1lB,SAAS;IAC9B;IACA;IACAylB,iBAAiB,KAAK,kBAAkB;IAE1CzkB,OAAO,CACL,CAAC2kB,2BAA2B,EAC5B,aAAUJ,aAAa,CAAC7e,EAAE,mCAA4B+e,iBAAiB,wFACQ,IACjD,+BAAAA,iBAAiB,yBAAoB,CACpE;IAED,IACE,CAACE,2BAA2B,IAC5B,CAAC3f,kBAAkB,CAACkJ,GAAG,CAACuW,iBAAsC,CAAC,EAC/D;MACAD,YAAY,CAACC,iBAAiB,CAAC,GAC7BH,SAAS,CAACG,iBAA2C,CAAC;IACzD;EACF;EAED;EACA;EACAza,MAAM,CAACrF,MAAM,CAAC4f,aAAa,EAAEC,YAAY,CAAC;EAE1C;EACA;EACA;EACAxa,MAAM,CAACrF,MAAM,CAAC4f,aAAa,EAAA3gB,QAAA,CAKtB,IAAA0B,kBAAkB,CAACif,aAAa,CAAC;IACpC/P,IAAI,EAAExV;EAAS,EAChB,CAAC;AACJ;AAEA,eAAeib,kBAAkBA,CAC/BH,IAAyB,EACzBf,OAAgB,EAChBjP,KAA6B,EAC7BxD,OAAiC,EACjCd,QAAuB,EACvBF,kBAA8C,EAC9CW,QAAgB,EAChB2R,IAAA,EAIM;EAAA,IAJNA,IAAA;IAAAA,IAAA,GAII,EAAE;EAAA;EAEN,IAAIgN,UAAU;EACd,IAAIzc,MAAM;EACV,IAAI0c,QAAkC;EAEtC,IAAIC,UAAU,GAAIC,OAAwC,IAAI;IAC5D;IACA,IAAIlW,MAAkB;IACtB,IAAIC,YAAY,GAAG,IAAIC,OAAO,CAAC,CAACtD,CAAC,EAAEuD,CAAC,KAAMH,MAAM,GAAGG,CAAE,CAAC;IACtD6V,QAAQ,GAAGA,CAAA,KAAMhW,MAAM,EAAE;IACzBkK,OAAO,CAAC1J,MAAM,CAACxK,gBAAgB,CAAC,OAAO,EAAEggB,QAAQ,CAAC;IAClD,OAAO9V,OAAO,CAACY,IAAI,CAAC,CAClBoV,OAAO,CAAC;MACNhM,OAAO;MACP9O,MAAM,EAAEH,KAAK,CAACG,MAAM;MACpB8W,OAAO,EAAEnJ,IAAI,CAAC6H;KACf,CAAC,EACF3Q,YAAY,CACb,CAAC;GACH;EAED,IAAI;IACF,IAAIiW,OAAO,GAAGjb,KAAK,CAAC3E,KAAK,CAAC2U,IAAI,CAAC;IAE/B,IAAIhQ,KAAK,CAAC3E,KAAK,CAACqP,IAAI,EAAE;MACpB,IAAIuQ,OAAO,EAAE;QACX;QACA,IAAI3E,MAAM,GAAG,MAAMrR,OAAO,CAAC6O,GAAG,CAAC,CAC7BkH,UAAU,CAACC,OAAO,CAAC,EACnBV,mBAAmB,CAACva,KAAK,CAAC3E,KAAK,EAAEG,kBAAkB,EAAEE,QAAQ,CAAC,CAC/D,CAAC;QACF2C,MAAM,GAAGiY,MAAM,CAAC,CAAC,CAAC;MACnB,OAAM;QACL;QACA,MAAMiE,mBAAmB,CAACva,KAAK,CAAC3E,KAAK,EAAEG,kBAAkB,EAAEE,QAAQ,CAAC;QAEpEuf,OAAO,GAAGjb,KAAK,CAAC3E,KAAK,CAAC2U,IAAI,CAAC;QAC3B,IAAIiL,OAAO,EAAE;UACX;UACA;UACA;UACA5c,MAAM,GAAG,MAAM2c,UAAU,CAACC,OAAO,CAAC;QACnC,OAAM,IAAIjL,IAAI,KAAK,QAAQ,EAAE;UAC5B,IAAIrX,GAAG,GAAG,IAAIjC,GAAG,CAACuY,OAAO,CAACtW,GAAG,CAAC;UAC9B,IAAI1C,QAAQ,GAAG0C,GAAG,CAAC1C,QAAQ,GAAG0C,GAAG,CAAC7B,MAAM;UACxC,MAAMwT,sBAAsB,CAAC,GAAG,EAAE;YAChC2F,MAAM,EAAEhB,OAAO,CAACgB,MAAM;YACtBha,QAAQ;YACRia,OAAO,EAAElQ,KAAK,CAAC3E,KAAK,CAACO;UACtB,EAAC;QACH,OAAM;UACL;UACA;UACA,OAAO;YAAEoU,IAAI,EAAE/U,UAAU,CAAC6I,IAAI;YAAEA,IAAI,EAAE5O;WAAW;QAClD;MACF;IACF,OAAM,IAAI,CAAC+lB,OAAO,EAAE;MACnB,IAAItiB,GAAG,GAAG,IAAIjC,GAAG,CAACuY,OAAO,CAACtW,GAAG,CAAC;MAC9B,IAAI1C,QAAQ,GAAG0C,GAAG,CAAC1C,QAAQ,GAAG0C,GAAG,CAAC7B,MAAM;MACxC,MAAMwT,sBAAsB,CAAC,GAAG,EAAE;QAChCrU;MACD,EAAC;IACH,OAAM;MACLoI,MAAM,GAAG,MAAM2c,UAAU,CAACC,OAAO,CAAC;IACnC;IAEDjiB,SAAS,CACPqF,MAAM,KAAKnJ,SAAS,EACpB,cAAe,IAAA8a,IAAI,KAAK,QAAQ,GAAG,WAAW,GAAG,UAAU,4BACrDhQ,KAAK,CAAC3E,KAAK,CAACO,EAAE,GAA4C,8CAAAoU,IAAI,GAAK,oDACzB,CACjD;GACF,CAAC,OAAOzW,CAAC,EAAE;IACVuhB,UAAU,GAAG7f,UAAU,CAACP,KAAK;IAC7B2D,MAAM,GAAG9E,CAAC;EACX,UAAS;IACR,IAAIwhB,QAAQ,EAAE;MACZ9L,OAAO,CAAC1J,MAAM,CAACvK,mBAAmB,CAAC,OAAO,EAAE+f,QAAQ,CAAC;IACtD;EACF;EAED,IAAI7E,UAAU,CAAC7X,MAAM,CAAC,EAAE;IACtB,IAAI4F,MAAM,GAAG5F,MAAM,CAAC4F,MAAM;IAE1B;IACA,IAAIkE,mBAAmB,CAAC/D,GAAG,CAACH,MAAM,CAAC,EAAE;MACnC,IAAIlO,QAAQ,GAAGsI,MAAM,CAAC6F,OAAO,CAACgC,GAAG,CAAC,UAAU,CAAC;MAC7ClN,SAAS,CACPjD,QAAQ,EACR,4EAA4E,CAC7E;MAED;MACA,IAAI,CAACgT,kBAAkB,CAACvJ,IAAI,CAACzJ,QAAQ,CAAC,EAAE;QACtCA,QAAQ,GAAGiY,WAAW,CACpB,IAAItX,GAAG,CAACuY,OAAO,CAACtW,GAAG,CAAC,EACpB6D,OAAO,CAAC1D,KAAK,CAAC,CAAC,EAAE0D,OAAO,CAAC3D,OAAO,CAACmH,KAAK,CAAC,GAAG,CAAC,CAAC,EAC5C7D,QAAQ,EACR,IAAI,EACJpG,QAAQ,CACT;MACF,OAAM,IAAI,CAAC+X,IAAI,CAACiJ,eAAe,EAAE;QAChC;QACA;QACA;QACA,IAAIkC,UAAU,GAAG,IAAIviB,GAAG,CAACuY,OAAO,CAACtW,GAAG,CAAC;QACrC,IAAIA,GAAG,GAAG5C,QAAQ,CAACmH,UAAU,CAAC,IAAI,CAAC,GAC/B,IAAIxG,GAAG,CAACuiB,UAAU,CAACiC,QAAQ,GAAGnlB,QAAQ,CAAC,GACvC,IAAIW,GAAG,CAACX,QAAQ,CAAC;QACrB,IAAIolB,cAAc,GAAG/e,aAAa,CAACzD,GAAG,CAAC1C,QAAQ,EAAEkG,QAAQ,CAAC,IAAI,IAAI;QAClE,IAAIxD,GAAG,CAACmC,MAAM,KAAKme,UAAU,CAACne,MAAM,IAAIqgB,cAAc,EAAE;UACtDplB,QAAQ,GAAG4C,GAAG,CAAC1C,QAAQ,GAAG0C,GAAG,CAAC7B,MAAM,GAAG6B,GAAG,CAAC5B,IAAI;QAChD;MACF;MAED;MACA;MACA;MACA;MACA,IAAI+W,IAAI,CAACiJ,eAAe,EAAE;QACxB1Y,MAAM,CAAC6F,OAAO,CAACG,GAAG,CAAC,UAAU,EAAEtO,QAAQ,CAAC;QACxC,MAAMsI,MAAM;MACb;MAED,OAAO;QACL2R,IAAI,EAAE/U,UAAU,CAACyM,QAAQ;QACzBzD,MAAM;QACNlO,QAAQ;QACRwY,UAAU,EAAElQ,MAAM,CAAC6F,OAAO,CAACgC,GAAG,CAAC,oBAAoB,CAAC,KAAK;OAC1D;IACF;IAED;IACA;IACA;IACA,IAAI4H,IAAI,CAACgJ,cAAc,EAAE;MACvB;MACA,MAAM;QACJ9G,IAAI,EAAE8K,UAAU,IAAI7f,UAAU,CAAC6I,IAAI;QACnC+S,QAAQ,EAAExY;OACX;IACF;IAED,IAAIyF,IAAS;IACb,IAAIsX,WAAW,GAAG/c,MAAM,CAAC6F,OAAO,CAACgC,GAAG,CAAC,cAAc,CAAC;IACpD;IACA;IACA,IAAIkV,WAAW,IAAI,uBAAuB,CAAC5b,IAAI,CAAC4b,WAAW,CAAC,EAAE;MAC5DtX,IAAI,GAAG,MAAMzF,MAAM,CAACwF,IAAI,EAAE;IAC3B,OAAM;MACLC,IAAI,GAAG,MAAMzF,MAAM,CAACqK,IAAI,EAAE;IAC3B;IAED,IAAIoS,UAAU,KAAK7f,UAAU,CAACP,KAAK,EAAE;MACnC,OAAO;QACLsV,IAAI,EAAE8K,UAAU;QAChBpgB,KAAK,EAAE,IAAIiN,aAAa,CAAC1D,MAAM,EAAE5F,MAAM,CAACuJ,UAAU,EAAE9D,IAAI,CAAC;QACzDI,OAAO,EAAE7F,MAAM,CAAC6F;OACjB;IACF;IAED,OAAO;MACL8L,IAAI,EAAE/U,UAAU,CAAC6I,IAAI;MACrBA,IAAI;MACJgS,UAAU,EAAEzX,MAAM,CAAC4F,MAAM;MACzBC,OAAO,EAAE7F,MAAM,CAAC6F;KACjB;EACF;EAED,IAAI4W,UAAU,KAAK7f,UAAU,CAACP,KAAK,EAAE;IACnC,OAAO;MAAEsV,IAAI,EAAE8K,UAAU;MAAEpgB,KAAK,EAAE2D;KAAQ;EAC3C;EAED,IAAIgd,cAAc,CAAChd,MAAM,CAAC,EAAE;IAAA,IAAAid,YAAA,EAAAC,aAAA;IAC1B,OAAO;MACLvL,IAAI,EAAE/U,UAAU,CAACugB,QAAQ;MACzB1J,YAAY,EAAEzT,MAAM;MACpByX,UAAU,GAAAwF,YAAA,GAAEjd,MAAM,CAAC0F,IAAI,qBAAXuX,YAAA,CAAarX,MAAM;MAC/BC,OAAO,EAAE,EAAAqX,aAAA,GAAAld,MAAM,CAAC0F,IAAI,KAAX,gBAAAwX,aAAA,CAAarX,OAAO,KAAI,IAAIC,OAAO,CAAC9F,MAAM,CAAC0F,IAAI,CAACG,OAAO;KACjE;EACF;EAED,OAAO;IAAE8L,IAAI,EAAE/U,UAAU,CAAC6I,IAAI;IAAEA,IAAI,EAAEzF;GAAQ;AAChD;AAEA;AACA;AACA;AACA,SAAS6Q,uBAAuBA,CAC9B1Y,OAAgB,EAChBT,QAA2B,EAC3BwP,MAAmB,EACnB4I,UAAuB;EAEvB,IAAIxV,GAAG,GAAGnC,OAAO,CAACC,SAAS,CAAC4hB,iBAAiB,CAACtiB,QAAQ,CAAC,CAAC,CAAC2D,QAAQ,EAAE;EACnE,IAAIqK,IAAI,GAAgB;IAAEwB;GAAQ;EAElC,IAAI4I,UAAU,IAAIX,gBAAgB,CAACW,UAAU,CAAC7F,UAAU,CAAC,EAAE;IACzD,IAAI;MAAEA,UAAU;MAAEE;IAAa,IAAG2F,UAAU;IAC5C;IACA;IACA;IACApK,IAAI,CAACkM,MAAM,GAAG3H,UAAU,CAAC8P,WAAW,EAAE;IAEtC,IAAI5P,WAAW,KAAK,kBAAkB,EAAE;MACtCzE,IAAI,CAACG,OAAO,GAAG,IAAIC,OAAO,CAAC;QAAE,cAAc,EAAEqE;MAAa,EAAC;MAC3DzE,IAAI,CAAC4T,IAAI,GAAGvhB,IAAI,CAACC,SAAS,CAAC8X,UAAU,CAACtK,IAAI,CAAC;IAC5C,OAAM,IAAI2E,WAAW,KAAK,YAAY,EAAE;MACvC;MACAzE,IAAI,CAAC4T,IAAI,GAAGxJ,UAAU,CAACzF,IAAI;KAC5B,MAAM,IACLF,WAAW,KAAK,mCAAmC,IACnD2F,UAAU,CAAC1F,QAAQ,EACnB;MACA;MACA1E,IAAI,CAAC4T,IAAI,GAAGgB,6BAA6B,CAACxK,UAAU,CAAC1F,QAAQ,CAAC;IAC/D,OAAM;MACL;MACA1E,IAAI,CAAC4T,IAAI,GAAGxJ,UAAU,CAAC1F,QAAQ;IAChC;EACF;EAED,OAAO,IAAIiH,OAAO,CAAC/W,GAAG,EAAEoL,IAAI,CAAC;AAC/B;AAEA,SAAS4U,6BAA6BA,CAAClQ,QAAkB;EACvD,IAAIiQ,YAAY,GAAG,IAAIH,eAAe,EAAE;EAExC,KAAK,IAAI,CAACziB,GAAG,EAAEmD,KAAK,CAAC,IAAIwP,QAAQ,CAAC7T,OAAO,EAAE,EAAE;IAC3C;IACA8jB,YAAY,CAACG,MAAM,CAAC/iB,GAAG,EAAE,OAAOmD,KAAK,KAAK,QAAQ,GAAGA,KAAK,GAAGA,KAAK,CAAC2B,IAAI,CAAC;EACzE;EAED,OAAO8d,YAAY;AACrB;AAEA,SAASE,6BAA6BA,CACpCF,YAA6B;EAE7B,IAAIjQ,QAAQ,GAAG,IAAI6P,QAAQ,EAAE;EAC7B,KAAK,IAAI,CAACxiB,GAAG,EAAEmD,KAAK,CAAC,IAAIyf,YAAY,CAAC9jB,OAAO,EAAE,EAAE;IAC/C6T,QAAQ,CAACoQ,MAAM,CAAC/iB,GAAG,EAAEmD,KAAK,CAAC;EAC5B;EACD,OAAOwP,QAAQ;AACjB;AAEA,SAAS2O,sBAAsBA,CAC7B5a,OAAiC,EACjCmU,aAAuC,EACvCY,OAAqB,EACrBjD,YAAmC,EACnCjC,eAA0C;EAO1C;EACA,IAAInB,UAAU,GAA8B,EAAE;EAC9C,IAAIE,MAAM,GAAiC,IAAI;EAC/C,IAAI0K,UAA8B;EAClC,IAAI2F,UAAU,GAAG,KAAK;EACtB,IAAI1F,aAAa,GAA4B,EAAE;EAE/C;EACAxE,OAAO,CAAC/T,OAAO,CAAC,CAACa,MAAM,EAAEtJ,KAAK,KAAI;IAChC,IAAI6G,EAAE,GAAG+U,aAAa,CAAC5b,KAAK,CAAC,CAACsG,KAAK,CAACO,EAAE;IACtC5C,SAAS,CACP,CAACoX,gBAAgB,CAAC/R,MAAM,CAAC,EACzB,qDAAqD,CACtD;IACD,IAAIiS,aAAa,CAACjS,MAAM,CAAC,EAAE;MACzB;MACA;MACA,IAAIkS,aAAa,GAAGnB,mBAAmB,CAAC5S,OAAO,EAAEZ,EAAE,CAAC;MACpD,IAAIlB,KAAK,GAAG2D,MAAM,CAAC3D,KAAK;MACxB;MACA;MACA;MACA,IAAI4T,YAAY,EAAE;QAChB5T,KAAK,GAAGwF,MAAM,CAACoW,MAAM,CAAChI,YAAY,CAAC,CAAC,CAAC,CAAC;QACtCA,YAAY,GAAGpZ,SAAS;MACzB;MAEDkW,MAAM,GAAGA,MAAM,IAAI,EAAE;MAErB;MACA,IAAIA,MAAM,CAACmF,aAAa,CAAClV,KAAK,CAACO,EAAE,CAAC,IAAI,IAAI,EAAE;QAC1CwP,MAAM,CAACmF,aAAa,CAAClV,KAAK,CAACO,EAAE,CAAC,GAAGlB,KAAK;MACvC;MAED;MACAwQ,UAAU,CAACtP,EAAE,CAAC,GAAG1G,SAAS;MAE1B;MACA;MACA,IAAI,CAACumB,UAAU,EAAE;QACfA,UAAU,GAAG,IAAI;QACjB3F,UAAU,GAAGhO,oBAAoB,CAACzJ,MAAM,CAAC3D,KAAK,CAAC,GAC3C2D,MAAM,CAAC3D,KAAK,CAACuJ,MAAM,GACnB,GAAG;MACR;MACD,IAAI5F,MAAM,CAAC6F,OAAO,EAAE;QAClB6R,aAAa,CAACna,EAAE,CAAC,GAAGyC,MAAM,CAAC6F,OAAO;MACnC;IACF,OAAM;MACL,IAAIsM,gBAAgB,CAACnS,MAAM,CAAC,EAAE;QAC5BgO,eAAe,CAAChI,GAAG,CAACzI,EAAE,EAAEyC,MAAM,CAACyT,YAAY,CAAC;QAC5C5G,UAAU,CAACtP,EAAE,CAAC,GAAGyC,MAAM,CAACyT,YAAY,CAAChO,IAAI;MAC1C,OAAM;QACLoH,UAAU,CAACtP,EAAE,CAAC,GAAGyC,MAAM,CAACyF,IAAI;MAC7B;MAED;MACA;MACA,IACEzF,MAAM,CAACyX,UAAU,IAAI,IAAI,IACzBzX,MAAM,CAACyX,UAAU,KAAK,GAAG,IACzB,CAAC2F,UAAU,EACX;QACA3F,UAAU,GAAGzX,MAAM,CAACyX,UAAU;MAC/B;MACD,IAAIzX,MAAM,CAAC6F,OAAO,EAAE;QAClB6R,aAAa,CAACna,EAAE,CAAC,GAAGyC,MAAM,CAAC6F,OAAO;MACnC;IACF;EACH,CAAC,CAAC;EAEF;EACA;EACA;EACA,IAAIoK,YAAY,EAAE;IAChBlD,MAAM,GAAGkD,YAAY;IACrBpD,UAAU,CAAChL,MAAM,CAACwN,IAAI,CAACY,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGpZ,SAAS;EACrD;EAED,OAAO;IACLgW,UAAU;IACVE,MAAM;IACN0K,UAAU,EAAEA,UAAU,IAAI,GAAG;IAC7BC;GACD;AACH;AAEA,SAASlE,iBAAiBA,CACxB5c,KAAkB,EAClBuH,OAAiC,EACjCmU,aAAuC,EACvCY,OAAqB,EACrBjD,YAAmC,EACnCsC,oBAA2C,EAC3Ca,cAA4B,EAC5BpF,eAA0C;EAK1C,IAAI;IAAEnB,UAAU;IAAEE;EAAQ,IAAGgM,sBAAsB,CACjD5a,OAAO,EACPmU,aAAa,EACbY,OAAO,EACPjD,YAAY,EACZjC,eAAe,CAChB;EAED;EACA,KAAK,IAAItX,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAG6b,oBAAoB,CAACxb,MAAM,EAAEL,KAAK,EAAE,EAAE;IAChE,IAAI;MAAEe,GAAG;MAAEkK,KAAK;MAAEmF;IAAY,IAAGyL,oBAAoB,CAAC7b,KAAK,CAAC;IAC5DiE,SAAS,CACPyY,cAAc,KAAKvc,SAAS,IAAIuc,cAAc,CAAC1c,KAAK,CAAC,KAAKG,SAAS,EACnE,2CAA2C,CAC5C;IACD,IAAImJ,MAAM,GAAGoT,cAAc,CAAC1c,KAAK,CAAC;IAElC;IACA,IAAIoQ,UAAU,IAAIA,UAAU,CAACI,MAAM,CAACY,OAAO,EAAE;MAC3C;MACA;IACD,OAAM,IAAImK,aAAa,CAACjS,MAAM,CAAC,EAAE;MAChC,IAAIkS,aAAa,GAAGnB,mBAAmB,CAACna,KAAK,CAACuH,OAAO,EAAEwD,KAAK,oBAALA,KAAK,CAAE3E,KAAK,CAACO,EAAE,CAAC;MACvE,IAAI,EAAEwP,MAAM,IAAIA,MAAM,CAACmF,aAAa,CAAClV,KAAK,CAACO,EAAE,CAAC,CAAC,EAAE;QAC/CwP,MAAM,GAAAtR,QAAA,KACDsR,MAAM;UACT,CAACmF,aAAa,CAAClV,KAAK,CAACO,EAAE,GAAGyC,MAAM,CAAC3D;SAClC;MACF;MACDzF,KAAK,CAACoW,QAAQ,CAACjF,MAAM,CAACtQ,GAAG,CAAC;IAC3B,OAAM,IAAIsa,gBAAgB,CAAC/R,MAAM,CAAC,EAAE;MACnC;MACA;MACArF,SAAS,CAAC,KAAK,EAAE,yCAAyC,CAAC;IAC5D,OAAM,IAAIwX,gBAAgB,CAACnS,MAAM,CAAC,EAAE;MACnC;MACA;MACArF,SAAS,CAAC,KAAK,EAAE,iCAAiC,CAAC;IACpD,OAAM;MACL,IAAI8Z,WAAW,GAAGC,cAAc,CAAC1U,MAAM,CAACyF,IAAI,CAAC;MAC7C7O,KAAK,CAACoW,QAAQ,CAAChH,GAAG,CAACvO,GAAG,EAAEgd,WAAW,CAAC;IACrC;EACF;EAED,OAAO;IAAE5H,UAAU;IAAEE;GAAQ;AAC/B;AAEA,SAASuC,eAAeA,CACtBzC,UAAqB,EACrBwQ,aAAwB,EACxBlf,OAAiC,EACjC4O,MAAoC;EAEpC,IAAIuQ,gBAAgB,GAAA7hB,QAAA,KAAQ4hB,aAAa,CAAE;EAC3C,KAAK,IAAI1b,KAAK,IAAIxD,OAAO,EAAE;IACzB,IAAIZ,EAAE,GAAGoE,KAAK,CAAC3E,KAAK,CAACO,EAAE;IACvB,IAAI8f,aAAa,CAACE,cAAc,CAAChgB,EAAE,CAAC,EAAE;MACpC,IAAI8f,aAAa,CAAC9f,EAAE,CAAC,KAAK1G,SAAS,EAAE;QACnCymB,gBAAgB,CAAC/f,EAAE,CAAC,GAAG8f,aAAa,CAAC9f,EAAE,CAAC;MACzC;IAKF,OAAM,IAAIsP,UAAU,CAACtP,EAAE,CAAC,KAAK1G,SAAS,IAAI8K,KAAK,CAAC3E,KAAK,CAACsP,MAAM,EAAE;MAC7D;MACA;MACAgR,gBAAgB,CAAC/f,EAAE,CAAC,GAAGsP,UAAU,CAACtP,EAAE,CAAC;IACtC;IAED,IAAIwP,MAAM,IAAIA,MAAM,CAACwQ,cAAc,CAAChgB,EAAE,CAAC,EAAE;MACvC;MACA;IACD;EACF;EACD,OAAO+f,gBAAgB;AACzB;AAEA;AACA;AACA;AACA,SAASvM,mBAAmBA,CAC1B5S,OAAiC,EACjC0T,OAAgB;EAEhB,IAAI2L,eAAe,GAAG3L,OAAO,GACzB1T,OAAO,CAAC1D,KAAK,CAAC,CAAC,EAAE0D,OAAO,CAACwc,SAAS,CAAEvO,CAAC,IAAKA,CAAC,CAACpP,KAAK,CAACO,EAAE,KAAKsU,OAAO,CAAC,GAAG,CAAC,CAAC,GACtE,CAAC,GAAG1T,OAAO,CAAC;EAChB,OACEqf,eAAe,CAACC,OAAO,EAAE,CAACzF,IAAI,CAAE5L,CAAC,IAAKA,CAAC,CAACpP,KAAK,CAAC4N,gBAAgB,KAAK,IAAI,CAAC,IACxEzM,OAAO,CAAC,CAAC,CAAC;AAEd;AAEA,SAAS+N,sBAAsBA,CAAChP,MAAiC;EAI/D;EACA,IAAIF,KAAK,GAAGE,MAAM,CAAC8a,IAAI,CAAEnR,CAAC,IAAKA,CAAC,CAACnQ,KAAK,IAAI,CAACmQ,CAAC,CAACtO,IAAI,IAAIsO,CAAC,CAACtO,IAAI,KAAK,GAAG,CAAC,IAAI;IACtEgF,EAAE;GACH;EAED,OAAO;IACLY,OAAO,EAAE,CACP;MACE2D,MAAM,EAAE,EAAE;MACVlK,QAAQ,EAAE,EAAE;MACZmK,YAAY,EAAE,EAAE;MAChB/E;IACD,EACF;IACDA;GACD;AACH;AAEA,SAASiP,sBAAsBA,CAC7BrG,MAAc,EAAA8X,MAAA,EAWR;EAAA,IAVN;IACE9lB,QAAQ;IACRia,OAAO;IACPD,MAAM;IACND;0BAME,EAAE,GAAA+L,MAAA;EAEN,IAAInU,UAAU,GAAG,sBAAsB;EACvC,IAAIoU,YAAY,GAAG,iCAAiC;EAEpD,IAAI/X,MAAM,KAAK,GAAG,EAAE;IAClB2D,UAAU,GAAG,aAAa;IAC1B,IAAIqI,MAAM,IAAIha,QAAQ,IAAIia,OAAO,EAAE;MACjC8L,YAAY,GACV,gBAAc/L,MAAM,sBAAgBha,QAAQ,GACD,yDAAAia,OAAO,UAAK,GACZ;IAC9C,OAAM,IAAIF,IAAI,KAAK,cAAc,EAAE;MAClCgM,YAAY,GAAG,qCAAqC;IACrD,OAAM,IAAIhM,IAAI,KAAK,cAAc,EAAE;MAClCgM,YAAY,GAAG,kCAAkC;IAClD;EACF,OAAM,IAAI/X,MAAM,KAAK,GAAG,EAAE;IACzB2D,UAAU,GAAG,WAAW;IACxBoU,YAAY,GAAa,aAAA9L,OAAO,GAAyB,6BAAAja,QAAQ,GAAG;EACrE,OAAM,IAAIgO,MAAM,KAAK,GAAG,EAAE;IACzB2D,UAAU,GAAG,WAAW;IACxBoU,YAAY,+BAA4B/lB,QAAQ,GAAG;EACpD,OAAM,IAAIgO,MAAM,KAAK,GAAG,EAAE;IACzB2D,UAAU,GAAG,oBAAoB;IACjC,IAAIqI,MAAM,IAAIha,QAAQ,IAAIia,OAAO,EAAE;MACjC8L,YAAY,GACV,gBAAc/L,MAAM,CAACmI,WAAW,EAAE,sBAAgBniB,QAAQ,6DACdia,OAAO,UAAK,GACb;KAC9C,MAAM,IAAID,MAAM,EAAE;MACjB+L,YAAY,iCAA8B/L,MAAM,CAACmI,WAAW,EAAE,GAAG;IAClE;EACF;EAED,OAAO,IAAIzQ,aAAa,CACtB1D,MAAM,IAAI,GAAG,EACb2D,UAAU,EACV,IAAIzO,KAAK,CAAC6iB,YAAY,CAAC,EACvB,IAAI,CACL;AACH;AAEA;AACA,SAASrK,YAAYA,CACnBJ,OAAqB;EAErB,KAAK,IAAI9U,CAAC,GAAG8U,OAAO,CAACnc,MAAM,GAAG,CAAC,EAAEqH,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;IAC5C,IAAI4B,MAAM,GAAGkT,OAAO,CAAC9U,CAAC,CAAC;IACvB,IAAI2T,gBAAgB,CAAC/R,MAAM,CAAC,EAAE;MAC5B,OAAO;QAAEA,MAAM;QAAEzE,GAAG,EAAE6C;OAAG;IAC1B;EACF;AACH;AAEA,SAAS4b,iBAAiBA,CAACzhB,IAAQ;EACjC,IAAIoD,UAAU,GAAG,OAAOpD,IAAI,KAAK,QAAQ,GAAGC,SAAS,CAACD,IAAI,CAAC,GAAGA,IAAI;EAClE,OAAOL,UAAU,CAAAuD,QAAA,KAAME,UAAU;IAAEjD,IAAI,EAAE;EAAE,EAAE,CAAC;AAChD;AAEA,SAASiY,gBAAgBA,CAACxQ,CAAW,EAAEC,CAAW;EAChD,IAAID,CAAC,CAACvI,QAAQ,KAAKwI,CAAC,CAACxI,QAAQ,IAAIuI,CAAC,CAAC1H,MAAM,KAAK2H,CAAC,CAAC3H,MAAM,EAAE;IACtD,OAAO,KAAK;EACb;EAED,IAAI0H,CAAC,CAACzH,IAAI,KAAK,EAAE,EAAE;IACjB;IACA,OAAO0H,CAAC,CAAC1H,IAAI,KAAK,EAAE;GACrB,MAAM,IAAIyH,CAAC,CAACzH,IAAI,KAAK0H,CAAC,CAAC1H,IAAI,EAAE;IAC5B;IACA,OAAO,IAAI;EACZ,OAAM,IAAI0H,CAAC,CAAC1H,IAAI,KAAK,EAAE,EAAE;IACxB;IACA,OAAO,IAAI;EACZ;EAED;EACA;EACA,OAAO,KAAK;AACd;AAEA,SAASyZ,gBAAgBA,CAACnS,MAAkB;EAC1C,OAAOA,MAAM,CAAC2R,IAAI,KAAK/U,UAAU,CAACugB,QAAQ;AAC5C;AAEA,SAASlL,aAAaA,CAACjS,MAAkB;EACvC,OAAOA,MAAM,CAAC2R,IAAI,KAAK/U,UAAU,CAACP,KAAK;AACzC;AAEA,SAAS0V,gBAAgBA,CAAC/R,MAAmB;EAC3C,OAAO,CAACA,MAAM,IAAIA,MAAM,CAAC2R,IAAI,MAAM/U,UAAU,CAACyM,QAAQ;AACxD;AAEM,SAAU2T,cAAcA,CAACpiB,KAAU;EACvC,IAAIuiB,QAAQ,GAAiBviB,KAAK;EAClC,OACEuiB,QAAQ,IACR,OAAOA,QAAQ,KAAK,QAAQ,IAC5B,OAAOA,QAAQ,CAAC1X,IAAI,KAAK,QAAQ,IACjC,OAAO0X,QAAQ,CAAC/U,SAAS,KAAK,UAAU,IACxC,OAAO+U,QAAQ,CAAC9U,MAAM,KAAK,UAAU,IACrC,OAAO8U,QAAQ,CAAC1U,WAAW,KAAK,UAAU;AAE9C;AAEA,SAASoP,UAAUA,CAACjd,KAAU;EAC5B,OACEA,KAAK,IAAI,IAAI,IACb,OAAOA,KAAK,CAACgL,MAAM,KAAK,QAAQ,IAChC,OAAOhL,KAAK,CAAC2O,UAAU,KAAK,QAAQ,IACpC,OAAO3O,KAAK,CAACiL,OAAO,KAAK,QAAQ,IACjC,OAAOjL,KAAK,CAAC0e,IAAI,KAAK,WAAW;AAErC;AAEA,SAASf,kBAAkBA,CAACvY,MAAW;EACrC,IAAI,CAAC6X,UAAU,CAAC7X,MAAM,CAAC,EAAE;IACvB,OAAO,KAAK;EACb;EAED,IAAI4F,MAAM,GAAG5F,MAAM,CAAC4F,MAAM;EAC1B,IAAIlO,QAAQ,GAAGsI,MAAM,CAAC6F,OAAO,CAACgC,GAAG,CAAC,UAAU,CAAC;EAC7C,OAAOjC,MAAM,IAAI,GAAG,IAAIA,MAAM,IAAI,GAAG,IAAIlO,QAAQ,IAAI,IAAI;AAC3D;AAEA,SAAS4gB,oBAAoBA,CAACsF,GAAQ;EACpC,OACEA,GAAG,IACH/F,UAAU,CAAC+F,GAAG,CAACpF,QAAQ,CAAC,KACvBoF,GAAG,CAACjM,IAAI,KAAK/U,UAAU,CAAC6I,IAAI,IAAI7I,UAAU,CAACP,KAAK,CAAC;AAEtD;AAEA,SAASkb,aAAaA,CAAC3F,MAAc;EACnC,OAAO/H,mBAAmB,CAAC9D,GAAG,CAAC6L,MAAM,CAAClO,WAAW,EAAgB,CAAC;AACpE;AAEA,SAASyL,gBAAgBA,CACvByC,MAAc;EAEd,OAAOjI,oBAAoB,CAAC5D,GAAG,CAAC6L,MAAM,CAAClO,WAAW,EAAwB,CAAC;AAC7E;AAEA,eAAegS,sBAAsBA,CACnCH,cAAwC,EACxCjD,aAAgD,EAChDY,OAAqB,EACrB2K,OAA+B,EAC/BjE,SAAkB,EAClB8B,iBAA6B;EAE7B,KAAK,IAAIhlB,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGwc,OAAO,CAACnc,MAAM,EAAEL,KAAK,EAAE,EAAE;IACnD,IAAIsJ,MAAM,GAAGkT,OAAO,CAACxc,KAAK,CAAC;IAC3B,IAAIiL,KAAK,GAAG2Q,aAAa,CAAC5b,KAAK,CAAC;IAChC;IACA;IACA;IACA,IAAI,CAACiL,KAAK,EAAE;MACV;IACD;IAED,IAAIga,YAAY,GAAGpG,cAAc,CAACyC,IAAI,CACnC5L,CAAC,IAAKA,CAAC,CAACpP,KAAK,CAACO,EAAE,KAAKoE,KAAM,CAAC3E,KAAK,CAACO,EAAE,CACtC;IACD,IAAIugB,oBAAoB,GACtBnC,YAAY,IAAI,IAAI,IACpB,CAACL,kBAAkB,CAACK,YAAY,EAAEha,KAAK,CAAC,IACxC,CAAC+Z,iBAAiB,IAAIA,iBAAiB,CAAC/Z,KAAK,CAAC3E,KAAK,CAACO,EAAE,CAAC,MAAM1G,SAAS;IAExE,IAAIsb,gBAAgB,CAACnS,MAAM,CAAC,KAAK4Z,SAAS,IAAIkE,oBAAoB,CAAC,EAAE;MACnE;MACA;MACA;MACA,IAAI5W,MAAM,GAAG2W,OAAO,CAACnnB,KAAK,CAAC;MAC3BiE,SAAS,CACPuM,MAAM,EACN,kEAAkE,CACnE;MACD,MAAM+N,mBAAmB,CAACjV,MAAM,EAAEkH,MAAM,EAAE0S,SAAS,CAAC,CAACnS,IAAI,CAAEzH,MAAM,IAAI;QACnE,IAAIA,MAAM,EAAE;UACVkT,OAAO,CAACxc,KAAK,CAAC,GAAGsJ,MAAM,IAAIkT,OAAO,CAACxc,KAAK,CAAC;QAC1C;MACH,CAAC,CAAC;IACH;EACF;AACH;AAEA,eAAeue,mBAAmBA,CAChCjV,MAAsB,EACtBkH,MAAmB,EACnB6W,MAAM,EAAQ;EAAA,IAAdA,MAAM;IAANA,MAAM,GAAG,KAAK;EAAA;EAEd,IAAIjW,OAAO,GAAG,MAAM9H,MAAM,CAACyT,YAAY,CAAChL,WAAW,CAACvB,MAAM,CAAC;EAC3D,IAAIY,OAAO,EAAE;IACX;EACD;EAED,IAAIiW,MAAM,EAAE;IACV,IAAI;MACF,OAAO;QACLpM,IAAI,EAAE/U,UAAU,CAAC6I,IAAI;QACrBA,IAAI,EAAEzF,MAAM,CAACyT,YAAY,CAAC7K;OAC3B;KACF,CAAC,OAAO1N,CAAC,EAAE;MACV;MACA,OAAO;QACLyW,IAAI,EAAE/U,UAAU,CAACP,KAAK;QACtBA,KAAK,EAAEnB;OACR;IACF;EACF;EAED,OAAO;IACLyW,IAAI,EAAE/U,UAAU,CAAC6I,IAAI;IACrBA,IAAI,EAAEzF,MAAM,CAACyT,YAAY,CAAChO;GAC3B;AACH;AAEA,SAASiU,kBAAkBA,CAACjhB,MAAc;EACxC,OAAO,IAAIyhB,eAAe,CAACzhB,MAAM,CAAC,CAACulB,MAAM,CAAC,OAAO,CAAC,CAACjd,IAAI,CAAEwH,CAAC,IAAKA,CAAC,KAAK,EAAE,CAAC;AAC1E;AAEA;AACA;AACA,SAASqO,qBAAqBA,CAC5BjV,KAA6B,EAC7BkL,UAAqB;EAErB,IAAI;IAAE7P,KAAK;IAAEpF,QAAQ;IAAEkK;EAAM,CAAE,GAAGH,KAAK;EACvC,OAAO;IACLpE,EAAE,EAAEP,KAAK,CAACO,EAAE;IACZ3F,QAAQ;IACRkK,MAAM;IACN2D,IAAI,EAAEoH,UAAU,CAAC7P,KAAK,CAACO,EAAE,CAAY;IACrC0gB,MAAM,EAAEjhB,KAAK,CAACihB;GACf;AACH;AAEA,SAASvM,cAAcA,CACrBvT,OAAiC,EACjCzG,QAA2B;EAE3B,IAAIe,MAAM,GACR,OAAOf,QAAQ,KAAK,QAAQ,GAAGc,SAAS,CAACd,QAAQ,CAAC,CAACe,MAAM,GAAGf,QAAQ,CAACe,MAAM;EAC7E,IACE0F,OAAO,CAACA,OAAO,CAACpH,MAAM,GAAG,CAAC,CAAC,CAACiG,KAAK,CAACtG,KAAK,IACvCgjB,kBAAkB,CAACjhB,MAAM,IAAI,EAAE,CAAC,EAChC;IACA;IACA,OAAO0F,OAAO,CAACA,OAAO,CAACpH,MAAM,GAAG,CAAC,CAAC;EACnC;EACD;EACA;EACA,IAAImnB,WAAW,GAAGzZ,0BAA0B,CAACtG,OAAO,CAAC;EACrD,OAAO+f,WAAW,CAACA,WAAW,CAACnnB,MAAM,GAAG,CAAC,CAAC;AAC5C;AAEA,SAASsb,2BAA2BA,CAClC5F,UAAsB;EAEtB,IAAI;IAAExC,UAAU;IAAEC,UAAU;IAAEC,WAAW;IAAEE,IAAI;IAAED,QAAQ;IAAE5E;EAAM,IAC/DiH,UAAU;EACZ,IAAI,CAACxC,UAAU,IAAI,CAACC,UAAU,IAAI,CAACC,WAAW,EAAE;IAC9C;EACD;EAED,IAAIE,IAAI,IAAI,IAAI,EAAE;IAChB,OAAO;MACLJ,UAAU;MACVC,UAAU;MACVC,WAAW;MACXC,QAAQ,EAAEvT,SAAS;MACnB2O,IAAI,EAAE3O,SAAS;MACfwT;KACD;EACF,OAAM,IAAID,QAAQ,IAAI,IAAI,EAAE;IAC3B,OAAO;MACLH,UAAU;MACVC,UAAU;MACVC,WAAW;MACXC,QAAQ;MACR5E,IAAI,EAAE3O,SAAS;MACfwT,IAAI,EAAExT;KACP;EACF,OAAM,IAAI2O,IAAI,KAAK3O,SAAS,EAAE;IAC7B,OAAO;MACLoT,UAAU;MACVC,UAAU;MACVC,WAAW;MACXC,QAAQ,EAAEvT,SAAS;MACnB2O,IAAI;MACJ6E,IAAI,EAAExT;KACP;EACF;AACH;AAEA,SAASua,oBAAoBA,CAC3B1Z,QAAkB,EAClBoY,UAAuB;EAEvB,IAAIA,UAAU,EAAE;IACd,IAAIrD,UAAU,GAAgC;MAC5C7V,KAAK,EAAE,SAAS;MAChBc,QAAQ;MACRuS,UAAU,EAAE6F,UAAU,CAAC7F,UAAU;MACjCC,UAAU,EAAE4F,UAAU,CAAC5F,UAAU;MACjCC,WAAW,EAAE2F,UAAU,CAAC3F,WAAW;MACnCC,QAAQ,EAAE0F,UAAU,CAAC1F,QAAQ;MAC7B5E,IAAI,EAAEsK,UAAU,CAACtK,IAAI;MACrB6E,IAAI,EAAEyF,UAAU,CAACzF;KAClB;IACD,OAAOoC,UAAU;EAClB,OAAM;IACL,IAAIA,UAAU,GAAgC;MAC5C7V,KAAK,EAAE,SAAS;MAChBc,QAAQ;MACRuS,UAAU,EAAEpT,SAAS;MACrBqT,UAAU,EAAErT,SAAS;MACrBsT,WAAW,EAAEtT,SAAS;MACtBuT,QAAQ,EAAEvT,SAAS;MACnB2O,IAAI,EAAE3O,SAAS;MACfwT,IAAI,EAAExT;KACP;IACD,OAAO4V,UAAU;EAClB;AACH;AAEA,SAAS+E,uBAAuBA,CAC9B9Z,QAAkB,EAClBoY,UAAsB;EAEtB,IAAIrD,UAAU,GAAmC;IAC/C7V,KAAK,EAAE,YAAY;IACnBc,QAAQ;IACRuS,UAAU,EAAE6F,UAAU,CAAC7F,UAAU;IACjCC,UAAU,EAAE4F,UAAU,CAAC5F,UAAU;IACjCC,WAAW,EAAE2F,UAAU,CAAC3F,WAAW;IACnCC,QAAQ,EAAE0F,UAAU,CAAC1F,QAAQ;IAC7B5E,IAAI,EAAEsK,UAAU,CAACtK,IAAI;IACrB6E,IAAI,EAAEyF,UAAU,CAACzF;GAClB;EACD,OAAOoC,UAAU;AACnB;AAEA,SAASqG,iBAAiBA,CACxBhD,UAAuB,EACvBrK,IAAsB;EAEtB,IAAIqK,UAAU,EAAE;IACd,IAAI8C,OAAO,GAA6B;MACtChc,KAAK,EAAE,SAAS;MAChBqT,UAAU,EAAE6F,UAAU,CAAC7F,UAAU;MACjCC,UAAU,EAAE4F,UAAU,CAAC5F,UAAU;MACjCC,WAAW,EAAE2F,UAAU,CAAC3F,WAAW;MACnCC,QAAQ,EAAE0F,UAAU,CAAC1F,QAAQ;MAC7B5E,IAAI,EAAEsK,UAAU,CAACtK,IAAI;MACrB6E,IAAI,EAAEyF,UAAU,CAACzF,IAAI;MACrB5E,IAAI;MACJ,2BAA2B,EAAE;KAC9B;IACD,OAAOmN,OAAO;EACf,OAAM;IACL,IAAIA,OAAO,GAA6B;MACtChc,KAAK,EAAE,SAAS;MAChBqT,UAAU,EAAEpT,SAAS;MACrBqT,UAAU,EAAErT,SAAS;MACrBsT,WAAW,EAAEtT,SAAS;MACtBuT,QAAQ,EAAEvT,SAAS;MACnB2O,IAAI,EAAE3O,SAAS;MACfwT,IAAI,EAAExT,SAAS;MACf4O,IAAI;MACJ,2BAA2B,EAAE;KAC9B;IACD,OAAOmN,OAAO;EACf;AACH;AAEA,SAASwB,oBAAoBA,CAC3BtE,UAAsB,EACtBqE,eAAyB;EAEzB,IAAIvB,OAAO,GAAgC;IACzChc,KAAK,EAAE,YAAY;IACnBqT,UAAU,EAAE6F,UAAU,CAAC7F,UAAU;IACjCC,UAAU,EAAE4F,UAAU,CAAC5F,UAAU;IACjCC,WAAW,EAAE2F,UAAU,CAAC3F,WAAW;IACnCC,QAAQ,EAAE0F,UAAU,CAAC1F,QAAQ;IAC7B5E,IAAI,EAAEsK,UAAU,CAACtK,IAAI;IACrB6E,IAAI,EAAEyF,UAAU,CAACzF,IAAI;IACrB5E,IAAI,EAAE0O,eAAe,GAAGA,eAAe,CAAC1O,IAAI,GAAG5O,SAAS;IACxD,2BAA2B,EAAE;GAC9B;EACD,OAAO+b,OAAO;AAChB;AAEA,SAAS8B,cAAcA,CAACjP,IAAqB;EAC3C,IAAImN,OAAO,GAA0B;IACnChc,KAAK,EAAE,MAAM;IACbqT,UAAU,EAAEpT,SAAS;IACrBqT,UAAU,EAAErT,SAAS;IACrBsT,WAAW,EAAEtT,SAAS;IACtBuT,QAAQ,EAAEvT,SAAS;IACnB2O,IAAI,EAAE3O,SAAS;IACfwT,IAAI,EAAExT,SAAS;IACf4O,IAAI;IACJ,2BAA2B,EAAE;GAC9B;EACD,OAAOmN,OAAO;AAChB;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}