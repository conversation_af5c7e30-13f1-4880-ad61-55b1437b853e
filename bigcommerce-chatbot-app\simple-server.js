const http = require('http');
const fs = require('fs');
const path = require('path');
const url = require('url');

const PORT = process.env.PORT || 3001;

// In-memory storage
let conversations = [];
let notifications = [];

// Simple UUID generator
function generateUUID() {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0;
    const v = c == 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
}

// MIME types
const mimeTypes = {
  '.html': 'text/html',
  '.js': 'text/javascript',
  '.css': 'text/css',
  '.json': 'application/json',
  '.png': 'image/png',
  '.jpg': 'image/jpg',
  '.gif': 'image/gif',
  '.ico': 'image/x-icon'
};

// Serve static files
function serveStaticFile(res, filePath) {
  const extname = path.extname(filePath).toLowerCase();
  const contentType = mimeTypes[extname] || 'application/octet-stream';

  fs.readFile(filePath, (error, content) => {
    if (error) {
      if (error.code == 'ENOENT') {
        res.writeHead(404, { 'Content-Type': 'text/html' });
        res.end('<h1>404 Not Found</h1>', 'utf-8');
      } else {
        res.writeHead(500);
        res.end(`Server Error: ${error.code}`, 'utf-8');
      }
    } else {
      res.writeHead(200, { 'Content-Type': contentType });
      res.end(content, 'utf-8');
    }
  });
}

// Parse JSON body
function parseJSON(req, callback) {
  let body = '';
  req.on('data', chunk => {
    body += chunk.toString();
  });
  req.on('end', () => {
    try {
      const parsed = JSON.parse(body);
      callback(null, parsed);
    } catch (error) {
      callback(error, null);
    }
  });
}

// Create HTTP server
const server = http.createServer((req, res) => {
  const parsedUrl = url.parse(req.url, true);
  const pathname = parsedUrl.pathname;
  const method = req.method;

  // Enable CORS
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');

  if (method === 'OPTIONS') {
    res.writeHead(200);
    res.end();
    return;
  }

  // Routes
  if (pathname === '/' || pathname === '/admin') {
    serveStaticFile(res, path.join(__dirname, 'public', 'admin.html'));
  } 
  else if (pathname === '/widget') {
    serveStaticFile(res, path.join(__dirname, 'public', 'widget.html'));
  }
  else if (pathname === '/auth') {
    // BigCommerce OAuth callback
    const { code, scope, context } = parsedUrl.query;
    if (code) {
      res.writeHead(302, { 'Location': `/admin?context=${context}` });
      res.end();
    } else {
      res.writeHead(400, { 'Content-Type': 'text/html' });
      res.end('<h1>Authorization code required</h1>');
    }
  }
  // API Routes
  else if (pathname === '/api/conversations' && method === 'GET') {
    res.writeHead(200, { 'Content-Type': 'application/json' });
    res.end(JSON.stringify(conversations));
  }
  else if (pathname === '/api/conversations' && method === 'POST') {
    parseJSON(req, (error, data) => {
      if (error) {
        res.writeHead(400, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({ error: 'Invalid JSON' }));
        return;
      }

      const conversation = {
        id: generateUUID(),
        customerName: data.customerName || 'Anonymous',
        customerEmail: data.customerEmail || '',
        storeContext: data.storeContext || '',
        status: 'active',
        assignedAgent: null,
        messages: [],
        createdAt: new Date(),
        lastActivity: new Date()
      };

      conversations.unshift(conversation);

      // Add notification
      notifications.unshift({
        id: generateUUID(),
        type: 'new-conversation',
        message: `New chat from ${conversation.customerName}`,
        timestamp: new Date(),
        read: false
      });

      res.writeHead(200, { 'Content-Type': 'application/json' });
      res.end(JSON.stringify(conversation));
    });
  }
  else if (pathname.startsWith('/api/conversations/') && pathname.endsWith('/messages') && method === 'POST') {
    const conversationId = pathname.split('/')[3];
    const conversation = conversations.find(c => c.id === conversationId);

    if (!conversation) {
      res.writeHead(404, { 'Content-Type': 'application/json' });
      res.end(JSON.stringify({ error: 'Conversation not found' }));
      return;
    }

    parseJSON(req, (error, data) => {
      if (error) {
        res.writeHead(400, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({ error: 'Invalid JSON' }));
        return;
      }

      const message = {
        id: generateUUID(),
        message: data.message,
        sender: data.sender,
        senderType: data.senderType,
        timestamp: new Date()
      };

      conversation.messages.push(message);
      conversation.lastActivity = new Date();

      // Add notification for customer messages
      if (data.senderType === 'customer') {
        notifications.unshift({
          id: generateUUID(),
          type: 'new-message',
          conversationId: conversationId,
          customerName: conversation.customerName,
          message: data.message.substring(0, 50) + (data.message.length > 50 ? '...' : ''),
          timestamp: new Date(),
          read: false
        });
      }

      res.writeHead(200, { 'Content-Type': 'application/json' });
      res.end(JSON.stringify(message));
    });
  }
  else if (pathname === '/api/notifications' && method === 'GET') {
    res.writeHead(200, { 'Content-Type': 'application/json' });
    res.end(JSON.stringify(notifications.slice(0, 10)));
  }
  else if (pathname.startsWith('/api/conversations/') && pathname.endsWith('/assign') && method === 'PUT') {
    const conversationId = pathname.split('/')[3];
    const conversation = conversations.find(c => c.id === conversationId);

    if (!conversation) {
      res.writeHead(404, { 'Content-Type': 'application/json' });
      res.end(JSON.stringify({ error: 'Conversation not found' }));
      return;
    }

    parseJSON(req, (error, data) => {
      if (error) {
        res.writeHead(400, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({ error: 'Invalid JSON' }));
        return;
      }

      conversation.assignedAgent = data.agentName;

      res.writeHead(200, { 'Content-Type': 'application/json' });
      res.end(JSON.stringify(conversation));
    });
  }
  else if (pathname === '/health') {
    res.writeHead(200, { 'Content-Type': 'application/json' });
    res.end(JSON.stringify({
      status: 'OK',
      version: 'v1.0',
      conversations: conversations.length,
      notifications: notifications.length,
      timestamp: new Date().toISOString()
    }));
  }
  else {
    // Try to serve static file
    const filePath = path.join(__dirname, 'public', pathname);
    serveStaticFile(res, filePath);
  }
});

server.listen(PORT, () => {
  console.log(`🚀 BigCommerce ChatBot App running on port ${PORT}`);
  console.log(`📊 Admin Dashboard: http://localhost:${PORT}/admin`);
  console.log(`💬 Chat Widget: http://localhost:${PORT}/widget`);
  console.log(`🔗 Health Check: http://localhost:${PORT}/health`);
});
