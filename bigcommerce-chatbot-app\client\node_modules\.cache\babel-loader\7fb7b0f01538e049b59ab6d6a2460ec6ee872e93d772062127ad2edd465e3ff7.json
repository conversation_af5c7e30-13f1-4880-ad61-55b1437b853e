{"ast": null, "code": "import { Transport } from \"../transport.js\";\nimport { nextTick } from \"./websocket-constructor.js\";\nimport { createPacketDecoderStream, createPacketEncoderStream } from \"engine.io-parser\";\nexport class WT extends Transport {\n  get name() {\n    return \"webtransport\";\n  }\n  doOpen() {\n    // @ts-ignore\n    if (typeof WebTransport !== \"function\") {\n      return;\n    }\n    // @ts-ignore\n    this.transport = new WebTransport(this.createUri(\"https\"), this.opts.transportOptions[this.name]);\n    this.transport.closed.then(() => {\n      this.onClose();\n    }).catch(err => {\n      this.onError(\"webtransport error\", err);\n    });\n    // note: we could have used async/await, but that would require some additional polyfills\n    this.transport.ready.then(() => {\n      this.transport.createBidirectionalStream().then(stream => {\n        const decoderStream = createPacketDecoderStream(Number.MAX_SAFE_INTEGER, this.socket.binaryType);\n        const reader = stream.readable.pipeThrough(decoderStream).getReader();\n        const encoderStream = createPacketEncoderStream();\n        encoderStream.readable.pipeTo(stream.writable);\n        this.writer = encoderStream.writable.getWriter();\n        const read = () => {\n          reader.read().then(_ref => {\n            let {\n              done,\n              value\n            } = _ref;\n            if (done) {\n              return;\n            }\n            this.onPacket(value);\n            read();\n          }).catch(err => {});\n        };\n        read();\n        const packet = {\n          type: \"open\"\n        };\n        if (this.query.sid) {\n          packet.data = `{\"sid\":\"${this.query.sid}\"}`;\n        }\n        this.writer.write(packet).then(() => this.onOpen());\n      });\n    });\n  }\n  write(packets) {\n    this.writable = false;\n    for (let i = 0; i < packets.length; i++) {\n      const packet = packets[i];\n      const lastPacket = i === packets.length - 1;\n      this.writer.write(packet).then(() => {\n        if (lastPacket) {\n          nextTick(() => {\n            this.writable = true;\n            this.emitReserved(\"drain\");\n          }, this.setTimeoutFn);\n        }\n      });\n    }\n  }\n  doClose() {\n    var _a;\n    (_a = this.transport) === null || _a === void 0 ? void 0 : _a.close();\n  }\n}", "map": {"version": 3, "names": ["Transport", "nextTick", "createPacketDecoderStream", "createPacketEncoderStream", "WT", "name", "doOpen", "WebTransport", "transport", "createUri", "opts", "transportOptions", "closed", "then", "onClose", "catch", "err", "onError", "ready", "createBidirectionalStream", "stream", "decoderStream", "Number", "MAX_SAFE_INTEGER", "socket", "binaryType", "reader", "readable", "pipeThrough", "<PERSON><PERSON><PERSON><PERSON>", "encoderStream", "pipeTo", "writable", "writer", "getWriter", "read", "_ref", "done", "value", "onPacket", "packet", "type", "query", "sid", "data", "write", "onOpen", "packets", "i", "length", "lastPacket", "emit<PERSON><PERSON><PERSON><PERSON>", "setTimeoutFn", "doClose", "_a", "close"], "sources": ["D:/Projects/practice/bigcommerce-chatbot-app/client/node_modules/engine.io-client/build/esm/transports/webtransport.js"], "sourcesContent": ["import { Transport } from \"../transport.js\";\nimport { nextTick } from \"./websocket-constructor.js\";\nimport { createPacketDecoderStream, createPacketEncoderStream, } from \"engine.io-parser\";\nexport class WT extends Transport {\n    get name() {\n        return \"webtransport\";\n    }\n    doOpen() {\n        // @ts-ignore\n        if (typeof WebTransport !== \"function\") {\n            return;\n        }\n        // @ts-ignore\n        this.transport = new WebTransport(this.createUri(\"https\"), this.opts.transportOptions[this.name]);\n        this.transport.closed\n            .then(() => {\n            this.onClose();\n        })\n            .catch((err) => {\n            this.onError(\"webtransport error\", err);\n        });\n        // note: we could have used async/await, but that would require some additional polyfills\n        this.transport.ready.then(() => {\n            this.transport.createBidirectionalStream().then((stream) => {\n                const decoderStream = createPacketDecoderStream(Number.MAX_SAFE_INTEGER, this.socket.binaryType);\n                const reader = stream.readable.pipeThrough(decoderStream).getReader();\n                const encoderStream = createPacketEncoderStream();\n                encoderStream.readable.pipeTo(stream.writable);\n                this.writer = encoderStream.writable.getWriter();\n                const read = () => {\n                    reader\n                        .read()\n                        .then(({ done, value }) => {\n                        if (done) {\n                            return;\n                        }\n                        this.onPacket(value);\n                        read();\n                    })\n                        .catch((err) => {\n                    });\n                };\n                read();\n                const packet = { type: \"open\" };\n                if (this.query.sid) {\n                    packet.data = `{\"sid\":\"${this.query.sid}\"}`;\n                }\n                this.writer.write(packet).then(() => this.onOpen());\n            });\n        });\n    }\n    write(packets) {\n        this.writable = false;\n        for (let i = 0; i < packets.length; i++) {\n            const packet = packets[i];\n            const lastPacket = i === packets.length - 1;\n            this.writer.write(packet).then(() => {\n                if (lastPacket) {\n                    nextTick(() => {\n                        this.writable = true;\n                        this.emitReserved(\"drain\");\n                    }, this.setTimeoutFn);\n                }\n            });\n        }\n    }\n    doClose() {\n        var _a;\n        (_a = this.transport) === null || _a === void 0 ? void 0 : _a.close();\n    }\n}\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,iBAAiB;AAC3C,SAASC,QAAQ,QAAQ,4BAA4B;AACrD,SAASC,yBAAyB,EAAEC,yBAAyB,QAAS,kBAAkB;AACxF,OAAO,MAAMC,EAAE,SAASJ,SAAS,CAAC;EAC9B,IAAIK,IAAIA,CAAA,EAAG;IACP,OAAO,cAAc;EACzB;EACAC,MAAMA,CAAA,EAAG;IACL;IACA,IAAI,OAAOC,YAAY,KAAK,UAAU,EAAE;MACpC;IACJ;IACA;IACA,IAAI,CAACC,SAAS,GAAG,IAAID,YAAY,CAAC,IAAI,CAACE,SAAS,CAAC,OAAO,CAAC,EAAE,IAAI,CAACC,IAAI,CAACC,gBAAgB,CAAC,IAAI,CAACN,IAAI,CAAC,CAAC;IACjG,IAAI,CAACG,SAAS,CAACI,MAAM,CAChBC,IAAI,CAAC,MAAM;MACZ,IAAI,CAACC,OAAO,CAAC,CAAC;IAClB,CAAC,CAAC,CACGC,KAAK,CAAEC,GAAG,IAAK;MAChB,IAAI,CAACC,OAAO,CAAC,oBAAoB,EAAED,GAAG,CAAC;IAC3C,CAAC,CAAC;IACF;IACA,IAAI,CAACR,SAAS,CAACU,KAAK,CAACL,IAAI,CAAC,MAAM;MAC5B,IAAI,CAACL,SAAS,CAACW,yBAAyB,CAAC,CAAC,CAACN,IAAI,CAAEO,MAAM,IAAK;QACxD,MAAMC,aAAa,GAAGnB,yBAAyB,CAACoB,MAAM,CAACC,gBAAgB,EAAE,IAAI,CAACC,MAAM,CAACC,UAAU,CAAC;QAChG,MAAMC,MAAM,GAAGN,MAAM,CAACO,QAAQ,CAACC,WAAW,CAACP,aAAa,CAAC,CAACQ,SAAS,CAAC,CAAC;QACrE,MAAMC,aAAa,GAAG3B,yBAAyB,CAAC,CAAC;QACjD2B,aAAa,CAACH,QAAQ,CAACI,MAAM,CAACX,MAAM,CAACY,QAAQ,CAAC;QAC9C,IAAI,CAACC,MAAM,GAAGH,aAAa,CAACE,QAAQ,CAACE,SAAS,CAAC,CAAC;QAChD,MAAMC,IAAI,GAAGA,CAAA,KAAM;UACfT,MAAM,CACDS,IAAI,CAAC,CAAC,CACNtB,IAAI,CAACuB,IAAA,IAAqB;YAAA,IAApB;cAAEC,IAAI;cAAEC;YAAM,CAAC,GAAAF,IAAA;YACtB,IAAIC,IAAI,EAAE;cACN;YACJ;YACA,IAAI,CAACE,QAAQ,CAACD,KAAK,CAAC;YACpBH,IAAI,CAAC,CAAC;UACV,CAAC,CAAC,CACGpB,KAAK,CAAEC,GAAG,IAAK,CACpB,CAAC,CAAC;QACN,CAAC;QACDmB,IAAI,CAAC,CAAC;QACN,MAAMK,MAAM,GAAG;UAAEC,IAAI,EAAE;QAAO,CAAC;QAC/B,IAAI,IAAI,CAACC,KAAK,CAACC,GAAG,EAAE;UAChBH,MAAM,CAACI,IAAI,GAAG,WAAW,IAAI,CAACF,KAAK,CAACC,GAAG,IAAI;QAC/C;QACA,IAAI,CAACV,MAAM,CAACY,KAAK,CAACL,MAAM,CAAC,CAAC3B,IAAI,CAAC,MAAM,IAAI,CAACiC,MAAM,CAAC,CAAC,CAAC;MACvD,CAAC,CAAC;IACN,CAAC,CAAC;EACN;EACAD,KAAKA,CAACE,OAAO,EAAE;IACX,IAAI,CAACf,QAAQ,GAAG,KAAK;IACrB,KAAK,IAAIgB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,OAAO,CAACE,MAAM,EAAED,CAAC,EAAE,EAAE;MACrC,MAAMR,MAAM,GAAGO,OAAO,CAACC,CAAC,CAAC;MACzB,MAAME,UAAU,GAAGF,CAAC,KAAKD,OAAO,CAACE,MAAM,GAAG,CAAC;MAC3C,IAAI,CAAChB,MAAM,CAACY,KAAK,CAACL,MAAM,CAAC,CAAC3B,IAAI,CAAC,MAAM;QACjC,IAAIqC,UAAU,EAAE;UACZjD,QAAQ,CAAC,MAAM;YACX,IAAI,CAAC+B,QAAQ,GAAG,IAAI;YACpB,IAAI,CAACmB,YAAY,CAAC,OAAO,CAAC;UAC9B,CAAC,EAAE,IAAI,CAACC,YAAY,CAAC;QACzB;MACJ,CAAC,CAAC;IACN;EACJ;EACAC,OAAOA,CAAA,EAAG;IACN,IAAIC,EAAE;IACN,CAACA,EAAE,GAAG,IAAI,CAAC9C,SAAS,MAAM,IAAI,IAAI8C,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACC,KAAK,CAAC,CAAC;EACzE;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}