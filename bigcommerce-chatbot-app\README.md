# BigCommerce ChatBot App

A real-time chat application for BigCommerce stores with admin dashboard, customer chat widget, and notification system.

## Features

### 🤖 Customer Chat Widget
- Floating chat button on storefront
- Customer information collection
- Real-time messaging
- Typing indicators

### 👨‍💼 Admin Dashboard
- View all conversations
- Real-time notifications
- Agent assignment
- Message history
- Conversation management

### 🔔 Real-time Notifications
- New conversation alerts
- New message notifications
- Unread message counter
- Auto-refresh dashboard

### 📱 Responsive Design
- Mobile-friendly interface
- Adaptive layouts
- Touch-friendly controls

## Installation

### 1. Install Dependencies

```bash
# Install server dependencies
npm install

# Install client dependencies
cd client
npm install
cd ..
```

### 2. Environment Setup

Update `.env` file with your BigCommerce app credentials:

```env
BC_CLIENT_ID=your_bigcommerce_client_id
BC_CLIENT_SECRET=your_bigcommerce_client_secret
BC_CALLBACK_URL=https://your-ngrok-url.ngrok-free.app/auth
APP_URL=https://your-ngrok-url.ngrok-free.app
```

### 3. Build Client

```bash
cd client
npm run build
cd ..
```

### 4. Start Server

```bash
npm start
```

## Usage

### Admin Dashboard
- Access: `https://your-ngrok-url.ngrok-free.app/admin`
- View and manage all customer conversations
- Assign agents to conversations
- Receive real-time notifications

### Customer Chat Widget
- Access: `https://your-ngrok-url.ngrok-free.app/widget`
- Embed in your BigCommerce store
- Customers can start conversations
- Real-time messaging with support agents

### Integration with BigCommerce

#### Option 1: Script Manager
Add to **Storefront** → **Script Manager**:

```html
<div id="chat-widget"></div>
<script>
  const iframe = document.createElement('iframe');
  iframe.src = 'https://your-ngrok-url.ngrok-free.app/widget';
  iframe.style.position = 'fixed';
  iframe.style.bottom = '20px';
  iframe.style.right = '20px';
  iframe.style.width = '400px';
  iframe.style.height = '600px';
  iframe.style.border = 'none';
  iframe.style.zIndex = '9999';
  document.body.appendChild(iframe);
</script>
```

#### Option 2: Theme Integration
- Download your theme
- Edit template files
- Add the chat widget iframe

## API Endpoints

### Conversations
- `GET /api/conversations` - Get all conversations
- `POST /api/conversations` - Create new conversation
- `GET /api/conversations/:id` - Get specific conversation
- `PUT /api/conversations/:id/assign` - Assign agent

### Messages
- `POST /api/conversations/:id/messages` - Send message

### Notifications
- `GET /api/notifications` - Get notifications
- `PUT /api/notifications/:id/read` - Mark as read

## Socket.IO Events

### Client to Server
- `join-admin` - Join admin room
- `join-conversation` - Join conversation room
- `typing` - Send typing indicator
- `stop-typing` - Stop typing indicator

### Server to Client
- `new-conversation` - New conversation created
- `new-message` - New message received
- `new-notification` - New notification
- `conversation-updated` - Conversation updated
- `user-typing` - User typing indicator
- `user-stop-typing` - User stopped typing

## Development

### Project Structure
```
bigcommerce-chatbot-app/
├── server.js              # Express server with Socket.IO
├── client/                 # React frontend
│   ├── src/
│   │   ├── components/     # React components
│   │   ├── App.jsx         # Main app component
│   │   └── App.css         # Styles
│   └── public/
├── package.json            # Server dependencies
└── .env                    # Environment variables
```

### Key Components
- **AdminDashboard** - Main admin interface
- **ConversationList** - List of conversations
- **ChatWindow** - Chat interface
- **NotificationPanel** - Notification display
- **ChatWidget** - Customer chat widget

## Technologies Used

- **Backend**: Node.js, Express, Socket.IO
- **Frontend**: React, Socket.IO Client
- **Styling**: CSS3, Flexbox, Grid
- **Real-time**: WebSocket connections
- **Storage**: In-memory (can be extended to database)

## Future Enhancements

- Database integration (MongoDB/PostgreSQL)
- File upload support
- Automated responses/chatbots
- Analytics and reporting
- Multi-language support
- Agent status management
- Conversation routing
- Customer satisfaction surveys

## License

MIT License
