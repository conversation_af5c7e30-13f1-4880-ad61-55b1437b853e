import { useState, useEffect, useRef } from 'react'
import axios from 'axios'

// List of popular cities for suggestions
const POPULAR_CITIES = [
  'London', 'New York', 'Tokyo', 'Paris', 'Sydney', 
  'Berlin', 'Rome', 'Madrid', 'Moscow', 'Beijing',
  'Toronto', 'Dubai', 'Singapore', 'Mumbai', 'Cairo',
  'Los Angeles', 'Chicago', 'San Francisco', 'Seattle', 'Boston',
  'Miami', 'Las Vegas', 'Denver', 'Austin', 'Atlanta',
  'Delhi', 'Bangkok', 'Hong Kong', 'Seoul', 'Istanbul'
];

const CitySearch = ({ onSearch, loading }) => {
  const [city, setCity] = useState('')
  const [suggestions, setSuggestions] = useState([])
  const [showSuggestions, setShowSuggestions] = useState(false)
  const suggestionsRef = useRef(null)

  // Filter suggestions based on input
  const getSuggestions = (input) => {
    if (!input.trim()) return []
    
    const inputLower = input.toLowerCase()
    return POPULAR_CITIES.filter(city => 
      city.toLowerCase().includes(inputLower)
    ).slice(0, 5) // Limit to 5 suggestions
  }

  // Handle input change
  const handleInputChange = (e) => {
    const value = e.target.value
    setCity(value)
    
    // Update suggestions
    const newSuggestions = getSuggestions(value)
    setSuggestions(newSuggestions)
    setShowSuggestions(newSuggestions.length > 0)
  }

  // Handle suggestion click
  const handleSuggestionClick = (suggestion) => {
    setCity(suggestion)
    setSuggestions([])
    setShowSuggestions(false)
    onSearch(suggestion)
  }

  // Handle form submission
  const handleSubmit = (e) => {
    e.preventDefault()
    if (city.trim()) {
      onSearch(city)
      setShowSuggestions(false)
    }
  }

  // Close suggestions when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (suggestionsRef.current && !suggestionsRef.current.contains(event.target)) {
        setShowSuggestions(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [])

  return (
    <div className="search-container" ref={suggestionsRef}>
      <form onSubmit={handleSubmit}>
        <input
          type="text"
          className="search-input"
          placeholder="Enter city name (e.g., London, New York, Tokyo)"
          value={city}
          onChange={handleInputChange}
          onFocus={() => setShowSuggestions(suggestions.length > 0)}
          disabled={loading}
        />
        
        {showSuggestions && (
          <ul className="suggestions-list">
            {suggestions.map((suggestion, index) => (
              <li 
                key={index} 
                onClick={() => handleSuggestionClick(suggestion)}
                className="suggestion-item"
              >
                {suggestion}
              </li>
            ))}
          </ul>
        )}
        
        <button 
          type="submit" 
          className="search-btn" 
          disabled={loading || !city.trim()}
        >
          {loading ? 'Loading...' : 'Get Weather'}
        </button>
      </form>
    </div>
  )
}

export default CitySearch
