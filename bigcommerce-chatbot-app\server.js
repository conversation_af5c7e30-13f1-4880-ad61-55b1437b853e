const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const cors = require('cors');
const path = require('path');
const { v4: uuidv4 } = require('uuid');
require('dotenv').config();

const app = express();
const server = http.createServer(app);
const io = socketIo(server, {
  cors: {
    origin: "*",
    methods: ["GET", "POST"]
  }
});

const PORT = process.env.PORT || 3001;

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Serve static files from React build
app.use(express.static(path.join(__dirname, 'client/build')));

// In-memory storage (in production, use a database)
let conversations = [];
let agents = [];
let notifications = [];

// BigCommerce OAuth routes
app.get('/auth', (req, res) => {
  const { code, scope, context } = req.query;
  
  if (!code) {
    return res.status(400).send('Authorization code is required');
  }

  // Exchange code for access token
  const tokenData = {
    client_id: process.env.BC_CLIENT_ID,
    client_secret: process.env.BC_CLIENT_SECRET,
    redirect_uri: process.env.BC_CALLBACK_URL,
    grant_type: 'authorization_code',
    code: code,
    scope: scope,
    context: context
  };

  // For demo purposes, redirect to admin dashboard
  res.redirect(`/admin?context=${context}`);
});

// Admin dashboard endpoint
app.get('/admin', (req, res) => {
  res.sendFile(path.join(__dirname, 'client/build', 'index.html'));
});

// Customer chat widget endpoint
app.get('/widget', (req, res) => {
  res.sendFile(path.join(__dirname, 'client/build', 'widget.html'));
});

// API Routes

// Get all conversations for admin
app.get('/api/conversations', (req, res) => {
  res.json(conversations);
});

// Get specific conversation
app.get('/api/conversations/:id', (req, res) => {
  const conversation = conversations.find(c => c.id === req.params.id);
  if (!conversation) {
    return res.status(404).json({ error: 'Conversation not found' });
  }
  res.json(conversation);
});

// Create new conversation
app.post('/api/conversations', (req, res) => {
  const { customerName, customerEmail, storeContext } = req.body;
  
  const conversation = {
    id: uuidv4(),
    customerName: customerName || 'Anonymous',
    customerEmail: customerEmail || '',
    storeContext: storeContext || '',
    status: 'active',
    assignedAgent: null,
    messages: [],
    createdAt: new Date(),
    lastActivity: new Date()
  };
  
  conversations.push(conversation);
  
  // Notify all connected admin clients
  io.to('admin-room').emit('new-conversation', conversation);
  
  res.json(conversation);
});

// Send message
app.post('/api/conversations/:id/messages', (req, res) => {
  const { message, sender, senderType } = req.body;
  const conversationId = req.params.id;
  
  const conversation = conversations.find(c => c.id === conversationId);
  if (!conversation) {
    return res.status(404).json({ error: 'Conversation not found' });
  }
  
  const newMessage = {
    id: uuidv4(),
    message,
    sender,
    senderType, // 'customer' or 'agent'
    timestamp: new Date()
  };
  
  conversation.messages.push(newMessage);
  conversation.lastActivity = new Date();
  
  // Emit to all clients in this conversation
  io.to(`conversation-${conversationId}`).emit('new-message', {
    conversationId,
    message: newMessage
  });
  
  // If customer message, notify admins
  if (senderType === 'customer') {
    const notification = {
      id: uuidv4(),
      type: 'new-message',
      conversationId,
      customerName: conversation.customerName,
      message: message.substring(0, 50) + (message.length > 50 ? '...' : ''),
      timestamp: new Date(),
      read: false
    };
    
    notifications.push(notification);
    io.to('admin-room').emit('new-notification', notification);
  }
  
  res.json(newMessage);
});

// Get notifications
app.get('/api/notifications', (req, res) => {
  res.json(notifications.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp)));
});

// Mark notification as read
app.put('/api/notifications/:id/read', (req, res) => {
  const notification = notifications.find(n => n.id === req.params.id);
  if (notification) {
    notification.read = true;
  }
  res.json({ success: true });
});

// Assign agent to conversation
app.put('/api/conversations/:id/assign', (req, res) => {
  const { agentName } = req.body;
  const conversation = conversations.find(c => c.id === req.params.id);
  
  if (conversation) {
    conversation.assignedAgent = agentName;
    io.to('admin-room').emit('conversation-updated', conversation);
  }
  
  res.json(conversation);
});

// Socket.IO connection handling
io.on('connection', (socket) => {
  console.log('User connected:', socket.id);
  
  // Join admin room
  socket.on('join-admin', () => {
    socket.join('admin-room');
    console.log('Admin joined:', socket.id);
  });
  
  // Join conversation room
  socket.on('join-conversation', (conversationId) => {
    socket.join(`conversation-${conversationId}`);
    console.log(`User ${socket.id} joined conversation ${conversationId}`);
  });
  
  // Handle typing indicators
  socket.on('typing', (data) => {
    socket.to(`conversation-${data.conversationId}`).emit('user-typing', data);
  });
  
  socket.on('stop-typing', (data) => {
    socket.to(`conversation-${data.conversationId}`).emit('user-stop-typing', data);
  });
  
  socket.on('disconnect', () => {
    console.log('User disconnected:', socket.id);
  });
});

// Health check
app.get('/health', (req, res) => {
  res.json({ 
    status: 'OK', 
    version: 'v1.0',
    conversations: conversations.length,
    notifications: notifications.length,
    timestamp: new Date().toISOString() 
  });
});

// Catch all handler for React app
app.get('*', (req, res) => {
  res.sendFile(path.join(__dirname, 'client/build', 'index.html'));
});

server.listen(PORT, () => {
  console.log(`BigCommerce ChatBot App running on port ${PORT}`);
  console.log(`Admin URL: ${process.env.APP_URL || `http://localhost:${PORT}`}/admin`);
  console.log(`Widget URL: ${process.env.APP_URL || `http://localhost:${PORT}`}/widget`);
});
