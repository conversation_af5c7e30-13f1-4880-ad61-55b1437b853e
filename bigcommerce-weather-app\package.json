{"name": "bigcommerce-weather-app", "version": "1.0.0", "description": "Weather app for BigCommerce admin panel", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "build": "npm run build:client", "build:client": "cd client && npm install && npm run build", "heroku-postbuild": "npm run build:client"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "dotenv": "^16.3.1", "axios": "^1.6.0", "jsonwebtoken": "^9.0.2", "crypto": "^1.0.1", "path": "^0.12.7"}, "devDependencies": {"nodemon": "^3.0.1"}, "keywords": ["bigcommerce", "weather", "app"], "author": "Your Name", "license": "MIT"}