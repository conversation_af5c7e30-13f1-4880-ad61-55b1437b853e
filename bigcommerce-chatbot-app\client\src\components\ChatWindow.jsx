import React, { useState, useEffect, useRef } from 'react';
import axios from 'axios';

const ChatWindow = ({ conversation, socket, agentName, isAdmin = false }) => {
  const [messages, setMessages] = useState([]);
  const [newMessage, setNewMessage] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const [typingUser, setTypingUser] = useState('');
  const messagesEndRef = useRef(null);
  const typingTimeoutRef = useRef(null);

  useEffect(() => {
    if (conversation) {
      setMessages(conversation.messages || []);
      
      // Join conversation room
      if (socket) {
        socket.emit('join-conversation', conversation.id);
        
        // Listen for new messages
        socket.on('new-message', (data) => {
          if (data.conversationId === conversation.id) {
            setMessages(prev => [...prev, data.message]);
          }
        });
        
        // Listen for typing indicators
        socket.on('user-typing', (data) => {
          if (data.conversationId === conversation.id) {
            setIsTyping(true);
            setTypingUser(data.userName);
          }
        });
        
        socket.on('user-stop-typing', (data) => {
          if (data.conversationId === conversation.id) {
            setIsTyping(false);
            setTypingUser('');
          }
        });
      }
    }

    return () => {
      if (socket) {
        socket.off('new-message');
        socket.off('user-typing');
        socket.off('user-stop-typing');
      }
    };
  }, [conversation, socket]);

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const handleSendMessage = async (e) => {
    e.preventDefault();
    
    if (!newMessage.trim() || !conversation) return;

    try {
      await axios.post(`/api/conversations/${conversation.id}/messages`, {
        message: newMessage,
        sender: isAdmin ? agentName : conversation.customerName,
        senderType: isAdmin ? 'agent' : 'customer'
      });
      
      setNewMessage('');
      
      // Stop typing indicator
      if (socket) {
        socket.emit('stop-typing', {
          conversationId: conversation.id,
          userName: isAdmin ? agentName : conversation.customerName
        });
      }
    } catch (error) {
      console.error('Failed to send message:', error);
    }
  };

  const handleTyping = () => {
    if (socket && conversation) {
      socket.emit('typing', {
        conversationId: conversation.id,
        userName: isAdmin ? agentName : conversation.customerName
      });
      
      // Clear existing timeout
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current);
      }
      
      // Set new timeout to stop typing
      typingTimeoutRef.current = setTimeout(() => {
        socket.emit('stop-typing', {
          conversationId: conversation.id,
          userName: isAdmin ? agentName : conversation.customerName
        });
      }, 1000);
    }
  };

  const formatTime = (timestamp) => {
    return new Date(timestamp).toLocaleTimeString([], { 
      hour: '2-digit', 
      minute: '2-digit' 
    });
  };

  if (!conversation) {
    return <div className="no-conversation">Select a conversation to start chatting</div>;
  }

  return (
    <div className="chat-window">
      <div className="chat-header">
        <div className="conversation-info">
          <h3>{conversation.customerName}</h3>
          <p>{conversation.customerEmail}</p>
        </div>
        <div className="conversation-status">
          <span className={`status ${conversation.status}`}>
            {conversation.status}
          </span>
          {conversation.assignedAgent && (
            <span className="assigned-to">
              Assigned to: {conversation.assignedAgent}
            </span>
          )}
        </div>
      </div>

      <div className="messages-container">
        {messages.map(message => (
          <div
            key={message.id}
            className={`message ${message.senderType === 'agent' ? 'agent' : 'customer'}`}
          >
            <div className="message-content">
              <div className="message-text">{message.message}</div>
              <div className="message-meta">
                <span className="sender">{message.sender}</span>
                <span className="timestamp">{formatTime(message.timestamp)}</span>
              </div>
            </div>
          </div>
        ))}
        
        {isTyping && (
          <div className="typing-indicator">
            <div className="typing-dots">
              <span></span>
              <span></span>
              <span></span>
            </div>
            <span className="typing-text">{typingUser} is typing...</span>
          </div>
        )}
        
        <div ref={messagesEndRef} />
      </div>

      <form className="message-form" onSubmit={handleSendMessage}>
        <input
          type="text"
          value={newMessage}
          onChange={(e) => {
            setNewMessage(e.target.value);
            handleTyping();
          }}
          placeholder="Type your message..."
          className="message-input"
        />
        <button type="submit" className="send-button">
          Send
        </button>
      </form>
    </div>
  );
};

export default ChatWindow;
