import React, { useState, useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import AdminDashboard from './components/AdminDashboard';
import ChatWidget from './components/ChatWidget';
import io from 'socket.io-client';
import './App.css';

function App() {
  const [socket, setSocket] = useState(null);

  useEffect(() => {
    // Initialize socket connection
    const newSocket = io(window.location.origin);
    setSocket(newSocket);

    return () => {
      if (newSocket) {
        newSocket.disconnect();
      }
    };
  }, []);

  return (
    <div className="App">
      <Router>
        <Routes>
          <Route 
            path="/admin" 
            element={<AdminDashboard socket={socket} />} 
          />
          <Route 
            path="/widget" 
            element={<ChatWidget socket={socket} />} 
          />
          <Route 
            path="/" 
            element={<AdminDashboard socket={socket} />} 
          />
        </Routes>
      </Router>
    </div>
  );
}

export default App;
