import React from 'react';

const NotificationPanel = ({ notifications, onMarkRead }) => {
  const formatTime = (timestamp) => {
    const now = new Date();
    const notificationTime = new Date(timestamp);
    const diffInMinutes = Math.floor((now - notificationTime) / (1000 * 60));
    
    if (diffInMinutes < 1) return 'Just now';
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h ago`;
    return notificationTime.toLocaleDateString();
  };

  const getNotificationIcon = (type) => {
    switch (type) {
      case 'new-conversation': return '💬';
      case 'new-message': return '📩';
      default: return '🔔';
    }
  };

  return (
    <div className="notification-panel">
      {notifications.length === 0 ? (
        <div className="empty-notifications">
          <p>No notifications</p>
        </div>
      ) : (
        notifications.map(notification => (
          <div
            key={notification.id}
            className={`notification-item ${notification.read ? 'read' : 'unread'}`}
            onClick={() => !notification.read && onMarkRead(notification.id)}
          >
            <div className="notification-icon">
              {getNotificationIcon(notification.type)}
            </div>
            <div className="notification-content">
              <div className="notification-message">
                {notification.message || `New message from ${notification.customerName}`}
              </div>
              <div className="notification-time">
                {formatTime(notification.timestamp)}
              </div>
            </div>
            {!notification.read && <div className="unread-dot" />}
          </div>
        ))
      )}
    </div>
  );
};

export default NotificationPanel;
