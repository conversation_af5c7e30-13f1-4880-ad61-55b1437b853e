const express = require('express');
const cors = require('cors');
const path = require('path');
const axios = require('axios');
require('dotenv').config();

const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Serve static files from React build
app.use(express.static(path.join(__dirname, 'client/build')));

// BigCommerce OAuth routes
app.get('/auth', (req, res) => {
  const { code, scope, context } = req.query;
  
  if (!code) {
    return res.status(400).send('Authorization code is required');
  }

  // Exchange code for access token
  const tokenData = {
    client_id: process.env.BC_CLIENT_ID,
    client_secret: process.env.BC_CLIENT_SECRET,
    redirect_uri: process.env.BC_CALLBACK_URL,
    grant_type: 'authorization_code',
    code: code,
    scope: scope,
    context: context
  };

  axios.post(process.env.BC_TOKEN_URL, tokenData)
    .then(response => {
      const { access_token, context } = response.data;
      
      // Store token securely (in production, use a database)
      // For demo purposes, we'll redirect to the app
      res.redirect(`/app?context=${context}&token=${access_token}`);
    })
    .catch(error => {
      console.error('OAuth error:', error.response?.data || error.message);
      res.status(500).send('Authentication failed');
    });
});

// App load endpoint
app.get('/app', (req, res) => {
  res.sendFile(path.join(__dirname, 'client/build', 'index.html'));
});

// IP-based location endpoint for VPN users
app.get('/api/weather/ip-location', async (req, res) => {
  try {
    // Get user's IP-based location using a free service
    const ipResponse = await axios.get('http://ip-api.com/json/');
    const { lat, lon, city, country } = ipResponse.data;

    if (!lat || !lon) {
      return res.status(400).json({ error: 'Could not determine location from IP' });
    }

    // Fetch weather for IP-based location
    const weatherResponse = await axios.get(
      `https://api.openweathermap.org/data/2.5/weather?lat=${lat}&lon=${lon}&appid=${process.env.WEATHER_API_KEY}&units=metric`
    );

    // Add location info to response
    const weatherData = {
      ...weatherResponse.data,
      locationSource: 'ip',
      detectedCity: city,
      detectedCountry: country
    };

    res.json(weatherData);
  } catch (error) {
    console.error('IP location error:', error.response?.data || error.message);
    res.status(500).json({ error: 'Failed to fetch weather data using IP location' });
  }
});

// Weather API proxy endpoint for coordinates (must come BEFORE the :city route)
app.get('/api/weather/coordinates', async (req, res) => {
  try {
    const { lat, lon } = req.query;

    if (!lat || !lon) {
      return res.status(400).json({ error: 'Latitude and longitude are required' });
    }

    const response = await axios.get(
      `https://api.openweathermap.org/data/2.5/weather?lat=${lat}&lon=${lon}&appid=${process.env.WEATHER_API_KEY}&units=metric`
    );
    res.json(response.data);
  } catch (error) {
    console.error('Weather API error:', error.response?.data || error.message);
    res.status(500).json({ error: 'Failed to fetch weather data for coordinates' });
  }
});

// Weather API proxy endpoint for city search
app.get('/api/weather/:city', async (req, res) => {
  try {
    const { city } = req.params;
    const response = await axios.get(
      `https://api.openweathermap.org/data/2.5/weather?q=${city}&appid=${process.env.WEATHER_API_KEY}&units=metric`
    );
    res.json(response.data);
  } catch (error) {
    res.status(500).json({ error: 'Failed to fetch weather data' });
  }
});

// Health check
app.get('/health', (req, res) => {
  res.json({ status: 'OK', timestamp: new Date().toISOString() });
});

// Catch all handler for React app
app.get('*', (req, res) => {
  res.sendFile(path.join(__dirname, 'client/build', 'index.html'));
});

app.listen(PORT, () => {
  console.log(`BigCommerce Weather App running on port ${PORT}`);
  console.log(`App URL: ${process.env.APP_URL || `http://localhost:${PORT}`}`);
});
