{"name": "bigcommerce-chatbot-app", "version": "1.0.0", "description": "BigCommerce Chat Bot App with Admin Dashboard", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "build": "cd client && npm run build", "install-client": "cd client && npm install"}, "keywords": ["bigcommerce", "chatbot", "customer-support", "admin-dashboard"], "author": "Your Name", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "dotenv": "^16.3.1", "axios": "^1.5.0", "socket.io": "^4.7.2", "jsonwebtoken": "^9.0.2", "bcryptjs": "^2.4.3", "mongoose": "^7.5.0", "multer": "^1.4.5", "uuid": "^9.0.0"}, "devDependencies": {"nodemon": "^3.0.1"}}