* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  background: #f5f5f5;
  color: #333;
  line-height: 1.6;
}

.App {
  min-height: 100vh;
}

/* Admin Dashboard */
.admin-dashboard {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: white;
}

.dashboard-header {
  background: #2c3e50;
  color: white;
  padding: 1rem 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.dashboard-header h1 {
  font-size: 1.5rem;
  font-weight: 600;
}

.header-controls {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.agent-input {
  padding: 0.5rem;
  border: 1px solid #34495e;
  border-radius: 4px;
  background: white;
  color: #333;
}

.notification-badge {
  position: relative;
  font-size: 1.2rem;
  cursor: pointer;
}

.badge {
  position: absolute;
  top: -8px;
  right: -8px;
  background: #e74c3c;
  color: white;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  font-size: 0.7rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.dashboard-content {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.sidebar {
  width: 350px;
  background: #ecf0f1;
  border-right: 1px solid #bdc3c7;
  display: flex;
  flex-direction: column;
}

.sidebar-section {
  flex: 1;
  padding: 1rem;
  border-bottom: 1px solid #bdc3c7;
}

.sidebar-section h3 {
  margin-bottom: 1rem;
  color: #2c3e50;
  font-size: 1rem;
}

.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.no-conversation {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #7f8c8d;
  text-align: center;
}

/* Conversation List */
.conversation-list {
  max-height: 400px;
  overflow-y: auto;
}

.conversation-item {
  padding: 1rem;
  border-bottom: 1px solid #d5dbdb;
  cursor: pointer;
  transition: background 0.2s ease;
}

.conversation-item:hover {
  background: #d5dbdb;
}

.conversation-item.selected {
  background: #3498db;
  color: white;
}

.conversation-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.customer-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.customer-name {
  font-weight: 600;
}

.status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.timestamp {
  font-size: 0.8rem;
  opacity: 0.7;
}

.conversation-preview {
  margin-bottom: 0.5rem;
}

.last-message {
  font-size: 0.9rem;
  opacity: 0.8;
  margin-bottom: 0.25rem;
}

.message-count {
  font-size: 0.8rem;
  opacity: 0.6;
}

.conversation-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.assigned-agent {
  font-size: 0.8rem;
  background: rgba(255,255,255,0.2);
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
}

.assign-btn {
  background: #27ae60;
  color: white;
  border: none;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.8rem;
  cursor: pointer;
}

.assign-btn:hover {
  background: #229954;
}

/* Chat Window */
.chat-window {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.chat-header {
  background: #34495e;
  color: white;
  padding: 1rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.conversation-info h3 {
  margin-bottom: 0.25rem;
}

.conversation-info p {
  font-size: 0.9rem;
  opacity: 0.8;
}

.conversation-status {
  text-align: right;
}

.status {
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.8rem;
  text-transform: uppercase;
}

.status.active {
  background: #27ae60;
}

.status.waiting {
  background: #f39c12;
}

.status.closed {
  background: #95a5a6;
}

.assigned-to {
  display: block;
  font-size: 0.8rem;
  margin-top: 0.25rem;
  opacity: 0.8;
}

.messages-container {
  flex: 1;
  padding: 1rem;
  overflow-y: auto;
  background: #f8f9fa;
}

.message {
  margin-bottom: 1rem;
  display: flex;
}

.message.agent {
  justify-content: flex-end;
}

.message.customer {
  justify-content: flex-start;
}

.message-content {
  max-width: 70%;
  background: white;
  padding: 0.75rem;
  border-radius: 8px;
  box-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

.message.agent .message-content {
  background: #3498db;
  color: white;
}

.message-text {
  margin-bottom: 0.5rem;
}

.message-meta {
  display: flex;
  justify-content: space-between;
  font-size: 0.8rem;
  opacity: 0.7;
}

.message-form {
  padding: 1rem;
  background: white;
  border-top: 1px solid #e9ecef;
  display: flex;
  gap: 0.5rem;
}

.message-input {
  flex: 1;
  padding: 0.75rem;
  border: 1px solid #ced4da;
  border-radius: 4px;
  outline: none;
}

.message-input:focus {
  border-color: #3498db;
}

.send-button {
  background: #3498db;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
}

.send-button:hover {
  background: #2980b9;
}

/* Typing Indicator */
.typing-indicator {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
  opacity: 0.7;
}

.typing-dots {
  display: flex;
  gap: 2px;
}

.typing-dots span {
  width: 6px;
  height: 6px;
  background: #95a5a6;
  border-radius: 50%;
  animation: typing 1.4s infinite ease-in-out;
}

.typing-dots span:nth-child(1) { animation-delay: -0.32s; }
.typing-dots span:nth-child(2) { animation-delay: -0.16s; }

@keyframes typing {
  0%, 80%, 100% { transform: scale(0); }
  40% { transform: scale(1); }
}

.typing-text {
  font-size: 0.9rem;
  font-style: italic;
}

/* Notification Panel */
.notification-panel {
  max-height: 300px;
  overflow-y: auto;
}

.empty-notifications {
  text-align: center;
  padding: 2rem;
  color: #7f8c8d;
}

.notification-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  border-bottom: 1px solid #d5dbdb;
  cursor: pointer;
  transition: background 0.2s ease;
  position: relative;
}

.notification-item:hover {
  background: #d5dbdb;
}

.notification-item.unread {
  background: #ebf3fd;
  font-weight: 500;
}

.notification-icon {
  font-size: 1.2rem;
}

.notification-content {
  flex: 1;
}

.notification-message {
  font-size: 0.9rem;
  margin-bottom: 0.25rem;
}

.notification-time {
  font-size: 0.8rem;
  opacity: 0.6;
}

.unread-dot {
  width: 8px;
  height: 8px;
  background: #3498db;
  border-radius: 50%;
}

/* Chat Widget */
.chat-widget {
  position: fixed;
  bottom: 20px;
  right: 20px;
  z-index: 1000;
}

.chat-toggle {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: #3498db;
  color: white;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
  transition: all 0.3s ease;
}

.chat-toggle:hover {
  background: #2980b9;
  transform: scale(1.05);
}

.chat-toggle.open {
  background: #e74c3c;
}

.chat-widget-window {
  position: absolute;
  bottom: 80px;
  right: 0;
  width: 350px;
  height: 500px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 8px 24px rgba(0,0,0,0.15);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.chat-start-form {
  padding: 1.5rem;
}

.chat-start-form .chat-header {
  margin-bottom: 1.5rem;
  text-align: center;
}

.chat-start-form h3 {
  color: #2c3e50;
  margin-bottom: 0.5rem;
}

.chat-start-form p {
  color: #7f8c8d;
  font-size: 0.9rem;
}

.form-group {
  margin-bottom: 1rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: #2c3e50;
}

.form-group input {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #ced4da;
  border-radius: 4px;
  outline: none;
}

.form-group input:focus {
  border-color: #3498db;
}

.start-chat-btn {
  width: 100%;
  background: #3498db;
  color: white;
  border: none;
  padding: 0.75rem;
  border-radius: 4px;
  font-weight: 500;
  cursor: pointer;
  transition: background 0.2s ease;
}

.start-chat-btn:hover {
  background: #2980b9;
}

/* Responsive */
@media (max-width: 768px) {
  .dashboard-content {
    flex-direction: column;
  }
  
  .sidebar {
    width: 100%;
    height: 300px;
  }
  
  .chat-widget-window {
    width: 300px;
    height: 400px;
  }
}
