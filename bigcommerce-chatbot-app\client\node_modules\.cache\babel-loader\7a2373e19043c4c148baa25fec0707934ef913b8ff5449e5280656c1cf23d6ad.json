{"ast": null, "code": "const PACKET_TYPES = Object.create(null); // no Map = no polyfill\nPACKET_TYPES[\"open\"] = \"0\";\nPACKET_TYPES[\"close\"] = \"1\";\nPACKET_TYPES[\"ping\"] = \"2\";\nPACKET_TYPES[\"pong\"] = \"3\";\nPACKET_TYPES[\"message\"] = \"4\";\nPACKET_TYPES[\"upgrade\"] = \"5\";\nPACKET_TYPES[\"noop\"] = \"6\";\nconst PACKET_TYPES_REVERSE = Object.create(null);\nObject.keys(PACKET_TYPES).forEach(key => {\n  PACKET_TYPES_REVERSE[PACKET_TYPES[key]] = key;\n});\nconst ERROR_PACKET = {\n  type: \"error\",\n  data: \"parser error\"\n};\nexport { PACKET_TYPES, PACKET_TYPES_REVERSE, ERROR_PACKET };", "map": {"version": 3, "names": ["PACKET_TYPES", "Object", "create", "PACKET_TYPES_REVERSE", "keys", "for<PERSON>ach", "key", "ERROR_PACKET", "type", "data"], "sources": ["D:/Projects/practice/bigcommerce-chatbot-app/client/node_modules/engine.io-parser/build/esm/commons.js"], "sourcesContent": ["const PACKET_TYPES = Object.create(null); // no Map = no polyfill\nPACKET_TYPES[\"open\"] = \"0\";\nPACKET_TYPES[\"close\"] = \"1\";\nPACKET_TYPES[\"ping\"] = \"2\";\nPACKET_TYPES[\"pong\"] = \"3\";\nPACKET_TYPES[\"message\"] = \"4\";\nPACKET_TYPES[\"upgrade\"] = \"5\";\nPACKET_TYPES[\"noop\"] = \"6\";\nconst PACKET_TYPES_REVERSE = Object.create(null);\nObject.keys(PACKET_TYPES).forEach((key) => {\n    PACKET_TYPES_REVERSE[PACKET_TYPES[key]] = key;\n});\nconst ERROR_PACKET = { type: \"error\", data: \"parser error\" };\nexport { PACKET_TYPES, PACKET_TYPES_REVERSE, ERROR_PACKET };\n"], "mappings": "AAAA,MAAMA,YAAY,GAAGC,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;AAC1CF,YAAY,CAAC,MAAM,CAAC,GAAG,GAAG;AAC1BA,YAAY,CAAC,OAAO,CAAC,GAAG,GAAG;AAC3BA,YAAY,CAAC,MAAM,CAAC,GAAG,GAAG;AAC1BA,YAAY,CAAC,MAAM,CAAC,GAAG,GAAG;AAC1BA,YAAY,CAAC,SAAS,CAAC,GAAG,GAAG;AAC7BA,YAAY,CAAC,SAAS,CAAC,GAAG,GAAG;AAC7BA,YAAY,CAAC,MAAM,CAAC,GAAG,GAAG;AAC1B,MAAMG,oBAAoB,GAAGF,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;AAChDD,MAAM,CAACG,IAAI,CAACJ,YAAY,CAAC,CAACK,OAAO,CAAEC,GAAG,IAAK;EACvCH,oBAAoB,CAACH,YAAY,CAACM,GAAG,CAAC,CAAC,GAAGA,GAAG;AACjD,CAAC,CAAC;AACF,MAAMC,YAAY,GAAG;EAAEC,IAAI,EAAE,OAAO;EAAEC,IAAI,EAAE;AAAe,CAAC;AAC5D,SAAST,YAAY,EAAEG,oBAAoB,EAAEI,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}