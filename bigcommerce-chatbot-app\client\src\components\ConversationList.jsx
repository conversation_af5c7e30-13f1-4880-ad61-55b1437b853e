import React from 'react';

const ConversationList = ({ 
  conversations, 
  selectedConversation, 
  onConversationSelect, 
  onAssignAgent,
  agentName 
}) => {
  const formatTime = (timestamp) => {
    return new Date(timestamp).toLocaleTimeString([], { 
      hour: '2-digit', 
      minute: '2-digit' 
    });
  };

  const getLastMessage = (conversation) => {
    if (conversation.messages.length === 0) {
      return 'No messages yet';
    }
    const lastMessage = conversation.messages[conversation.messages.length - 1];
    return lastMessage.message.length > 50 
      ? lastMessage.message.substring(0, 50) + '...'
      : lastMessage.message;
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'active': return '#28a745';
      case 'waiting': return '#ffc107';
      case 'closed': return '#6c757d';
      default: return '#17a2b8';
    }
  };

  return (
    <div className="conversation-list">
      {conversations.length === 0 ? (
        <div className="empty-state">
          <p>No conversations yet</p>
          <small>Conversations will appear here when customers start chatting</small>
        </div>
      ) : (
        conversations.map(conversation => (
          <div
            key={conversation.id}
            className={`conversation-item ${
              selectedConversation?.id === conversation.id ? 'selected' : ''
            }`}
            onClick={() => onConversationSelect(conversation)}
          >
            <div className="conversation-header">
              <div className="customer-info">
                <span className="customer-name">
                  {conversation.customerName}
                </span>
                <div 
                  className="status-indicator"
                  style={{ backgroundColor: getStatusColor(conversation.status) }}
                />
              </div>
              <span className="timestamp">
                {formatTime(conversation.lastActivity)}
              </span>
            </div>
            
            <div className="conversation-preview">
              <p className="last-message">{getLastMessage(conversation)}</p>
              <div className="message-count">
                {conversation.messages.length} messages
              </div>
            </div>
            
            <div className="conversation-meta">
              {conversation.assignedAgent ? (
                <span className="assigned-agent">
                  👤 {conversation.assignedAgent}
                </span>
              ) : (
                <button
                  className="assign-btn"
                  onClick={(e) => {
                    e.stopPropagation();
                    onAssignAgent(conversation.id);
                  }}
                >
                  Assign to {agentName}
                </button>
              )}
            </div>
          </div>
        ))
      )}
    </div>
  );
};

export default ConversationList;
