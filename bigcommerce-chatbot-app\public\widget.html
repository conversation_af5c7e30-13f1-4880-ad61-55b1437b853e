<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Customer Chat Widget</title>
    <script src="/socket.io/socket.io.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            background: #f5f5f5;
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .chat-widget {
            width: 400px;
            height: 600px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 8px 24px rgba(0,0,0,0.15);
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }
        
        .chat-header {
            background: #3498db;
            color: white;
            padding: 1rem;
            text-align: center;
        }
        
        .start-form {
            padding: 2rem;
            text-align: center;
        }
        
        .form-group {
            margin-bottom: 1rem;
            text-align: left;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
        }
        
        .form-group input {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid #ddd;
            border-radius: 4px;
            outline: none;
        }
        
        .form-group input:focus {
            border-color: #3498db;
        }
        
        .start-btn {
            background: #3498db;
            color: white;
            border: none;
            padding: 0.75rem 2rem;
            border-radius: 4px;
            cursor: pointer;
            font-weight: 500;
            width: 100%;
        }
        
        .start-btn:hover {
            background: #2980b9;
        }
        
        .messages {
            flex: 1;
            padding: 1rem;
            overflow-y: auto;
            background: #f8f9fa;
        }
        
        .message {
            margin-bottom: 1rem;
            display: flex;
        }
        
        .message.customer {
            justify-content: flex-end;
        }
        
        .message-content {
            max-width: 80%;
            padding: 0.75rem;
            border-radius: 8px;
            background: white;
            box-shadow: 0 1px 2px rgba(0,0,0,0.1);
        }
        
        .message.customer .message-content {
            background: #3498db;
            color: white;
        }
        
        .message-form {
            padding: 1rem;
            background: white;
            border-top: 1px solid #ddd;
            display: flex;
            gap: 0.5rem;
        }
        
        .message-input {
            flex: 1;
            padding: 0.75rem;
            border: 1px solid #ddd;
            border-radius: 4px;
            outline: none;
        }
        
        .send-btn {
            background: #3498db;
            color: white;
            border: none;
            padding: 0.75rem 1rem;
            border-radius: 4px;
            cursor: pointer;
        }
        
        .send-btn:hover {
            background: #2980b9;
        }
        
        .typing-indicator {
            padding: 0.5rem 1rem;
            font-style: italic;
            color: #666;
            font-size: 0.9rem;
        }
        
        .status {
            text-align: center;
            padding: 0.5rem;
            background: #e8f5e8;
            color: #2d5a2d;
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <div class="chat-widget">
        <div class="chat-header">
            <h3>💬 Customer Support</h3>
            <p>We're here to help!</p>
        </div>
        
        <div id="startForm" class="start-form">
            <h4 style="margin-bottom: 1rem;">Start a conversation</h4>
            <p style="margin-bottom: 2rem; color: #666;">Please provide your details to begin chatting with our support team.</p>
            
            <div class="form-group">
                <label for="customerName">Name *</label>
                <input type="text" id="customerName" placeholder="Enter your name" required>
            </div>
            
            <div class="form-group">
                <label for="customerEmail">Email (optional)</label>
                <input type="email" id="customerEmail" placeholder="Enter your email">
            </div>
            
            <button class="start-btn" onclick="startChat()">Start Chat</button>
        </div>
        
        <div id="chatArea" style="display: none; flex: 1; display: flex; flex-direction: column;">
            <div class="status" id="statusMessage">
                Connected to support
            </div>
            
            <div class="messages" id="messagesList">
                <!-- Messages will be added here -->
            </div>
            
            <div id="typingIndicator" class="typing-indicator" style="display: none;">
                Support agent is typing...
            </div>
            
            <form class="message-form" id="messageForm">
                <input type="text" class="message-input" id="messageInput" placeholder="Type your message..." required>
                <button type="submit" class="send-btn">Send</button>
            </form>
        </div>
    </div>

    <script>
        // Initialize Socket.IO
        const socket = io();
        
        // Global variables
        let conversation = null;
        let customerInfo = {};
        
        // DOM elements
        const startForm = document.getElementById('startForm');
        const chatArea = document.getElementById('chatArea');
        const customerName = document.getElementById('customerName');
        const customerEmail = document.getElementById('customerEmail');
        const messagesList = document.getElementById('messagesList');
        const messageForm = document.getElementById('messageForm');
        const messageInput = document.getElementById('messageInput');
        const statusMessage = document.getElementById('statusMessage');
        const typingIndicator = document.getElementById('typingIndicator');
        
        // Socket event listeners
        socket.on('new-message', (data) => {
            if (conversation && data.conversationId === conversation.id) {
                addMessageToChat(data.message);
            }
        });
        
        socket.on('user-typing', (data) => {
            if (conversation && data.conversationId === conversation.id && data.userName !== customerInfo.name) {
                typingIndicator.style.display = 'block';
            }
        });
        
        socket.on('user-stop-typing', (data) => {
            if (conversation && data.conversationId === conversation.id) {
                typingIndicator.style.display = 'none';
            }
        });
        
        // Message form handler
        messageForm.addEventListener('submit', (e) => {
            e.preventDefault();
            sendMessage();
        });
        
        // Typing indicator
        let typingTimeout;
        messageInput.addEventListener('input', () => {
            if (conversation) {
                socket.emit('typing', {
                    conversationId: conversation.id,
                    userName: customerInfo.name
                });
                
                clearTimeout(typingTimeout);
                typingTimeout = setTimeout(() => {
                    socket.emit('stop-typing', {
                        conversationId: conversation.id,
                        userName: customerInfo.name
                    });
                }, 1000);
            }
        });
        
        function startChat() {
            const name = customerName.value.trim();
            const email = customerEmail.value.trim();
            
            if (!name) {
                alert('Please enter your name');
                return;
            }
            
            customerInfo = { name, email };
            
            // Create conversation
            fetch('/api/conversations', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    customerName: name,
                    customerEmail: email,
                    storeContext: window.location.hostname
                })
            })
            .then(response => response.json())
            .then(data => {
                conversation = data;
                
                // Join conversation room
                socket.emit('join-conversation', conversation.id);
                
                // Switch to chat view
                startForm.style.display = 'none';
                chatArea.style.display = 'flex';
                
                // Send welcome message
                setTimeout(() => {
                    sendMessage('Hello! I need help with my order.');
                }, 1000);
            })
            .catch(error => {
                console.error('Error starting conversation:', error);
                alert('Failed to start chat. Please try again.');
            });
        }
        
        function sendMessage(customMessage = null) {
            const message = customMessage || messageInput.value.trim();
            if (!conversation || !message) return;
            
            fetch(`/api/conversations/${conversation.id}/messages`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    message: message,
                    sender: customerInfo.name,
                    senderType: 'customer'
                })
            })
            .then(response => response.json())
            .then(data => {
                if (!customMessage) {
                    messageInput.value = '';
                }
                
                // Stop typing indicator
                socket.emit('stop-typing', {
                    conversationId: conversation.id,
                    userName: customerInfo.name
                });
            })
            .catch(error => console.error('Error sending message:', error));
        }
        
        function addMessageToChat(message) {
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${message.senderType}`;
            messageDiv.innerHTML = `
                <div class="message-content">
                    <div>${message.message}</div>
                    <div style="font-size: 0.8rem; opacity: 0.7; margin-top: 0.5rem;">
                        ${message.sender} • ${new Date(message.timestamp).toLocaleTimeString()}
                    </div>
                </div>
            `;
            messagesList.appendChild(messageDiv);
            messagesList.scrollTop = messagesList.scrollHeight;
        }
    </script>
</body>
</html>
