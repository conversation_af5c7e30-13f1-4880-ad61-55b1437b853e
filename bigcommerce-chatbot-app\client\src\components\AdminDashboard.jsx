import React, { useState, useEffect } from 'react';
import axios from 'axios';
import ConversationList from './ConversationList';
import ChatWindow from './ChatWindow';
import NotificationPanel from './NotificationPanel';

const AdminDashboard = ({ socket }) => {
  const [conversations, setConversations] = useState([]);
  const [selectedConversation, setSelectedConversation] = useState(null);
  const [notifications, setNotifications] = useState([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const [agentName, setAgentName] = useState('Admin Agent');

  useEffect(() => {
    // Load initial data
    loadConversations();
    loadNotifications();

    // Join admin room for real-time updates
    if (socket) {
      socket.emit('join-admin');

      // Listen for new conversations
      socket.on('new-conversation', (conversation) => {
        setConversations(prev => [conversation, ...prev]);
        
        // Show notification
        const notification = {
          id: Date.now(),
          type: 'new-conversation',
          message: `New chat from ${conversation.customerName}`,
          timestamp: new Date()
        };
        setNotifications(prev => [notification, ...prev]);
        setUnreadCount(prev => prev + 1);
      });

      // Listen for new notifications
      socket.on('new-notification', (notification) => {
        setNotifications(prev => [notification, ...prev]);
        setUnreadCount(prev => prev + 1);
      });

      // Listen for conversation updates
      socket.on('conversation-updated', (updatedConversation) => {
        setConversations(prev => 
          prev.map(conv => 
            conv.id === updatedConversation.id ? updatedConversation : conv
          )
        );
      });
    }

    return () => {
      if (socket) {
        socket.off('new-conversation');
        socket.off('new-notification');
        socket.off('conversation-updated');
      }
    };
  }, [socket]);

  const loadConversations = async () => {
    try {
      const response = await axios.get('/api/conversations');
      setConversations(response.data);
    } catch (error) {
      console.error('Failed to load conversations:', error);
    }
  };

  const loadNotifications = async () => {
    try {
      const response = await axios.get('/api/notifications');
      setNotifications(response.data);
      setUnreadCount(response.data.filter(n => !n.read).length);
    } catch (error) {
      console.error('Failed to load notifications:', error);
    }
  };

  const handleConversationSelect = (conversation) => {
    setSelectedConversation(conversation);
  };

  const handleAssignAgent = async (conversationId) => {
    try {
      await axios.put(`/api/conversations/${conversationId}/assign`, {
        agentName
      });
      loadConversations();
    } catch (error) {
      console.error('Failed to assign agent:', error);
    }
  };

  const markNotificationRead = async (notificationId) => {
    try {
      await axios.put(`/api/notifications/${notificationId}/read`);
      setNotifications(prev => 
        prev.map(n => n.id === notificationId ? { ...n, read: true } : n)
      );
      setUnreadCount(prev => Math.max(0, prev - 1));
    } catch (error) {
      console.error('Failed to mark notification as read:', error);
    }
  };

  return (
    <div className="admin-dashboard">
      <div className="dashboard-header">
        <h1>💬 ChatBot Admin Dashboard</h1>
        <div className="header-controls">
          <input
            type="text"
            placeholder="Agent Name"
            value={agentName}
            onChange={(e) => setAgentName(e.target.value)}
            className="agent-input"
          />
          <div className="notification-badge">
            🔔 {unreadCount > 0 && <span className="badge">{unreadCount}</span>}
          </div>
        </div>
      </div>

      <div className="dashboard-content">
        <div className="sidebar">
          <div className="sidebar-section">
            <h3>Conversations ({conversations.length})</h3>
            <ConversationList
              conversations={conversations}
              selectedConversation={selectedConversation}
              onConversationSelect={handleConversationSelect}
              onAssignAgent={handleAssignAgent}
              agentName={agentName}
            />
          </div>
          
          <div className="sidebar-section">
            <h3>Notifications</h3>
            <NotificationPanel
              notifications={notifications.slice(0, 5)}
              onMarkRead={markNotificationRead}
            />
          </div>
        </div>

        <div className="main-content">
          {selectedConversation ? (
            <ChatWindow
              conversation={selectedConversation}
              socket={socket}
              agentName={agentName}
              isAdmin={true}
            />
          ) : (
            <div className="no-conversation">
              <h3>Select a conversation to start chatting</h3>
              <p>Choose a conversation from the sidebar to view messages and respond to customers.</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default AdminDashboard;
