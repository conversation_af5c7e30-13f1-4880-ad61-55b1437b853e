[{"D:\\Projects\\practice\\bigcommerce-chatbot-app\\client\\src\\index.js": "1", "D:\\Projects\\practice\\bigcommerce-chatbot-app\\client\\src\\App.jsx": "2", "D:\\Projects\\practice\\bigcommerce-chatbot-app\\client\\src\\components\\AdminDashboard.jsx": "3", "D:\\Projects\\practice\\bigcommerce-chatbot-app\\client\\src\\components\\ChatWidget.jsx": "4", "D:\\Projects\\practice\\bigcommerce-chatbot-app\\client\\src\\components\\ConversationList.jsx": "5", "D:\\Projects\\practice\\bigcommerce-chatbot-app\\client\\src\\components\\ChatWindow.jsx": "6", "D:\\Projects\\practice\\bigcommerce-chatbot-app\\client\\src\\components\\NotificationPanel.jsx": "7"}, {"size": 232, "mtime": 1752643036074, "results": "8", "hashOfConfig": "9"}, {"size": 1070, "mtime": 1752642865143, "results": "10", "hashOfConfig": "9"}, {"size": 5237, "mtime": 1752642889665, "results": "11", "hashOfConfig": "9"}, {"size": 3028, "mtime": 1752642978367, "results": "12", "hashOfConfig": "9"}, {"size": 2979, "mtime": 1752642907290, "results": "13", "hashOfConfig": "9"}, {"size": 5409, "mtime": 1752642932587, "results": "14", "hashOfConfig": "9"}, {"size": 1825, "mtime": 1752642961346, "results": "15", "hashOfConfig": "9"}, {"filePath": "16", "messages": "17", "suppressedMessages": "18", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "dbzo<PERSON>r", {"filePath": "19", "messages": "20", "suppressedMessages": "21", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "22", "messages": "23", "suppressedMessages": "24", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "25", "messages": "26", "suppressedMessages": "27", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "28", "messages": "29", "suppressedMessages": "30", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "31", "messages": "32", "suppressedMessages": "33", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "34", "messages": "35", "suppressedMessages": "36", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\Projects\\practice\\bigcommerce-chatbot-app\\client\\src\\index.js", [], [], "D:\\Projects\\practice\\bigcommerce-chatbot-app\\client\\src\\App.jsx", [], [], "D:\\Projects\\practice\\bigcommerce-chatbot-app\\client\\src\\components\\AdminDashboard.jsx", [], [], "D:\\Projects\\practice\\bigcommerce-chatbot-app\\client\\src\\components\\ChatWidget.jsx", ["37"], [], "D:\\Projects\\practice\\bigcommerce-chatbot-app\\client\\src\\components\\ConversationList.jsx", [], [], "D:\\Projects\\practice\\bigcommerce-chatbot-app\\client\\src\\components\\ChatWindow.jsx", [], [], "D:\\Projects\\practice\\bigcommerce-chatbot-app\\client\\src\\components\\NotificationPanel.jsx", [], [], {"ruleId": "38", "severity": 1, "message": "39", "line": 1, "column": 27, "nodeType": "40", "messageId": "41", "endLine": 1, "endColumn": 36}, "no-unused-vars", "'useEffect' is defined but never used.", "Identifier", "unusedVar"]