{"ast": null, "code": "export const globalThisShim = (() => {\n  if (typeof self !== \"undefined\") {\n    return self;\n  } else if (typeof window !== \"undefined\") {\n    return window;\n  } else {\n    return Function(\"return this\")();\n  }\n})();", "map": {"version": 3, "names": ["globalThisShim", "self", "window", "Function"], "sources": ["D:/Projects/practice/bigcommerce-chatbot-app/client/node_modules/engine.io-client/build/esm/globalThis.browser.js"], "sourcesContent": ["export const globalThisShim = (() => {\n    if (typeof self !== \"undefined\") {\n        return self;\n    }\n    else if (typeof window !== \"undefined\") {\n        return window;\n    }\n    else {\n        return Function(\"return this\")();\n    }\n})();\n"], "mappings": "AAAA,OAAO,MAAMA,cAAc,GAAG,CAAC,MAAM;EACjC,IAAI,OAAOC,IAAI,KAAK,WAAW,EAAE;IAC7B,OAAOA,IAAI;EACf,CAAC,MACI,IAAI,OAAOC,MAAM,KAAK,WAAW,EAAE;IACpC,OAAOA,MAAM;EACjB,CAAC,MACI;IACD,OAAOC,QAAQ,CAAC,aAAa,CAAC,CAAC,CAAC;EACpC;AACJ,CAAC,EAAE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}