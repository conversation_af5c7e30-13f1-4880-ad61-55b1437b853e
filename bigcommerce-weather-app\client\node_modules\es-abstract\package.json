{"name": "es-abstract", "version": "1.24.0", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://ljharb.codes"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://ljharb.codes"}], "description": "ECMAScript spec abstract operations.", "license": "MIT", "main": "index.js", "browser": {"worker_threads": false}, "type": "commonjs", "sideEffects": false, "scripts": {"spackled": "git ls-files | xargs git check-attr spackled | grep -v 'unspecified$' | cut -d: -f1", "prespackle": "npm run --silent spackled | xargs rm || true", "spackle": "node operations/spackle 1 && node operations/build-unicode.mjs", "postspackle": "git ls-files | xargs git check-attr spackled | grep -v 'unspecified$' | cut -d: -f1 | xargs git add", "prepack": "npmignore --auto --commentLines=autogenerated", "prepublish": "not-in-publish || npm run prepublishOnly", "prepublishOnly": "safe-publish-latest && npm run spackle", "pretest": "npm run lint", "test": "npm run tests-only && npm run test:ses", "test:ses": "node --stack-size=5120 test/ses-compat", "posttest": "npx npm@'>= 10.2' audit --production", "tests-only": "nyc node --stack-size=5120 test", "lint": "eslint .", "eccheck": "eclint check $(git ls-files | xargs find 2> /dev/null | grep -vE 'node_modules|\\.git')"}, "repository": {"type": "git", "url": "git://github.com/ljharb/es-abstract.git"}, "keywords": ["ECMAScript", "ES", "abstract", "operation", "abstract operation", "JavaScript", "ES5", "ES6", "ES7"], "dependencies": {"array-buffer-byte-length": "^1.0.2", "arraybuffer.prototype.slice": "^1.0.4", "available-typed-arrays": "^1.0.7", "call-bind": "^1.0.8", "call-bound": "^1.0.4", "data-view-buffer": "^1.0.2", "data-view-byte-length": "^1.0.2", "data-view-byte-offset": "^1.0.1", "es-define-property": "^1.0.1", "es-errors": "^1.3.0", "es-object-atoms": "^1.1.1", "es-set-tostringtag": "^2.1.0", "es-to-primitive": "^1.3.0", "function.prototype.name": "^1.1.8", "get-intrinsic": "^1.3.0", "get-proto": "^1.0.1", "get-symbol-description": "^1.1.0", "globalthis": "^1.0.4", "gopd": "^1.2.0", "has-property-descriptors": "^1.0.2", "has-proto": "^1.2.0", "has-symbols": "^1.1.0", "hasown": "^2.0.2", "internal-slot": "^1.1.0", "is-array-buffer": "^3.0.5", "is-callable": "^1.2.7", "is-data-view": "^1.0.2", "is-negative-zero": "^2.0.3", "is-regex": "^1.2.1", "is-set": "^2.0.3", "is-shared-array-buffer": "^1.0.4", "is-string": "^1.1.1", "is-typed-array": "^1.1.15", "is-weakref": "^1.1.1", "math-intrinsics": "^1.1.0", "object-inspect": "^1.13.4", "object-keys": "^1.1.1", "object.assign": "^4.1.7", "own-keys": "^1.0.1", "regexp.prototype.flags": "^1.5.4", "safe-array-concat": "^1.1.3", "safe-push-apply": "^1.0.0", "safe-regex-test": "^1.1.0", "set-proto": "^1.0.0", "stop-iteration-iterator": "^1.1.0", "string.prototype.trim": "^1.2.10", "string.prototype.trimend": "^1.0.9", "string.prototype.trimstart": "^1.0.8", "typed-array-buffer": "^1.0.3", "typed-array-byte-length": "^1.0.3", "typed-array-byte-offset": "^1.0.4", "typed-array-length": "^1.0.7", "unbox-primitive": "^1.1.0", "which-typed-array": "^1.1.19"}, "devDependencies": {"@ljharb/eslint-config": "^21.1.1", "@unicode/unicode-15.0.0": "^1.6.6", "array.from": "^1.1.6", "array.prototype.filter": "^1.0.4", "array.prototype.flatmap": "^1.3.3", "array.prototype.indexof": "^1.0.8", "available-regexp-flags": "^1.0.4", "cheerio": "=1.0.0-rc.3", "define-accessor-property": "^1.0.0", "define-data-property": "^1.1.4", "diff": "^7.0.0", "eclint": "^2.8.1", "es-value-fixtures": "^1.7.1", "eslint": "=8.8.0", "for-each": "^0.3.5", "function-bind": "^1.1.2", "functions-have-names": "^1.2.3", "glob": "^7.2.3", "has-bigints": "^1.1.0", "has-named-captures": "^1.0.0", "has-strict-mode": "^1.1.0", "in-publish": "^2.0.1", "is-bigint": "^1.1.0", "is-core-module": "^2.16.1", "jackspeak": "=2.1.1", "make-async-function": "^1.0.0", "make-async-generator-function": "^1.0.0", "make-generator-function": "^2.0.0", "mock-property": "^1.1.0", "npmignore": "^0.3.1", "nyc": "^10.3.2", "object.fromentries": "^2.0.8", "safe-bigint": "^1.1.1", "safe-publish-latest": "^2.0.0", "ses": "^1.12.0", "tape": "^5.9.0"}, "testling": {"files": "test/index.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "engines": {"node": ">= 0.4"}, "publishConfig": {"ignore": [".github", "", "# dev scripts", "operations/*", "!operations/es5.js", "!operations/20*.js", "", "test/", "", ".gitattributes"]}}