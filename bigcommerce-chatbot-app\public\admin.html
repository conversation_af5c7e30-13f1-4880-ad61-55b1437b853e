<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ChatBot Admin Dashboard</title>
    <!-- No external dependencies -->
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            background: #f5f5f5;
            height: 100vh;
            display: flex;
            flex-direction: column;
        }
        
        .header {
            background: #2c3e50;
            color: white;
            padding: 1rem 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .notification-badge {
            background: #e74c3c;
            color: white;
            border-radius: 50%;
            padding: 0.25rem 0.5rem;
            font-size: 0.8rem;
            margin-left: 0.5rem;
        }
        
        .main-content {
            flex: 1;
            display: flex;
            overflow: hidden;
        }
        
        .sidebar {
            width: 300px;
            background: white;
            border-right: 1px solid #ddd;
            display: flex;
            flex-direction: column;
        }
        
        .conversations {
            flex: 1;
            overflow-y: auto;
            padding: 1rem;
        }
        
        .conversation-item {
            padding: 1rem;
            border-bottom: 1px solid #eee;
            cursor: pointer;
            transition: background 0.2s;
        }
        
        .conversation-item:hover {
            background: #f8f9fa;
        }
        
        .conversation-item.active {
            background: #3498db;
            color: white;
        }
        
        .customer-name {
            font-weight: 600;
            margin-bottom: 0.25rem;
        }
        
        .last-message {
            font-size: 0.9rem;
            opacity: 0.7;
        }
        
        .chat-area {
            flex: 1;
            display: flex;
            flex-direction: column;
            background: white;
        }
        
        .chat-header {
            background: #34495e;
            color: white;
            padding: 1rem;
        }
        
        .messages {
            flex: 1;
            padding: 1rem;
            overflow-y: auto;
            background: #f8f9fa;
        }
        
        .message {
            margin-bottom: 1rem;
            display: flex;
        }
        
        .message.agent {
            justify-content: flex-end;
        }
        
        .message-content {
            max-width: 70%;
            padding: 0.75rem;
            border-radius: 8px;
            background: white;
            box-shadow: 0 1px 2px rgba(0,0,0,0.1);
        }
        
        .message.agent .message-content {
            background: #3498db;
            color: white;
        }
        
        .message-form {
            padding: 1rem;
            background: white;
            border-top: 1px solid #ddd;
            display: flex;
            gap: 0.5rem;
        }
        
        .message-input {
            flex: 1;
            padding: 0.75rem;
            border: 1px solid #ddd;
            border-radius: 4px;
            outline: none;
        }
        
        .send-btn {
            background: #3498db;
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 4px;
            cursor: pointer;
        }
        
        .send-btn:hover {
            background: #2980b9;
        }
        
        .no-conversation {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #7f8c8d;
            text-align: center;
        }
        
        .notifications {
            max-height: 200px;
            overflow-y: auto;
            border-top: 1px solid #ddd;
            padding: 1rem;
        }
        
        .notification {
            padding: 0.5rem;
            background: #ebf3fd;
            border-radius: 4px;
            margin-bottom: 0.5rem;
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>💬 ChatBot Admin Dashboard</h1>
        <div>
            <input type="text" id="agentName" placeholder="Agent Name" value="Admin Agent" style="padding: 0.5rem; margin-right: 1rem;">
            <span>🔔 Notifications <span id="notificationCount" class="notification-badge" style="display: none;">0</span></span>
        </div>
    </div>
    
    <div class="main-content">
        <div class="sidebar">
            <div class="conversations" id="conversationsList">
                <div style="text-align: center; padding: 2rem; color: #7f8c8d;">
                    No conversations yet
                </div>
            </div>
            
            <div class="notifications">
                <h4 style="margin-bottom: 0.5rem;">Recent Notifications</h4>
                <div id="notificationsList">
                    <div style="text-align: center; color: #7f8c8d; font-size: 0.9rem;">
                        No notifications
                    </div>
                </div>
            </div>
        </div>
        
        <div class="chat-area">
            <div id="noChatSelected" class="no-conversation">
                <div>
                    <h3>Select a conversation to start chatting</h3>
                    <p>Choose a conversation from the sidebar to view messages and respond to customers.</p>
                </div>
            </div>
            
            <div id="chatWindow" style="display: none; flex: 1; display: flex; flex-direction: column;">
                <div class="chat-header">
                    <h3 id="customerName">Customer Name</h3>
                    <p id="customerEmail"><EMAIL></p>
                </div>
                
                <div class="messages" id="messagesList">
                    <!-- Messages will be added here -->
                </div>
                
                <form class="message-form" id="messageForm">
                    <input type="text" class="message-input" id="messageInput" placeholder="Type your message..." required>
                    <button type="submit" class="send-btn">Send</button>
                </form>
            </div>
        </div>
    </div>

    <script>
        // Initialize Socket.IO
        const socket = io();
        
        // Global variables
        let conversations = [];
        let selectedConversation = null;
        let notifications = [];
        
        // DOM elements
        const conversationsList = document.getElementById('conversationsList');
        const notificationsList = document.getElementById('notificationsList');
        const notificationCount = document.getElementById('notificationCount');
        const noChatSelected = document.getElementById('noChatSelected');
        const chatWindow = document.getElementById('chatWindow');
        const customerName = document.getElementById('customerName');
        const customerEmail = document.getElementById('customerEmail');
        const messagesList = document.getElementById('messagesList');
        const messageForm = document.getElementById('messageForm');
        const messageInput = document.getElementById('messageInput');
        const agentName = document.getElementById('agentName');
        
        // Join admin room
        socket.emit('join-admin');
        
        // Socket event listeners
        socket.on('new-conversation', (conversation) => {
            conversations.unshift(conversation);
            updateConversationsList();
            addNotification(`New chat from ${conversation.customerName}`);
        });
        
        socket.on('new-message', (data) => {
            if (selectedConversation && data.conversationId === selectedConversation.id) {
                addMessageToChat(data.message);
            }
            
            // Update conversation in list
            const conv = conversations.find(c => c.id === data.conversationId);
            if (conv) {
                conv.messages.push(data.message);
                conv.lastActivity = new Date();
                updateConversationsList();
            }
        });
        
        socket.on('new-notification', (notification) => {
            addNotification(notification.message || `New message from ${notification.customerName}`);
        });
        
        // Load initial data
        loadConversations();
        
        // Message form handler
        messageForm.addEventListener('submit', (e) => {
            e.preventDefault();
            sendMessage();
        });
        
        function loadConversations() {
            fetch('/api/conversations')
                .then(response => response.json())
                .then(data => {
                    conversations = data;
                    updateConversationsList();
                })
                .catch(error => console.error('Error loading conversations:', error));
        }
        
        function updateConversationsList() {
            if (conversations.length === 0) {
                conversationsList.innerHTML = '<div style="text-align: center; padding: 2rem; color: #7f8c8d;">No conversations yet</div>';
                return;
            }
            
            conversationsList.innerHTML = conversations.map(conv => `
                <div class="conversation-item ${selectedConversation && selectedConversation.id === conv.id ? 'active' : ''}" 
                     onclick="selectConversation('${conv.id}')">
                    <div class="customer-name">${conv.customerName}</div>
                    <div class="last-message">
                        ${conv.messages.length > 0 ? conv.messages[conv.messages.length - 1].message.substring(0, 50) + '...' : 'No messages yet'}
                    </div>
                    <div style="font-size: 0.8rem; color: #666; margin-top: 0.25rem;">
                        ${conv.messages.length} messages
                        ${conv.assignedAgent ? ` • Assigned to ${conv.assignedAgent}` : ''}
                    </div>
                </div>
            `).join('');
        }
        
        function selectConversation(conversationId) {
            selectedConversation = conversations.find(c => c.id === conversationId);
            if (selectedConversation) {
                // Join conversation room
                socket.emit('join-conversation', conversationId);
                
                // Update UI
                noChatSelected.style.display = 'none';
                chatWindow.style.display = 'flex';
                
                customerName.textContent = selectedConversation.customerName;
                customerEmail.textContent = selectedConversation.customerEmail || 'No email provided';
                
                // Load messages
                updateMessagesList();
                updateConversationsList();
            }
        }
        
        function updateMessagesList() {
            if (!selectedConversation) return;
            
            messagesList.innerHTML = selectedConversation.messages.map(msg => `
                <div class="message ${msg.senderType}">
                    <div class="message-content">
                        <div>${msg.message}</div>
                        <div style="font-size: 0.8rem; opacity: 0.7; margin-top: 0.5rem;">
                            ${msg.sender} • ${new Date(msg.timestamp).toLocaleTimeString()}
                        </div>
                    </div>
                </div>
            `).join('');
            
            // Scroll to bottom
            messagesList.scrollTop = messagesList.scrollHeight;
        }
        
        function sendMessage() {
            if (!selectedConversation || !messageInput.value.trim()) return;
            
            const message = messageInput.value.trim();
            const agent = agentName.value || 'Admin Agent';
            
            fetch(`/api/conversations/${selectedConversation.id}/messages`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    message: message,
                    sender: agent,
                    senderType: 'agent'
                })
            })
            .then(response => response.json())
            .then(data => {
                messageInput.value = '';
                // Message will be added via socket event
            })
            .catch(error => console.error('Error sending message:', error));
        }
        
        function addMessageToChat(message) {
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${message.senderType}`;
            messageDiv.innerHTML = `
                <div class="message-content">
                    <div>${message.message}</div>
                    <div style="font-size: 0.8rem; opacity: 0.7; margin-top: 0.5rem;">
                        ${message.sender} • ${new Date(message.timestamp).toLocaleTimeString()}
                    </div>
                </div>
            `;
            messagesList.appendChild(messageDiv);
            messagesList.scrollTop = messagesList.scrollHeight;
        }
        
        function addNotification(message) {
            notifications.unshift({
                message: message,
                timestamp: new Date()
            });
            
            // Keep only last 5 notifications
            notifications = notifications.slice(0, 5);
            
            updateNotificationsList();
            updateNotificationCount();
        }
        
        function updateNotificationsList() {
            if (notifications.length === 0) {
                notificationsList.innerHTML = '<div style="text-align: center; color: #7f8c8d; font-size: 0.9rem;">No notifications</div>';
                return;
            }
            
            notificationsList.innerHTML = notifications.map(notif => `
                <div class="notification">
                    ${notif.message}
                    <div style="font-size: 0.8rem; opacity: 0.7; margin-top: 0.25rem;">
                        ${new Date(notif.timestamp).toLocaleTimeString()}
                    </div>
                </div>
            `).join('');
        }
        
        function updateNotificationCount() {
            const count = notifications.length;
            if (count > 0) {
                notificationCount.textContent = count;
                notificationCount.style.display = 'inline';
            } else {
                notificationCount.style.display = 'none';
            }
        }
    </script>
</body>
</html>
