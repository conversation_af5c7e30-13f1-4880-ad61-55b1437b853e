{"ast": null, "code": "import { Transport } from \"../transport.js\";\nimport { yeast } from \"../contrib/yeast.js\";\nimport { pick } from \"../util.js\";\nimport { nextTick, usingBrowserWebSocket, WebSocket } from \"./websocket-constructor.js\";\nimport { encodePacket } from \"engine.io-parser\";\n// detect ReactNative environment\nconst isReactNative = typeof navigator !== \"undefined\" && typeof navigator.product === \"string\" && navigator.product.toLowerCase() === \"reactnative\";\nexport class WS extends Transport {\n  /**\n   * WebSocket transport constructor.\n   *\n   * @param {Object} opts - connection options\n   * @protected\n   */\n  constructor(opts) {\n    super(opts);\n    this.supportsBinary = !opts.forceBase64;\n  }\n  get name() {\n    return \"websocket\";\n  }\n  doOpen() {\n    if (!this.check()) {\n      // let probe timeout\n      return;\n    }\n    const uri = this.uri();\n    const protocols = this.opts.protocols;\n    // React Native only supports the 'headers' option, and will print a warning if anything else is passed\n    const opts = isReactNative ? {} : pick(this.opts, \"agent\", \"perMessageDeflate\", \"pfx\", \"key\", \"passphrase\", \"cert\", \"ca\", \"ciphers\", \"rejectUnauthorized\", \"localAddress\", \"protocolVersion\", \"origin\", \"maxPayload\", \"family\", \"checkServerIdentity\");\n    if (this.opts.extraHeaders) {\n      opts.headers = this.opts.extraHeaders;\n    }\n    try {\n      this.ws = usingBrowserWebSocket && !isReactNative ? protocols ? new WebSocket(uri, protocols) : new WebSocket(uri) : new WebSocket(uri, protocols, opts);\n    } catch (err) {\n      return this.emitReserved(\"error\", err);\n    }\n    this.ws.binaryType = this.socket.binaryType;\n    this.addEventListeners();\n  }\n  /**\n   * Adds event listeners to the socket\n   *\n   * @private\n   */\n  addEventListeners() {\n    this.ws.onopen = () => {\n      if (this.opts.autoUnref) {\n        this.ws._socket.unref();\n      }\n      this.onOpen();\n    };\n    this.ws.onclose = closeEvent => this.onClose({\n      description: \"websocket connection closed\",\n      context: closeEvent\n    });\n    this.ws.onmessage = ev => this.onData(ev.data);\n    this.ws.onerror = e => this.onError(\"websocket error\", e);\n  }\n  write(packets) {\n    this.writable = false;\n    // encodePacket efficient as it uses WS framing\n    // no need for encodePayload\n    for (let i = 0; i < packets.length; i++) {\n      const packet = packets[i];\n      const lastPacket = i === packets.length - 1;\n      encodePacket(packet, this.supportsBinary, data => {\n        // always create a new object (GH-437)\n        const opts = {};\n        if (!usingBrowserWebSocket) {\n          if (packet.options) {\n            opts.compress = packet.options.compress;\n          }\n          if (this.opts.perMessageDeflate) {\n            const len =\n            // @ts-ignore\n            \"string\" === typeof data ? Buffer.byteLength(data) : data.length;\n            if (len < this.opts.perMessageDeflate.threshold) {\n              opts.compress = false;\n            }\n          }\n        }\n        // Sometimes the websocket has already been closed but the browser didn't\n        // have a chance of informing us about it yet, in that case send will\n        // throw an error\n        try {\n          if (usingBrowserWebSocket) {\n            // TypeError is thrown when passing the second argument on Safari\n            this.ws.send(data);\n          } else {\n            this.ws.send(data, opts);\n          }\n        } catch (e) {}\n        if (lastPacket) {\n          // fake drain\n          // defer to next tick to allow Socket to clear writeBuffer\n          nextTick(() => {\n            this.writable = true;\n            this.emitReserved(\"drain\");\n          }, this.setTimeoutFn);\n        }\n      });\n    }\n  }\n  doClose() {\n    if (typeof this.ws !== \"undefined\") {\n      this.ws.close();\n      this.ws = null;\n    }\n  }\n  /**\n   * Generates uri for connection.\n   *\n   * @private\n   */\n  uri() {\n    const schema = this.opts.secure ? \"wss\" : \"ws\";\n    const query = this.query || {};\n    // append timestamp to URI\n    if (this.opts.timestampRequests) {\n      query[this.opts.timestampParam] = yeast();\n    }\n    // communicate binary support capabilities\n    if (!this.supportsBinary) {\n      query.b64 = 1;\n    }\n    return this.createUri(schema, query);\n  }\n  /**\n   * Feature detection for WebSocket.\n   *\n   * @return {Boolean} whether this transport is available.\n   * @private\n   */\n  check() {\n    return !!WebSocket;\n  }\n}", "map": {"version": 3, "names": ["Transport", "yeast", "pick", "nextTick", "usingBrowserWebSocket", "WebSocket", "encodePacket", "isReactNative", "navigator", "product", "toLowerCase", "WS", "constructor", "opts", "supportsBinary", "forceBase64", "name", "doOpen", "check", "uri", "protocols", "extraHeaders", "headers", "ws", "err", "emit<PERSON><PERSON><PERSON><PERSON>", "binaryType", "socket", "addEventListeners", "onopen", "autoUnref", "_socket", "unref", "onOpen", "onclose", "closeEvent", "onClose", "description", "context", "onmessage", "ev", "onData", "data", "onerror", "e", "onError", "write", "packets", "writable", "i", "length", "packet", "lastPacket", "options", "compress", "perMessageDeflate", "len", "<PERSON><PERSON><PERSON>", "byteLength", "threshold", "send", "setTimeoutFn", "doClose", "close", "schema", "secure", "query", "timestampRequests", "timestampParam", "b64", "createUri"], "sources": ["D:/Projects/practice/bigcommerce-chatbot-app/client/node_modules/engine.io-client/build/esm/transports/websocket.js"], "sourcesContent": ["import { Transport } from \"../transport.js\";\nimport { yeast } from \"../contrib/yeast.js\";\nimport { pick } from \"../util.js\";\nimport { nextTick, usingBrowserWebSocket, WebSocket, } from \"./websocket-constructor.js\";\nimport { encodePacket } from \"engine.io-parser\";\n// detect ReactNative environment\nconst isReactNative = typeof navigator !== \"undefined\" &&\n    typeof navigator.product === \"string\" &&\n    navigator.product.toLowerCase() === \"reactnative\";\nexport class WS extends Transport {\n    /**\n     * WebSocket transport constructor.\n     *\n     * @param {Object} opts - connection options\n     * @protected\n     */\n    constructor(opts) {\n        super(opts);\n        this.supportsBinary = !opts.forceBase64;\n    }\n    get name() {\n        return \"websocket\";\n    }\n    doOpen() {\n        if (!this.check()) {\n            // let probe timeout\n            return;\n        }\n        const uri = this.uri();\n        const protocols = this.opts.protocols;\n        // React Native only supports the 'headers' option, and will print a warning if anything else is passed\n        const opts = isReactNative\n            ? {}\n            : pick(this.opts, \"agent\", \"perMessageDeflate\", \"pfx\", \"key\", \"passphrase\", \"cert\", \"ca\", \"ciphers\", \"rejectUnauthorized\", \"localAddress\", \"protocolVersion\", \"origin\", \"maxPayload\", \"family\", \"checkServerIdentity\");\n        if (this.opts.extraHeaders) {\n            opts.headers = this.opts.extraHeaders;\n        }\n        try {\n            this.ws =\n                usingBrowserWebSocket && !isReactNative\n                    ? protocols\n                        ? new WebSocket(uri, protocols)\n                        : new WebSocket(uri)\n                    : new WebSocket(uri, protocols, opts);\n        }\n        catch (err) {\n            return this.emitReserved(\"error\", err);\n        }\n        this.ws.binaryType = this.socket.binaryType;\n        this.addEventListeners();\n    }\n    /**\n     * Adds event listeners to the socket\n     *\n     * @private\n     */\n    addEventListeners() {\n        this.ws.onopen = () => {\n            if (this.opts.autoUnref) {\n                this.ws._socket.unref();\n            }\n            this.onOpen();\n        };\n        this.ws.onclose = (closeEvent) => this.onClose({\n            description: \"websocket connection closed\",\n            context: closeEvent,\n        });\n        this.ws.onmessage = (ev) => this.onData(ev.data);\n        this.ws.onerror = (e) => this.onError(\"websocket error\", e);\n    }\n    write(packets) {\n        this.writable = false;\n        // encodePacket efficient as it uses WS framing\n        // no need for encodePayload\n        for (let i = 0; i < packets.length; i++) {\n            const packet = packets[i];\n            const lastPacket = i === packets.length - 1;\n            encodePacket(packet, this.supportsBinary, (data) => {\n                // always create a new object (GH-437)\n                const opts = {};\n                if (!usingBrowserWebSocket) {\n                    if (packet.options) {\n                        opts.compress = packet.options.compress;\n                    }\n                    if (this.opts.perMessageDeflate) {\n                        const len = \n                        // @ts-ignore\n                        \"string\" === typeof data ? Buffer.byteLength(data) : data.length;\n                        if (len < this.opts.perMessageDeflate.threshold) {\n                            opts.compress = false;\n                        }\n                    }\n                }\n                // Sometimes the websocket has already been closed but the browser didn't\n                // have a chance of informing us about it yet, in that case send will\n                // throw an error\n                try {\n                    if (usingBrowserWebSocket) {\n                        // TypeError is thrown when passing the second argument on Safari\n                        this.ws.send(data);\n                    }\n                    else {\n                        this.ws.send(data, opts);\n                    }\n                }\n                catch (e) {\n                }\n                if (lastPacket) {\n                    // fake drain\n                    // defer to next tick to allow Socket to clear writeBuffer\n                    nextTick(() => {\n                        this.writable = true;\n                        this.emitReserved(\"drain\");\n                    }, this.setTimeoutFn);\n                }\n            });\n        }\n    }\n    doClose() {\n        if (typeof this.ws !== \"undefined\") {\n            this.ws.close();\n            this.ws = null;\n        }\n    }\n    /**\n     * Generates uri for connection.\n     *\n     * @private\n     */\n    uri() {\n        const schema = this.opts.secure ? \"wss\" : \"ws\";\n        const query = this.query || {};\n        // append timestamp to URI\n        if (this.opts.timestampRequests) {\n            query[this.opts.timestampParam] = yeast();\n        }\n        // communicate binary support capabilities\n        if (!this.supportsBinary) {\n            query.b64 = 1;\n        }\n        return this.createUri(schema, query);\n    }\n    /**\n     * Feature detection for WebSocket.\n     *\n     * @return {Boolean} whether this transport is available.\n     * @private\n     */\n    check() {\n        return !!WebSocket;\n    }\n}\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,iBAAiB;AAC3C,SAASC,KAAK,QAAQ,qBAAqB;AAC3C,SAASC,IAAI,QAAQ,YAAY;AACjC,SAASC,QAAQ,EAAEC,qBAAqB,EAAEC,SAAS,QAAS,4BAA4B;AACxF,SAASC,YAAY,QAAQ,kBAAkB;AAC/C;AACA,MAAMC,aAAa,GAAG,OAAOC,SAAS,KAAK,WAAW,IAClD,OAAOA,SAAS,CAACC,OAAO,KAAK,QAAQ,IACrCD,SAAS,CAACC,OAAO,CAACC,WAAW,CAAC,CAAC,KAAK,aAAa;AACrD,OAAO,MAAMC,EAAE,SAASX,SAAS,CAAC;EAC9B;AACJ;AACA;AACA;AACA;AACA;EACIY,WAAWA,CAACC,IAAI,EAAE;IACd,KAAK,CAACA,IAAI,CAAC;IACX,IAAI,CAACC,cAAc,GAAG,CAACD,IAAI,CAACE,WAAW;EAC3C;EACA,IAAIC,IAAIA,CAAA,EAAG;IACP,OAAO,WAAW;EACtB;EACAC,MAAMA,CAAA,EAAG;IACL,IAAI,CAAC,IAAI,CAACC,KAAK,CAAC,CAAC,EAAE;MACf;MACA;IACJ;IACA,MAAMC,GAAG,GAAG,IAAI,CAACA,GAAG,CAAC,CAAC;IACtB,MAAMC,SAAS,GAAG,IAAI,CAACP,IAAI,CAACO,SAAS;IACrC;IACA,MAAMP,IAAI,GAAGN,aAAa,GACpB,CAAC,CAAC,GACFL,IAAI,CAAC,IAAI,CAACW,IAAI,EAAE,OAAO,EAAE,mBAAmB,EAAE,KAAK,EAAE,KAAK,EAAE,YAAY,EAAE,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,oBAAoB,EAAE,cAAc,EAAE,iBAAiB,EAAE,QAAQ,EAAE,YAAY,EAAE,QAAQ,EAAE,qBAAqB,CAAC;IAC1N,IAAI,IAAI,CAACA,IAAI,CAACQ,YAAY,EAAE;MACxBR,IAAI,CAACS,OAAO,GAAG,IAAI,CAACT,IAAI,CAACQ,YAAY;IACzC;IACA,IAAI;MACA,IAAI,CAACE,EAAE,GACHnB,qBAAqB,IAAI,CAACG,aAAa,GACjCa,SAAS,GACL,IAAIf,SAAS,CAACc,GAAG,EAAEC,SAAS,CAAC,GAC7B,IAAIf,SAAS,CAACc,GAAG,CAAC,GACtB,IAAId,SAAS,CAACc,GAAG,EAAEC,SAAS,EAAEP,IAAI,CAAC;IACjD,CAAC,CACD,OAAOW,GAAG,EAAE;MACR,OAAO,IAAI,CAACC,YAAY,CAAC,OAAO,EAAED,GAAG,CAAC;IAC1C;IACA,IAAI,CAACD,EAAE,CAACG,UAAU,GAAG,IAAI,CAACC,MAAM,CAACD,UAAU;IAC3C,IAAI,CAACE,iBAAiB,CAAC,CAAC;EAC5B;EACA;AACJ;AACA;AACA;AACA;EACIA,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAACL,EAAE,CAACM,MAAM,GAAG,MAAM;MACnB,IAAI,IAAI,CAAChB,IAAI,CAACiB,SAAS,EAAE;QACrB,IAAI,CAACP,EAAE,CAACQ,OAAO,CAACC,KAAK,CAAC,CAAC;MAC3B;MACA,IAAI,CAACC,MAAM,CAAC,CAAC;IACjB,CAAC;IACD,IAAI,CAACV,EAAE,CAACW,OAAO,GAAIC,UAAU,IAAK,IAAI,CAACC,OAAO,CAAC;MAC3CC,WAAW,EAAE,6BAA6B;MAC1CC,OAAO,EAAEH;IACb,CAAC,CAAC;IACF,IAAI,CAACZ,EAAE,CAACgB,SAAS,GAAIC,EAAE,IAAK,IAAI,CAACC,MAAM,CAACD,EAAE,CAACE,IAAI,CAAC;IAChD,IAAI,CAACnB,EAAE,CAACoB,OAAO,GAAIC,CAAC,IAAK,IAAI,CAACC,OAAO,CAAC,iBAAiB,EAAED,CAAC,CAAC;EAC/D;EACAE,KAAKA,CAACC,OAAO,EAAE;IACX,IAAI,CAACC,QAAQ,GAAG,KAAK;IACrB;IACA;IACA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,OAAO,CAACG,MAAM,EAAED,CAAC,EAAE,EAAE;MACrC,MAAME,MAAM,GAAGJ,OAAO,CAACE,CAAC,CAAC;MACzB,MAAMG,UAAU,GAAGH,CAAC,KAAKF,OAAO,CAACG,MAAM,GAAG,CAAC;MAC3C5C,YAAY,CAAC6C,MAAM,EAAE,IAAI,CAACrC,cAAc,EAAG4B,IAAI,IAAK;QAChD;QACA,MAAM7B,IAAI,GAAG,CAAC,CAAC;QACf,IAAI,CAACT,qBAAqB,EAAE;UACxB,IAAI+C,MAAM,CAACE,OAAO,EAAE;YAChBxC,IAAI,CAACyC,QAAQ,GAAGH,MAAM,CAACE,OAAO,CAACC,QAAQ;UAC3C;UACA,IAAI,IAAI,CAACzC,IAAI,CAAC0C,iBAAiB,EAAE;YAC7B,MAAMC,GAAG;YACT;YACA,QAAQ,KAAK,OAAOd,IAAI,GAAGe,MAAM,CAACC,UAAU,CAAChB,IAAI,CAAC,GAAGA,IAAI,CAACQ,MAAM;YAChE,IAAIM,GAAG,GAAG,IAAI,CAAC3C,IAAI,CAAC0C,iBAAiB,CAACI,SAAS,EAAE;cAC7C9C,IAAI,CAACyC,QAAQ,GAAG,KAAK;YACzB;UACJ;QACJ;QACA;QACA;QACA;QACA,IAAI;UACA,IAAIlD,qBAAqB,EAAE;YACvB;YACA,IAAI,CAACmB,EAAE,CAACqC,IAAI,CAAClB,IAAI,CAAC;UACtB,CAAC,MACI;YACD,IAAI,CAACnB,EAAE,CAACqC,IAAI,CAAClB,IAAI,EAAE7B,IAAI,CAAC;UAC5B;QACJ,CAAC,CACD,OAAO+B,CAAC,EAAE,CACV;QACA,IAAIQ,UAAU,EAAE;UACZ;UACA;UACAjD,QAAQ,CAAC,MAAM;YACX,IAAI,CAAC6C,QAAQ,GAAG,IAAI;YACpB,IAAI,CAACvB,YAAY,CAAC,OAAO,CAAC;UAC9B,CAAC,EAAE,IAAI,CAACoC,YAAY,CAAC;QACzB;MACJ,CAAC,CAAC;IACN;EACJ;EACAC,OAAOA,CAAA,EAAG;IACN,IAAI,OAAO,IAAI,CAACvC,EAAE,KAAK,WAAW,EAAE;MAChC,IAAI,CAACA,EAAE,CAACwC,KAAK,CAAC,CAAC;MACf,IAAI,CAACxC,EAAE,GAAG,IAAI;IAClB;EACJ;EACA;AACJ;AACA;AACA;AACA;EACIJ,GAAGA,CAAA,EAAG;IACF,MAAM6C,MAAM,GAAG,IAAI,CAACnD,IAAI,CAACoD,MAAM,GAAG,KAAK,GAAG,IAAI;IAC9C,MAAMC,KAAK,GAAG,IAAI,CAACA,KAAK,IAAI,CAAC,CAAC;IAC9B;IACA,IAAI,IAAI,CAACrD,IAAI,CAACsD,iBAAiB,EAAE;MAC7BD,KAAK,CAAC,IAAI,CAACrD,IAAI,CAACuD,cAAc,CAAC,GAAGnE,KAAK,CAAC,CAAC;IAC7C;IACA;IACA,IAAI,CAAC,IAAI,CAACa,cAAc,EAAE;MACtBoD,KAAK,CAACG,GAAG,GAAG,CAAC;IACjB;IACA,OAAO,IAAI,CAACC,SAAS,CAACN,MAAM,EAAEE,KAAK,CAAC;EACxC;EACA;AACJ;AACA;AACA;AACA;AACA;EACIhD,KAAKA,CAAA,EAAG;IACJ,OAAO,CAAC,CAACb,SAAS;EACtB;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}