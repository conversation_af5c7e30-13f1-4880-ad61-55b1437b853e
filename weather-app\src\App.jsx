import { useState } from 'react'
import WeatherCard from './components/WeatherCard'
import SearchForm from './components/SearchForm'

function App() {
  const [weatherData, setWeatherData] = useState(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')

  const API_KEY = 'YOUR_ACTUAL_API_KEY_HERE' // Replace with your OpenWeatherMap API key

  const fetchWeather = async (city) => {
    if (!city.trim()) return

    setLoading(true)
    setError('')
    
    try {
      const response = await fetch(
        `https://api.openweathermap.org/data/2.5/weather?q=${city}&appid=${API_KEY}&units=metric`
      )
      
      if (!response.ok) {
        throw new Error('City not found')
      }
      
      const data = await response.json()
      setWeatherData(data)
    } catch (err) {
      setError(err.message)
      setWeatherData(null)
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="weather-app">
      <h1 style={{ marginBottom: '2rem', color: '#2d3436' }}>Weather App</h1>
      
      <SearchForm onSearch={fetchWeather} loading={loading} />
      
      {loading && <div className="loading">Loading weather data...</div>}
      
      {error && <div className="error">{error}</div>}
      
      {weatherData && <WeatherCard data={weatherData} />}
      
      {!weatherData && !loading && !error && (
        <div style={{ marginTop: '2rem', color: '#636e72' }}>
          Enter a city name to get weather information
        </div>
      )}
    </div>
  )
}

export default App
