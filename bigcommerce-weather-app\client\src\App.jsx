import { useState, useEffect } from 'react'
import WeatherCard from './components/WeatherCard'
import CitySearch from './components/CitySearch'
import axios from 'axios'

function App() {
  const [weatherData, setWeatherData] = useState(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')
  const [locationLoading, setLocationLoading] = useState(false)
  const [ipLocationLoading, setIpLocationLoading] = useState(false)
  const [currentLocation, setCurrentLocation] = useState(null)
  const [locationPermission, setLocationPermission] = useState('prompt') // 'granted', 'denied', 'prompt'
  const [appVersion, setAppVersion] = useState('v2.1') // For easy version updates

  const fetchWeather = async (city) => {
    if (!city.trim()) return

    setLoading(true)
    setError('')

    try {
      // Use the backend API proxy instead of direct API call
      const response = await axios.get(`/api/weather/${encodeURIComponent(city)}`)
      setWeatherData(response.data)
    } catch (err) {
      setError(err.response?.data?.error || 'Failed to fetch weather data')
      setWeatherData(null)
    } finally {
      setLoading(false)
    }
  }

  // Check for permission status
  const checkLocationPermission = async () => {
    try {
      // This only works in secure contexts (HTTPS) and modern browsers
      if (navigator.permissions && navigator.permissions.query) {
        const result = await navigator.permissions.query({ name: 'geolocation' })
        setLocationPermission(result.state)

        // Listen for permission changes
        result.addEventListener('change', () => {
          setLocationPermission(result.state)
        })
      }
    } catch (err) {
      console.error('Permission check error:', err)
    }
  }

  const getUserLocation = () => {
    if (!navigator.geolocation) {
      setError('Geolocation is not supported by your browser')
      return
    }

    setLocationLoading(true)
    setError('')

    // Set a timeout for geolocation request
    const timeoutId = setTimeout(() => {
      if (locationLoading) {
        setError('Location request timed out. Please try searching manually.')
        setLocationLoading(false)
      }
    }, 15000) // 15 second timeout for VPN users

    navigator.geolocation.getCurrentPosition(
      async (position) => {
        clearTimeout(timeoutId)
        try {
          const { latitude, longitude, accuracy } = position.coords
          setCurrentLocation({ latitude, longitude, accuracy })
          setLocationPermission('granted')

          // Show accuracy warning for VPN users
          if (accuracy > 10000) { // More than 10km accuracy
            setError('⚠️ Location detected but may be inaccurate (VPN detected). Consider searching manually.')
          }

          // Fetch weather by coordinates
          const response = await axios.get(`/api/weather/coordinates?lat=${latitude}&lon=${longitude}`)
          setWeatherData(response.data)
          setLocationLoading(false)
        } catch (err) {
          console.error('Weather fetch error:', err)
          setError('Failed to fetch weather for your location. Try searching manually.')
          setLocationLoading(false)
        }
      },
      (error) => {
        clearTimeout(timeoutId)
        if (error.code === error.PERMISSION_DENIED) {
          setLocationPermission('denied')
        }

        let errorMessage = getLocationErrorMessage(error)
        if (error.code === error.POSITION_UNAVAILABLE) {
          errorMessage += ' This often happens with VPN. Try searching manually.'
        }

        setError(`Location error: ${errorMessage}`)
        setLocationLoading(false)
      },
      {
        enableHighAccuracy: true,  // Try high accuracy first
        timeout: 12000,           // 12 second timeout
        maximumAge: 300000        // Accept positions up to 5 minutes old
      }
    )
  }

  const getLocationErrorMessage = (error) => {
    switch(error.code) {
      case error.PERMISSION_DENIED:
        return 'Location permission denied. Please enable location access.'
      case error.POSITION_UNAVAILABLE:
        return 'Location information is unavailable.'
      case error.TIMEOUT:
        return 'Location request timed out.'
      default:
        return 'An unknown error occurred.'
    }
  }

  // IP-based location for VPN users
  const getIpLocation = async () => {
    setIpLocationLoading(true)
    setError('')

    try {
      const response = await axios.get('/api/weather/ip-location')
      setWeatherData(response.data)

      if (response.data.locationSource === 'ip') {
        setError(`📍 Using IP-based location: ${response.data.detectedCity}, ${response.data.detectedCountry}. May not be accurate if using VPN.`)
      }

      setIpLocationLoading(false)
    } catch (err) {
      console.error('IP location error:', err)
      setError('Failed to get location from IP. Please search manually.')
      setIpLocationLoading(false)
    }
  }

  // Check permissions and get location on component mount
  useEffect(() => {
    checkLocationPermission()

    // Only auto-request location if not in private browsing
    // Private browsing often blocks geolocation
    const isPrivateBrowsing = () => {
      try {
        return !window.indexedDB || !window.localStorage
      } catch (e) {
        return true
      }
    }

    // Don't auto-request location in private browsing to avoid errors
    if (!isPrivateBrowsing()) {
      // Small delay to let permission check complete
      setTimeout(() => {
        if (locationPermission !== 'denied') {
          getUserLocation()
        }
      }, 500)
    }
  }, [])

  return (
    <div className="weather-app">
      <div className="app-header">
        <h1 className="app-title">Weather Dashboard</h1>
        <p className="app-subtitle">Get real-time weather information for any city</p>
        <div className="app-version">{appVersion} - Enhanced with Location & Suggestions</div>
      </div>

      <div className="location-controls">
        <div className="location-buttons">
          <button
            onClick={getUserLocation}
            disabled={locationLoading || loading || ipLocationLoading}
            className="location-btn"
          >
            {locationLoading ? '📍 Getting Location...' : '📍 Use My Location'}
          </button>

          <button
            onClick={getIpLocation}
            disabled={locationLoading || loading || ipLocationLoading}
            className="location-btn ip-btn"
          >
            {ipLocationLoading ? '🌐 Getting IP Location...' : '🌐 Use IP Location'}
          </button>
        </div>

        {locationPermission === 'denied' && (
          <div className="location-info error-text">
            ❌ Location access denied. Try "Use IP Location" button instead.
          </div>
        )}

        {currentLocation && locationPermission === 'granted' && (
          <div className="location-info success-text">
            ✅ GPS Location: {currentLocation.latitude.toFixed(2)}, {currentLocation.longitude.toFixed(2)}
            {currentLocation.accuracy && ` (±${Math.round(currentLocation.accuracy)}m)`}
          </div>
        )}

        {!navigator.geolocation && (
          <div className="location-info error-text">
            ❌ Geolocation not supported. Use "IP Location" instead.
          </div>
        )}
      </div>

      <CitySearch onSearch={fetchWeather} loading={loading} />

      {(loading || locationLoading) && (
        <div className="loading">
          {locationLoading ? 'Getting your location...' : 'Loading weather data...'}
        </div>
      )}

      {error && <div className="error">{error}</div>}

      {weatherData && <WeatherCard data={weatherData} />}

      {!weatherData && !loading && !locationLoading && !error && (
        <div className="empty-state">
          <p>🌤️ Welcome to Weather Dashboard!</p>
          <p>Click "Use My Location" for instant weather or search for any city.</p>
        </div>
      )}
    </div>
  )
}

export default App
