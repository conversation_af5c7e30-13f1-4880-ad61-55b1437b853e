import { useState, useEffect } from 'react'
import WeatherCard from './components/WeatherCard'
import SearchForm from './components/SearchForm'
import axios from 'axios'

function App() {
  const [weatherData, setWeatherData] = useState(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')
  const [locationLoading, setLocationLoading] = useState(false)
  const [currentLocation, setCurrentLocation] = useState(null)

  const fetchWeather = async (city) => {
    if (!city.trim()) return

    setLoading(true)
    setError('')

    try {
      // Use the backend API proxy instead of direct API call
      const response = await axios.get(`/api/weather/${encodeURIComponent(city)}`)
      setWeatherData(response.data)
    } catch (err) {
      setError(err.response?.data?.error || 'Failed to fetch weather data')
      setWeatherData(null)
    } finally {
      setLoading(false)
    }
  }

  const getUserLocation = () => {
    if (!navigator.geolocation) {
      setError('Geolocation is not supported by your browser')
      return
    }

    setLocationLoading(true)
    setError('')

    navigator.geolocation.getCurrentPosition(
      async (position) => {
        try {
          const { latitude, longitude } = position.coords
          setCurrentLocation({ latitude, longitude })

          // Fetch weather by coordinates
          const response = await axios.get(`/api/weather/coordinates?lat=${latitude}&lon=${longitude}`)
          setWeatherData(response.data)
          setLocationLoading(false)
        } catch (err) {
          setError('Failed to fetch weather for your location')
          setLocationLoading(false)
        }
      },
      (error) => {
        setError(`Location error: ${getLocationErrorMessage(error)}`)
        setLocationLoading(false)
      }
    )
  }

  const getLocationErrorMessage = (error) => {
    switch(error.code) {
      case error.PERMISSION_DENIED:
        return 'Location permission denied. Please enable location access.'
      case error.POSITION_UNAVAILABLE:
        return 'Location information is unavailable.'
      case error.TIMEOUT:
        return 'Location request timed out.'
      default:
        return 'An unknown error occurred.'
    }
  }

  // Get user location on component mount
  useEffect(() => {
    getUserLocation()
  }, [])

  return (
    <div className="weather-app">
      <div className="app-header">
        <h1 className="app-title">Weather Dashboard</h1>
        <p className="app-subtitle">Get real-time weather information for any city</p>
      </div>

      <div className="location-controls">
        <button
          onClick={getUserLocation}
          disabled={locationLoading || loading}
          className="location-btn"
        >
          {locationLoading ? '📍 Getting Location...' : '📍 Use My Location'}
        </button>
        {currentLocation && (
          <span className="location-info">
            📍 Location detected: {currentLocation.latitude.toFixed(2)}, {currentLocation.longitude.toFixed(2)}
          </span>
        )}
      </div>

      <SearchForm onSearch={fetchWeather} loading={loading} />

      {(loading || locationLoading) && (
        <div className="loading">
          {locationLoading ? 'Getting your location...' : 'Loading weather data...'}
        </div>
      )}

      {error && <div className="error">{error}</div>}

      {weatherData && <WeatherCard data={weatherData} />}

      {!weatherData && !loading && !locationLoading && !error && (
        <div className="empty-state">
          <p>🌤️ Welcome to Weather Dashboard!</p>
          <p>Click "Use My Location" for instant weather or search for any city.</p>
        </div>
      )}
    </div>
  )
}

export default App
