import { useState, useEffect } from 'react'
import WeatherCard from './components/WeatherCard'
import CitySearch from './components/CitySearch'
import axios from 'axios'

function App() {
  const [weatherData, setWeatherData] = useState(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')
  const [locationLoading, setLocationLoading] = useState(false)
  const [currentLocation, setCurrentLocation] = useState(null)
  const [locationPermission, setLocationPermission] = useState('prompt') // 'granted', 'denied', 'prompt'

  const fetchWeather = async (city) => {
    if (!city.trim()) return

    setLoading(true)
    setError('')

    try {
      // Use the backend API proxy instead of direct API call
      const response = await axios.get(`/api/weather/${encodeURIComponent(city)}`)
      setWeatherData(response.data)
    } catch (err) {
      setError(err.response?.data?.error || 'Failed to fetch weather data')
      setWeatherData(null)
    } finally {
      setLoading(false)
    }
  }

  // Check for permission status
  const checkLocationPermission = async () => {
    try {
      // This only works in secure contexts (HTTPS) and modern browsers
      if (navigator.permissions && navigator.permissions.query) {
        const result = await navigator.permissions.query({ name: 'geolocation' })
        setLocationPermission(result.state)

        // Listen for permission changes
        result.addEventListener('change', () => {
          setLocationPermission(result.state)
        })
      }
    } catch (err) {
      console.error('Permission check error:', err)
    }
  }

  const getUserLocation = () => {
    if (!navigator.geolocation) {
      setError('Geolocation is not supported by your browser')
      return
    }

    setLocationLoading(true)
    setError('')

    // Set a timeout for geolocation request
    const timeoutId = setTimeout(() => {
      if (locationLoading) {
        setError('Location request timed out. Please try again.')
        setLocationLoading(false)
      }
    }, 10000) // 10 second timeout

    navigator.geolocation.getCurrentPosition(
      async (position) => {
        clearTimeout(timeoutId)
        try {
          const { latitude, longitude } = position.coords
          setCurrentLocation({ latitude, longitude })
          setLocationPermission('granted')

          // Fetch weather by coordinates
          const response = await axios.get(`/api/weather/coordinates?lat=${latitude}&lon=${longitude}`)
          setWeatherData(response.data)
          setLocationLoading(false)
        } catch (err) {
          console.error('Weather fetch error:', err)
          setError('Failed to fetch weather for your location')
          setLocationLoading(false)
        }
      },
      (error) => {
        clearTimeout(timeoutId)
        if (error.code === error.PERMISSION_DENIED) {
          setLocationPermission('denied')
        }
        setError(`Location error: ${getLocationErrorMessage(error)}`)
        setLocationLoading(false)
      },
      {
        enableHighAccuracy: false, // Set to false for faster response
        timeout: 8000,            // 8 second timeout
        maximumAge: 60000         // Accept positions up to 1 minute old
      }
    )
  }

  const getLocationErrorMessage = (error) => {
    switch(error.code) {
      case error.PERMISSION_DENIED:
        return 'Location permission denied. Please enable location access.'
      case error.POSITION_UNAVAILABLE:
        return 'Location information is unavailable.'
      case error.TIMEOUT:
        return 'Location request timed out.'
      default:
        return 'An unknown error occurred.'
    }
  }

  // Check permissions and get location on component mount
  useEffect(() => {
    checkLocationPermission()

    // Only auto-request location if not in private browsing
    // Private browsing often blocks geolocation
    const isPrivateBrowsing = () => {
      try {
        return !window.indexedDB || !window.localStorage
      } catch (e) {
        return true
      }
    }

    // Don't auto-request location in private browsing to avoid errors
    if (!isPrivateBrowsing()) {
      // Small delay to let permission check complete
      setTimeout(() => {
        if (locationPermission !== 'denied') {
          getUserLocation()
        }
      }, 500)
    }
  }, [])

  return (
    <div className="weather-app">
      <div className="app-header">
        <h1 className="app-title">Weather Dashboard</h1>
        <p className="app-subtitle">Get real-time weather information for any city</p>
      </div>

      <div className="location-controls">
        <button
          onClick={getUserLocation}
          disabled={locationLoading || loading}
          className="location-btn"
        >
          {locationLoading ? '📍 Getting Location...' : '📍 Use My Location'}
        </button>

        {locationPermission === 'denied' && (
          <div className="location-info error-text">
            ❌ Location access denied. Please enable location in your browser settings.
          </div>
        )}

        {currentLocation && locationPermission === 'granted' && (
          <div className="location-info success-text">
            ✅ Location detected: {currentLocation.latitude.toFixed(2)}, {currentLocation.longitude.toFixed(2)}
          </div>
        )}

        {!navigator.geolocation && (
          <div className="location-info error-text">
            ❌ Geolocation not supported by your browser
          </div>
        )}
      </div>

      <CitySearch onSearch={fetchWeather} loading={loading} />

      {(loading || locationLoading) && (
        <div className="loading">
          {locationLoading ? 'Getting your location...' : 'Loading weather data...'}
        </div>
      )}

      {error && <div className="error">{error}</div>}

      {weatherData && <WeatherCard data={weatherData} />}

      {!weatherData && !loading && !locationLoading && !error && (
        <div className="empty-state">
          <p>🌤️ Welcome to Weather Dashboard!</p>
          <p>Click "Use My Location" for instant weather or search for any city.</p>
        </div>
      )}
    </div>
  )
}

export default App
