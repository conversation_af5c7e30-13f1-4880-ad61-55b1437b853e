{"version": 3, "file": "static/css/main.dd04e414.css", "mappings": "AAAA,EAGE,qBAAsB,CAFtB,QAAS,CACT,SAEF,CAEA,KAEE,kBAAmB,CACnB,UAAW,CAFX,uEAAgF,CAGhF,eACF,CAEA,KACE,gBACF,CAGA,iBAIE,eAAiB,CAFjB,YAAa,CACb,qBAAsB,CAFtB,YAIF,CAEA,kBAME,kBAAmB,CALnB,kBAAmB,CAMnB,8BAAqC,CALrC,UAAY,CAEZ,YAAa,CACb,6BAA8B,CAF9B,iBAKF,CAEA,qBACE,gBAAiB,CACjB,eACF,CAEA,iBAEE,kBAAmB,CADnB,YAAa,CAEb,QACF,CAEA,aAIE,eAAiB,CAFjB,wBAAyB,CACzB,iBAAkB,CAElB,UAAW,CAJX,aAKF,CAEA,oBAGE,cAAe,CADf,gBAAiB,CADjB,iBAGF,CAEA,OAWE,kBAAmB,CAPnB,kBAAmB,CAEnB,iBAAkB,CADlB,UAAY,CAKZ,YAAa,CADb,eAAiB,CADjB,WAAY,CAIZ,sBAAuB,CAXvB,iBAAkB,CAElB,UAAW,CADX,QAAS,CAKT,UAMF,CAEA,mBAEE,YAAa,CADb,QAAO,CAEP,eACF,CAEA,SAEE,kBAAmB,CACnB,8BAA+B,CAC/B,YAAa,CACb,qBAAsB,CAJtB,WAKF,CAEA,iBAGE,+BAAgC,CAFhC,QAAO,CACP,YAEF,CAEA,oBAEE,aAAc,CACd,cAAe,CAFf,kBAGF,CAQA,+BAJE,YAAa,CADb,QAAO,CAEP,qBAWF,CARA,iBAIE,kBAAmB,CAEnB,aAAc,CADd,sBAAuB,CAEvB,iBACF,CAGA,mBACE,gBAAiB,CACjB,eACF,CAEA,mBAEE,+BAAgC,CAChC,cAAe,CAFf,YAAa,CAGb,8BACF,CAEA,yBACE,kBACF,CAEA,4BACE,kBAAmB,CACnB,UACF,CAEA,qBAGE,kBAAmB,CAFnB,YAAa,CACb,6BAA8B,CAE9B,mBACF,CAEA,eAEE,kBAAmB,CADnB,YAAa,CAEb,SACF,CAEA,eACE,eACF,CAEA,kBAGE,iBAAkB,CADlB,UAAW,CADX,SAGF,CAEA,WACE,eAAiB,CACjB,UACF,CAEA,sBACE,mBACF,CAEA,cACE,eAAiB,CAEjB,oBAAsB,CADtB,UAEF,CAEA,eACE,eAAiB,CACjB,UACF,CAEA,mBAGE,kBAAmB,CAFnB,YAAa,CACb,6BAEF,CAEA,gBAEE,gBAAiC,CAEjC,kBACF,CAEA,4BANE,eAAiB,CAEjB,oBAYF,CARA,YACE,kBAAmB,CAEnB,WAAY,CAEZ,iBAAkB,CAHlB,UAAY,CAKZ,cACF,CAEA,kBACE,kBACF,CAGA,aAEE,YAAa,CADb,QAAO,CAEP,qBAAsB,CACtB,WACF,CAEA,aAME,kBAAmB,CALnB,kBAAmB,CACnB,UAAY,CAEZ,YAAa,CACb,6BAA8B,CAF9B,YAIF,CAEA,sBACE,oBACF,CAEA,qBACE,eAAiB,CACjB,UACF,CAEA,qBACE,gBACF,CAEA,QAEE,kBAAmB,CACnB,eAAiB,CAFjB,oBAAuB,CAGvB,wBACF,CAEA,eACE,kBACF,CAEA,gBACE,kBACF,CAEA,eACE,kBACF,CAEA,aACE,aAAc,CACd,eAAiB,CACjB,iBAAmB,CACnB,UACF,CAEA,oBAIE,kBAAmB,CAHnB,QAAO,CAEP,eAAgB,CADhB,YAGF,CAEA,SAEE,YAAa,CADb,kBAEF,CAEA,eACE,wBACF,CAEA,kBACE,0BACF,CAEA,iBAEE,eAAiB,CAEjB,iBAAkB,CAClB,8BAAqC,CAJrC,aAAc,CAEd,cAGF,CAEA,gCACE,kBAAmB,CACnB,UACF,CAEA,cACE,mBACF,CAEA,cACE,YAAa,CAEb,eAAiB,CADjB,6BAA8B,CAE9B,UACF,CAEA,cAEE,eAAiB,CACjB,4BAA6B,CAC7B,YAAa,CACb,SAAW,CAJX,YAKF,CAEA,eAGE,wBAAyB,CACzB,iBAAkB,CAHlB,QAAO,CAIP,YAAa,CAHb,cAIF,CAEA,qBACE,oBACF,CAEA,aACE,kBAAmB,CAEnB,WAAY,CAEZ,iBAAkB,CAHlB,UAAY,CAIZ,cAAe,CACf,eAAgB,CAHhB,qBAIF,CAEA,mBACE,kBACF,CAGA,kBAEE,kBAAmB,CADnB,YAAa,CAEb,SAAW,CACX,kBAAmB,CACnB,UACF,CAEA,aACE,YAAa,CACb,OACF,CAEA,kBAKE,0CAA2C,CAF3C,kBAAmB,CACnB,iBAAkB,CAFlB,UAAW,CADX,SAKF,CAEA,8BAAiC,qBAAyB,CAC1D,+BAAiC,qBAAyB,CAE1D,kBACE,UAAgB,kBAAqB,CACrC,IAAM,kBAAqB,CAC7B,CAEA,aACE,eAAiB,CACjB,iBACF,CAGA,oBACE,gBAAiB,CACjB,eACF,CAEA,qBAGE,aAAc,CADd,YAAa,CADb,iBAGF,CAEA,mBAEE,kBAAmB,CAGnB,+BAAgC,CAChC,cAAe,CALf,YAAa,CAEb,UAAY,CACZ,cAAgB,CAIhB,iBAAkB,CADlB,8BAEF,CAEA,yBACE,kBACF,CAEA,0BACE,kBAAmB,CACnB,eACF,CAEA,mBACE,gBACF,CAEA,sBACE,QACF,CAEA,sBACE,eAAiB,CACjB,oBACF,CAEA,mBACE,eAAiB,CACjB,UACF,CAEA,YAGE,kBAAmB,CACnB,iBAAkB,CAFlB,UAAW,CADX,SAIF,CAGA,aAEE,WAAY,CADZ,cAAe,CAEf,UAAW,CACX,YACF,CAEA,aAIE,kBAAmB,CAEnB,WAAY,CAHZ,iBAAkB,CAMlB,+BAAuC,CAJvC,UAAY,CAGZ,cAAe,CADf,gBAAiB,CALjB,WAAY,CAQZ,uBAAyB,CATzB,UAUF,CAEA,mBACE,kBAAmB,CACnB,qBACF,CAEA,kBACE,kBACF,CAEA,oBAME,eAAiB,CACjB,iBAAkB,CALlB,WAAY,CAMZ,+BAAuC,CACvC,YAAa,CACb,qBAAsB,CALtB,YAAa,CAMb,eAAgB,CAVhB,iBAAkB,CAElB,OAAQ,CACR,WAQF,CAEA,iBACE,cACF,CAEA,8BACE,oBAAqB,CACrB,iBACF,CAEA,oBACE,aAAc,CACd,mBACF,CAEA,mBACE,aAAc,CACd,eACF,CAEA,YACE,kBACF,CAEA,kBAIE,aAAc,CAHd,aAAc,CAEd,eAAgB,CADhB,mBAGF,CAEA,kBAGE,wBAAyB,CACzB,iBAAkB,CAClB,YAAa,CAHb,cAAgB,CADhB,UAKF,CAEA,wBACE,oBACF,CAEA,gBAEE,kBAAmB,CAEnB,WAAY,CAEZ,iBAAkB,CAHlB,UAAY,CAKZ,cAAe,CADf,eAAgB,CAFhB,cAAgB,CAIhB,8BAAgC,CARhC,UASF,CAEA,sBACE,kBACF,CAGA,yBACE,mBACE,qBACF,CAEA,SAEE,YAAa,CADb,UAEF,CAEA,oBAEE,YAAa,CADb,WAEF,CACF", "sources": ["App.css"], "sourcesContent": ["* {\n  margin: 0;\n  padding: 0;\n  box-sizing: border-box;\n}\n\nbody {\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;\n  background: #f5f5f5;\n  color: #333;\n  line-height: 1.6;\n}\n\n.App {\n  min-height: 100vh;\n}\n\n/* Admin Dashboard */\n.admin-dashboard {\n  height: 100vh;\n  display: flex;\n  flex-direction: column;\n  background: white;\n}\n\n.dashboard-header {\n  background: #2c3e50;\n  color: white;\n  padding: 1rem 2rem;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  box-shadow: 0 2px 4px rgba(0,0,0,0.1);\n}\n\n.dashboard-header h1 {\n  font-size: 1.5rem;\n  font-weight: 600;\n}\n\n.header-controls {\n  display: flex;\n  align-items: center;\n  gap: 1rem;\n}\n\n.agent-input {\n  padding: 0.5rem;\n  border: 1px solid #34495e;\n  border-radius: 4px;\n  background: white;\n  color: #333;\n}\n\n.notification-badge {\n  position: relative;\n  font-size: 1.2rem;\n  cursor: pointer;\n}\n\n.badge {\n  position: absolute;\n  top: -8px;\n  right: -8px;\n  background: #e74c3c;\n  color: white;\n  border-radius: 50%;\n  width: 20px;\n  height: 20px;\n  font-size: 0.7rem;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.dashboard-content {\n  flex: 1;\n  display: flex;\n  overflow: hidden;\n}\n\n.sidebar {\n  width: 350px;\n  background: #ecf0f1;\n  border-right: 1px solid #bdc3c7;\n  display: flex;\n  flex-direction: column;\n}\n\n.sidebar-section {\n  flex: 1;\n  padding: 1rem;\n  border-bottom: 1px solid #bdc3c7;\n}\n\n.sidebar-section h3 {\n  margin-bottom: 1rem;\n  color: #2c3e50;\n  font-size: 1rem;\n}\n\n.main-content {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n}\n\n.no-conversation {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  color: #7f8c8d;\n  text-align: center;\n}\n\n/* Conversation List */\n.conversation-list {\n  max-height: 400px;\n  overflow-y: auto;\n}\n\n.conversation-item {\n  padding: 1rem;\n  border-bottom: 1px solid #d5dbdb;\n  cursor: pointer;\n  transition: background 0.2s ease;\n}\n\n.conversation-item:hover {\n  background: #d5dbdb;\n}\n\n.conversation-item.selected {\n  background: #3498db;\n  color: white;\n}\n\n.conversation-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 0.5rem;\n}\n\n.customer-info {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n}\n\n.customer-name {\n  font-weight: 600;\n}\n\n.status-indicator {\n  width: 8px;\n  height: 8px;\n  border-radius: 50%;\n}\n\n.timestamp {\n  font-size: 0.8rem;\n  opacity: 0.7;\n}\n\n.conversation-preview {\n  margin-bottom: 0.5rem;\n}\n\n.last-message {\n  font-size: 0.9rem;\n  opacity: 0.8;\n  margin-bottom: 0.25rem;\n}\n\n.message-count {\n  font-size: 0.8rem;\n  opacity: 0.6;\n}\n\n.conversation-meta {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.assigned-agent {\n  font-size: 0.8rem;\n  background: rgba(255,255,255,0.2);\n  padding: 0.25rem 0.5rem;\n  border-radius: 12px;\n}\n\n.assign-btn {\n  background: #27ae60;\n  color: white;\n  border: none;\n  padding: 0.25rem 0.5rem;\n  border-radius: 4px;\n  font-size: 0.8rem;\n  cursor: pointer;\n}\n\n.assign-btn:hover {\n  background: #229954;\n}\n\n/* Chat Window */\n.chat-window {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  height: 100%;\n}\n\n.chat-header {\n  background: #34495e;\n  color: white;\n  padding: 1rem;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.conversation-info h3 {\n  margin-bottom: 0.25rem;\n}\n\n.conversation-info p {\n  font-size: 0.9rem;\n  opacity: 0.8;\n}\n\n.conversation-status {\n  text-align: right;\n}\n\n.status {\n  padding: 0.25rem 0.5rem;\n  border-radius: 12px;\n  font-size: 0.8rem;\n  text-transform: uppercase;\n}\n\n.status.active {\n  background: #27ae60;\n}\n\n.status.waiting {\n  background: #f39c12;\n}\n\n.status.closed {\n  background: #95a5a6;\n}\n\n.assigned-to {\n  display: block;\n  font-size: 0.8rem;\n  margin-top: 0.25rem;\n  opacity: 0.8;\n}\n\n.messages-container {\n  flex: 1;\n  padding: 1rem;\n  overflow-y: auto;\n  background: #f8f9fa;\n}\n\n.message {\n  margin-bottom: 1rem;\n  display: flex;\n}\n\n.message.agent {\n  justify-content: flex-end;\n}\n\n.message.customer {\n  justify-content: flex-start;\n}\n\n.message-content {\n  max-width: 70%;\n  background: white;\n  padding: 0.75rem;\n  border-radius: 8px;\n  box-shadow: 0 1px 2px rgba(0,0,0,0.1);\n}\n\n.message.agent .message-content {\n  background: #3498db;\n  color: white;\n}\n\n.message-text {\n  margin-bottom: 0.5rem;\n}\n\n.message-meta {\n  display: flex;\n  justify-content: space-between;\n  font-size: 0.8rem;\n  opacity: 0.7;\n}\n\n.message-form {\n  padding: 1rem;\n  background: white;\n  border-top: 1px solid #e9ecef;\n  display: flex;\n  gap: 0.5rem;\n}\n\n.message-input {\n  flex: 1;\n  padding: 0.75rem;\n  border: 1px solid #ced4da;\n  border-radius: 4px;\n  outline: none;\n}\n\n.message-input:focus {\n  border-color: #3498db;\n}\n\n.send-button {\n  background: #3498db;\n  color: white;\n  border: none;\n  padding: 0.75rem 1.5rem;\n  border-radius: 4px;\n  cursor: pointer;\n  font-weight: 500;\n}\n\n.send-button:hover {\n  background: #2980b9;\n}\n\n/* Typing Indicator */\n.typing-indicator {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  margin-bottom: 1rem;\n  opacity: 0.7;\n}\n\n.typing-dots {\n  display: flex;\n  gap: 2px;\n}\n\n.typing-dots span {\n  width: 6px;\n  height: 6px;\n  background: #95a5a6;\n  border-radius: 50%;\n  animation: typing 1.4s infinite ease-in-out;\n}\n\n.typing-dots span:nth-child(1) { animation-delay: -0.32s; }\n.typing-dots span:nth-child(2) { animation-delay: -0.16s; }\n\n@keyframes typing {\n  0%, 80%, 100% { transform: scale(0); }\n  40% { transform: scale(1); }\n}\n\n.typing-text {\n  font-size: 0.9rem;\n  font-style: italic;\n}\n\n/* Notification Panel */\n.notification-panel {\n  max-height: 300px;\n  overflow-y: auto;\n}\n\n.empty-notifications {\n  text-align: center;\n  padding: 2rem;\n  color: #7f8c8d;\n}\n\n.notification-item {\n  display: flex;\n  align-items: center;\n  gap: 0.75rem;\n  padding: 0.75rem;\n  border-bottom: 1px solid #d5dbdb;\n  cursor: pointer;\n  transition: background 0.2s ease;\n  position: relative;\n}\n\n.notification-item:hover {\n  background: #d5dbdb;\n}\n\n.notification-item.unread {\n  background: #ebf3fd;\n  font-weight: 500;\n}\n\n.notification-icon {\n  font-size: 1.2rem;\n}\n\n.notification-content {\n  flex: 1;\n}\n\n.notification-message {\n  font-size: 0.9rem;\n  margin-bottom: 0.25rem;\n}\n\n.notification-time {\n  font-size: 0.8rem;\n  opacity: 0.6;\n}\n\n.unread-dot {\n  width: 8px;\n  height: 8px;\n  background: #3498db;\n  border-radius: 50%;\n}\n\n/* Chat Widget */\n.chat-widget {\n  position: fixed;\n  bottom: 20px;\n  right: 20px;\n  z-index: 1000;\n}\n\n.chat-toggle {\n  width: 60px;\n  height: 60px;\n  border-radius: 50%;\n  background: #3498db;\n  color: white;\n  border: none;\n  font-size: 1.5rem;\n  cursor: pointer;\n  box-shadow: 0 4px 12px rgba(0,0,0,0.15);\n  transition: all 0.3s ease;\n}\n\n.chat-toggle:hover {\n  background: #2980b9;\n  transform: scale(1.05);\n}\n\n.chat-toggle.open {\n  background: #e74c3c;\n}\n\n.chat-widget-window {\n  position: absolute;\n  bottom: 80px;\n  right: 0;\n  width: 350px;\n  height: 500px;\n  background: white;\n  border-radius: 8px;\n  box-shadow: 0 8px 24px rgba(0,0,0,0.15);\n  display: flex;\n  flex-direction: column;\n  overflow: hidden;\n}\n\n.chat-start-form {\n  padding: 1.5rem;\n}\n\n.chat-start-form .chat-header {\n  margin-bottom: 1.5rem;\n  text-align: center;\n}\n\n.chat-start-form h3 {\n  color: #2c3e50;\n  margin-bottom: 0.5rem;\n}\n\n.chat-start-form p {\n  color: #7f8c8d;\n  font-size: 0.9rem;\n}\n\n.form-group {\n  margin-bottom: 1rem;\n}\n\n.form-group label {\n  display: block;\n  margin-bottom: 0.5rem;\n  font-weight: 500;\n  color: #2c3e50;\n}\n\n.form-group input {\n  width: 100%;\n  padding: 0.75rem;\n  border: 1px solid #ced4da;\n  border-radius: 4px;\n  outline: none;\n}\n\n.form-group input:focus {\n  border-color: #3498db;\n}\n\n.start-chat-btn {\n  width: 100%;\n  background: #3498db;\n  color: white;\n  border: none;\n  padding: 0.75rem;\n  border-radius: 4px;\n  font-weight: 500;\n  cursor: pointer;\n  transition: background 0.2s ease;\n}\n\n.start-chat-btn:hover {\n  background: #2980b9;\n}\n\n/* Responsive */\n@media (max-width: 768px) {\n  .dashboard-content {\n    flex-direction: column;\n  }\n  \n  .sidebar {\n    width: 100%;\n    height: 300px;\n  }\n  \n  .chat-widget-window {\n    width: 300px;\n    height: 400px;\n  }\n}\n"], "names": [], "sourceRoot": ""}