import React, { useState, useEffect } from 'react';
import axios from 'axios';
import ChatWindow from './ChatWindow';

const ChatWidget = ({ socket }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [conversation, setConversation] = useState(null);
  const [customerInfo, setCustomerInfo] = useState({
    name: '',
    email: ''
  });
  const [isStarted, setIsStarted] = useState(false);

  const startConversation = async () => {
    if (!customerInfo.name.trim()) {
      alert('Please enter your name');
      return;
    }

    try {
      const response = await axios.post('/api/conversations', {
        customerName: customerInfo.name,
        customerEmail: customerInfo.email,
        storeContext: window.location.hostname
      });
      
      setConversation(response.data);
      setIsStarted(true);
    } catch (error) {
      console.error('Failed to start conversation:', error);
      alert('Failed to start chat. Please try again.');
    }
  };

  const toggleWidget = () => {
    setIsOpen(!isOpen);
  };

  return (
    <div className="chat-widget">
      {/* Chat Button */}
      <button 
        className={`chat-toggle ${isOpen ? 'open' : ''}`}
        onClick={toggleWidget}
      >
        {isOpen ? '✕' : '💬'}
      </button>

      {/* Chat Window */}
      {isOpen && (
        <div className="chat-widget-window">
          {!isStarted ? (
            <div className="chat-start-form">
              <div className="chat-header">
                <h3>Start a Chat</h3>
                <p>We're here to help! Please provide your details to begin.</p>
              </div>
              
              <div className="form-group">
                <label>Name *</label>
                <input
                  type="text"
                  value={customerInfo.name}
                  onChange={(e) => setCustomerInfo(prev => ({
                    ...prev,
                    name: e.target.value
                  }))}
                  placeholder="Enter your name"
                  required
                />
              </div>
              
              <div className="form-group">
                <label>Email (optional)</label>
                <input
                  type="email"
                  value={customerInfo.email}
                  onChange={(e) => setCustomerInfo(prev => ({
                    ...prev,
                    email: e.target.value
                  }))}
                  placeholder="Enter your email"
                />
              </div>
              
              <button 
                className="start-chat-btn"
                onClick={startConversation}
              >
                Start Chat
              </button>
            </div>
          ) : (
            <ChatWindow
              conversation={conversation}
              socket={socket}
              agentName={customerInfo.name}
              isAdmin={false}
            />
          )}
        </div>
      )}
    </div>
  );
};

export default ChatWidget;
